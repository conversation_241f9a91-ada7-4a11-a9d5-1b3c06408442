import { defineConfig } from 'vite'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  server: {
    host: true,
    port: 3000,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
          rollupOptions: {
                input: {
          main: resolve(fileURLToPath(new URL('.', import.meta.url)), 'index.html'),
          'boss-animation': resolve(fileURLToPath(new URL('.', import.meta.url)), 'pages/boss-animation.html'),
          'bear-animation': resolve(fileURLToPath(new URL('.', import.meta.url)), 'pages/bear-animation.html'),
        'tree-model': resolve(fileURLToPath(new URL('.', import.meta.url)), 'pages/tree-model.html'),
        'tree02-model': resolve(fileURLToPath(new URL('.', import.meta.url)), 'pages/tree02-model.html'),
          // 未来可以添加更多页面
        // 'scene-management': resolve(__dirname, 'pages/scene-management.html'),
        // 'particle-effects': resolve(__dirname, 'pages/particle-effects.html'),
        // 'physics-engine': resolve(__dirname, 'pages/physics-engine.html'),
        // 'ui-system': resolve(__dirname, 'pages/ui-system.html'),
        // 'audio-system': resolve(__dirname, 'pages/audio-system.html'),
      }
    }
  },
}) 