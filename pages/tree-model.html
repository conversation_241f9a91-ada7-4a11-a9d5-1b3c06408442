<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tree Model - 树模型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            overflow: hidden;
            height: 100vh;
        }

        #container {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 加载动画 */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            font-size: 18px;
            z-index: 1000;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 返回按钮 */
        #back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            z-index: 100;
        }

        #back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* 控制面板 */
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            min-width: 280px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 100;
        }

        #controls h3 {
            margin-bottom: 15px;
            color: #4CAF50;
            text-align: center;
        }

        .control-group {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-group:last-child {
            border-bottom: none;
        }

        .control-group h4 {
            margin-bottom: 10px;
            color: #FFD700;
            font-size: 14px;
        }

        .button-row {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        button.primary {
            background: #4CAF50;
            color: white;
        }

        button.primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        button.secondary {
            background: #2196F3;
            color: white;
        }

        button.secondary:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        button.warning {
            background: #FF9800;
            color: white;
        }

        button.warning:hover {
            background: #F57C00;
            transform: translateY(-1px);
        }

        .slider-control {
            margin-bottom: 15px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #ccc;
        }

        .slider-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input[type="range"] {
            flex: 1;
            height: 4px;
            border-radius: 2px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
        }

        .slider-value {
            min-width: 40px;
            text-align: right;
            font-size: 12px;
            color: #FFD700;
            font-weight: bold;
        }

        /* 状态显示 */
        .status-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            font-size: 11px;
            line-height: 1.4;
        }

        .status-info div {
            margin-bottom: 3px;
        }

        .status-info .label {
            color: #ccc;
        }

        .status-info .value {
            color: #FFD700;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls {
                min-width: 250px;
                padding: 15px;
            }

            button {
                font-size: 11px;
                padding: 6px 10px;
            }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Tree Model...</div>
        </div>

        <div id="controls">
            <h3>🌳 树模型控制</h3>

            <div class="control-group">
                <h4>基础控制</h4>
                <div class="button-row">
                    <button id="resetBtn" class="primary">重置位置</button>
                </div>
            </div>

            <div class="control-group">
                <h4>树状态控制</h4>
                <div class="button-row">
                    <button id="staticBtn" class="secondary">🟢 静态</button>
                    <button id="swayingBtn" class="secondary">🌿 摆动</button>
                    <button id="windyBtn" class="warning">💨 强风</button>
                </div>
            </div>

            <div class="control-group">
                <h4>位置调节</h4>
                <div class="slider-control">
                    <label>缩放大小</label>
                    <div class="slider-row">
                        <input type="range" id="scaleSlider" min="0.01" max="0.1" step="0.001" value="0.02">
                        <span class="slider-value" id="scaleValue">0.02</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>X轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posXSlider" min="-10" max="10" step="0.1" value="0">
                        <span class="slider-value" id="posXValue">0.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Y轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posYSlider" min="-5" max="5" step="0.1" value="0">
                        <span class="slider-value" id="posYValue">0.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Z轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posZSlider" min="-10" max="10" step="0.1" value="0">
                        <span class="slider-value" id="posZValue">0.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Y轴旋转</label>
                    <div class="slider-row">
                        <input type="range" id="rotYSlider" min="0" max="360" step="1" value="0">
                        <span class="slider-value" id="rotYValue">0°</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>摆动参数</h4>
                <div class="slider-control">
                    <label>摆动强度</label>
                    <div class="slider-row">
                        <input type="range" id="swayIntensitySlider" min="0" max="2" step="0.1" value="0.5">
                        <span class="slider-value" id="swayIntensityValue">0.5</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>摆动速度</label>
                    <div class="slider-row">
                        <input type="range" id="swaySpeedSlider" min="0.1" max="3" step="0.1" value="1.0">
                        <span class="slider-value" id="swaySpeedValue">1.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>风力强度</label>
                    <div class="slider-row">
                        <input type="range" id="windStrengthSlider" min="0" max="3" step="0.1" value="1.0">
                        <span class="slider-value" id="windStrengthValue">1.0</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>状态信息</h4>
                <div class="status-info" id="statusInfo">
                    <div><span class="label">状态:</span> <span class="value" id="currentState">加载中...</span></div>
                    <div><span class="label">位置:</span> <span class="value" id="currentPosition">-</span></div>
                    <div><span class="label">缩放:</span> <span class="value" id="currentScale">-</span></div>
                </div>
            </div>
        </div>

        <canvas id="app"></canvas>
    </div>

    <!-- 返回按钮 -->
    <a href="/" id="back-button">← 返回主页</a>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { TreeModel } from '/src/entities/TreeModel.js';
        import { TreeState } from '/src/entities/TreeModel.js';

        class TreeApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.tree = null;
                this.animationId = null;

                this.init();
            }

            async init() {
                try {
                    this.setupScene();
                    this.setupCamera();
                    this.setupRenderer();
                    this.setupLighting();
                    this.setupControls();
                    this.addGround();

                    await this.createTree();
                    this.setupEventListeners();
                    this.animate();

                    // 隐藏加载界面
                    document.getElementById('loading').style.display = 'none';
                } catch (error) {
                    console.error('初始化失败:', error);
                    document.getElementById('loading').style.display = 'none';
                }
            }

            setupScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);
                this.scene.fog = new THREE.Fog(0x87CEEB, 10, 50);
            }

            setupCamera() {
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 3, 5);
            }

            setupRenderer() {
                const canvas = document.getElementById('app');
                this.renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setPixelRatio(window.devicePixelRatio);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;
            }

            setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);

                // 方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);
            }

            setupControls() {
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.screenSpacePanning = false;
                this.controls.minDistance = 2;
                this.controls.maxDistance = 20;
                this.controls.maxPolarAngle = Math.PI / 2;
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async createTree() {
                this.tree = TreeModel.create(this.scene, 'ForestTree');

                // 设置事件回调
                this.tree.setEventCallbacks({
                    onLoadComplete: () => {
                        console.log('🌳 Tree model loaded successfully');
                        this.updateStatus();

                        // 启用模型阴影
                        const model = this.tree.getModel();
                        if (model) {
                            model.traverse((child) => {
                                if (child instanceof THREE.Mesh) {
                                    child.castShadow = true;
                                    child.receiveShadow = true;
                                }
                            });
                        }
                    },
                    onStateChange: (state) => {
                        console.log(`Tree state changed to: ${state}`);
                        this.updateStatus();
                    }
                });

                await this.tree.initialize();
            }

            setupEventListeners() {
                // 窗口大小调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });

                // 重置按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetTree();
                });

                // 状态控制按钮
                document.getElementById('staticBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(TreeState.STATIC);
                    }
                });

                document.getElementById('swayingBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(TreeState.SWAYING);
                    }
                });

                document.getElementById('windyBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(TreeState.WINDY);
                    }
                });

                // 滑块事件
                this.setupSliderEvents();
            }

            setupSliderEvents() {
                // 缩放滑块
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    scaleValue.textContent = value.toFixed(3);
                    if (this.tree) {
                        this.tree.setScale(value);
                        this.updateStatus();
                    }
                });

                // 位置滑块
                const posXSlider = document.getElementById('posXSlider');
                const posXValue = document.getElementById('posXValue');
                posXSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posXValue.textContent = value.toFixed(1);
                    this.updateTreePosition();
                });

                const posYSlider = document.getElementById('posYSlider');
                const posYValue = document.getElementById('posYValue');
                posYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posYValue.textContent = value.toFixed(1);
                    this.updateTreePosition();
                });

                const posZSlider = document.getElementById('posZSlider');
                const posZValue = document.getElementById('posZValue');
                posZSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posZValue.textContent = value.toFixed(1);
                    this.updateTreePosition();
                });

                // 旋转滑块
                const rotYSlider = document.getElementById('rotYSlider');
                const rotYValue = document.getElementById('rotYValue');
                rotYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    rotYValue.textContent = value + '°';
                    if (this.tree) {
                        this.tree.setRotation(new THREE.Euler(0, THREE.MathUtils.degToRad(value), 0));
                        this.updateStatus();
                    }
                });

                // 摆动参数滑块
                const swayIntensitySlider = document.getElementById('swayIntensitySlider');
                const swayIntensityValue = document.getElementById('swayIntensityValue');
                swayIntensitySlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    swayIntensityValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setSwayIntensity(value);
                    }
                });

                const swaySpeedSlider = document.getElementById('swaySpeedSlider');
                const swaySpeedValue = document.getElementById('swaySpeedValue');
                swaySpeedSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    swaySpeedValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setSwaySpeed(value);
                    }
                });

                const windStrengthSlider = document.getElementById('windStrengthSlider');
                const windStrengthValue = document.getElementById('windStrengthValue');
                windStrengthSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    windStrengthValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setWindStrength(value);
                    }
                });
            }

            updateTreePosition() {
                if (!this.tree) return;

                const x = parseFloat(document.getElementById('posXSlider').value);
                const y = parseFloat(document.getElementById('posYSlider').value);
                const z = parseFloat(document.getElementById('posZSlider').value);

                this.tree.setPosition(new THREE.Vector3(x, y, z));
                this.updateStatus();
            }

            resetTree() {
                if (!this.tree) return;

                // 重置位置和旋转
                this.tree.setPosition(new THREE.Vector3(0, 0, 0));
                this.tree.setRotation(new THREE.Euler(0, 0, 0));
                this.tree.setScale(0.02);

                // 重置滑块值
                document.getElementById('scaleSlider').value = 0.02;
                document.getElementById('scaleValue').textContent = '0.020';
                document.getElementById('posXSlider').value = 0;
                document.getElementById('posXValue').textContent = '0.0';
                document.getElementById('posYSlider').value = 0;
                document.getElementById('posYValue').textContent = '0.0';
                document.getElementById('posZSlider').value = 0;
                document.getElementById('posZValue').textContent = '0.0';
                document.getElementById('rotYSlider').value = 0;
                document.getElementById('rotYValue').textContent = '0°';

                this.updateStatus();
            }

            updateStatus() {
                if (!this.tree) return;

                const status = this.tree.getStatus();

                // 更新状态显示
                document.getElementById('currentState').textContent = status.state;
                document.getElementById('currentPosition').textContent =
                    `(${status.position.x.toFixed(1)}, ${status.position.y.toFixed(1)}, ${status.position.z.toFixed(1)})`;
                document.getElementById('currentScale').textContent = status.scale.toFixed(3);
            }

            updateSliders() {
                if (!this.tree) return;

                const status = this.tree.getStatus();

                // 更新滑块值
                document.getElementById('scaleSlider').value = status.scale;
                document.getElementById('scaleValue').textContent = status.scale.toFixed(3);

                document.getElementById('posXSlider').value = status.position.x;
                document.getElementById('posXValue').textContent = status.position.x.toFixed(1);

                document.getElementById('posYSlider').value = status.position.y;
                document.getElementById('posYValue').textContent = status.position.y.toFixed(1);

                document.getElementById('posZSlider').value = status.position.z;
                document.getElementById('posZValue').textContent = status.position.z.toFixed(1);

                document.getElementById('rotYSlider').value = THREE.MathUtils.radToDeg(status.rotation.y);
                document.getElementById('rotYValue').textContent = THREE.MathUtils.radToDeg(status.rotation.y).toFixed(0) + '°';

                document.getElementById('swayIntensitySlider').value = status.swayIntensity;
                document.getElementById('swayIntensityValue').textContent = status.swayIntensity.toFixed(1);

                document.getElementById('swaySpeedSlider').value = status.swaySpeed;
                document.getElementById('swaySpeedValue').textContent = status.swaySpeed.toFixed(1);

                document.getElementById('windStrengthSlider').value = status.windStrength;
                document.getElementById('windStrengthValue').textContent = status.windStrength.toFixed(1);
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                if (this.controls) {
                    this.controls.update();
                }

                // 更新树动画
                if (this.tree) {
                    this.tree.update();
                }

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.tree) {
                    this.tree.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        // 启动应用
        const app = new TreeApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
