<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meat Model Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #controls h3, #status h3 {
            margin: 0 0 10px 0;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group h4 {
            margin: 0 0 8px 0;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .danger {
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
            color: white;
        }

        .info {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .slider-control {
            margin-bottom: 12px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-size: 12px;
        }

        .slider-control input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .value-display {
            font-size: 11px;
            color: #fff;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3px 6px;
            border-radius: 4px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #fff;
            font-weight: 500;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            padding: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar,
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track,
        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb,
        #status::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover,
        #status::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox 滚动条 */
        #controls,
        #status {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                position: relative;
                top: auto;
                left: auto;
                right: auto;
                width: calc(100% - 20px);
                margin: 10px;
                max-height: 200px;
            }

            #controls {
                order: 1;
            }

            #status {
                order: 2;
            }

            body {
                overflow-y: auto;
            }

            #container {
                height: auto;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }

            canvas {
                order: 3;
                height: 60vh !important;
            }
        }

        @media (max-width: 480px) {
            #controls, #status {
                font-size: 12px;
                padding: 10px;
            }

            button {
                padding: 6px 8px;
                font-size: 11px;
            }

            .slider-control label {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Meat Model...</div>
        </div>

        <div id="controls">
            <h3>🥩 肉块模型控制</h3>
            
            <div class="control-group">
                <h4>基础控制</h4>
                <div class="button-row">
                    <button id="resetBtn" class="primary">重置位置</button>
                </div>
            </div>

            <div class="control-group">
                <h4>变换控制</h4>
                <div class="slider-control">
                    <label for="scaleSlider">缩放：</label>
                    <input type="range" id="scaleSlider" min="0.05" max="0.5" step="0.01" value="0.1">
                    <div class="value-display" id="scaleValue">0.1</div>
                </div>
                <div class="slider-control">
                    <label for="posXSlider">位置 X：</label>
                    <input type="range" id="posXSlider" min="-5" max="5" step="0.1" value="0">
                    <div class="value-display" id="posXValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="posYSlider">位置 Y：</label>
                    <input type="range" id="posYSlider" min="-3" max="3" step="0.1" value="0">
                    <div class="value-display" id="posYValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="posZSlider">位置 Z：</label>
                    <input type="range" id="posZSlider" min="-5" max="5" step="0.1" value="0">
                    <div class="value-display" id="posZValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="rotXSlider">旋转 X：</label>
                    <input type="range" id="rotXSlider" min="0" max="6.28" step="0.1" value="0">
                    <div class="value-display" id="rotXValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="rotYSlider">旋转 Y：</label>
                    <input type="range" id="rotYSlider" min="0" max="6.28" step="0.1" value="0">
                    <div class="value-display" id="rotYValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="rotZSlider">旋转 Z：</label>
                    <input type="range" id="rotZSlider" min="0" max="6.28" step="0.1" value="0">
                    <div class="value-display" id="rotZValue">0</div>
                </div>
            </div>

            <div class="control-group">
                <h4>材质属性</h4>
                <div class="slider-control">
                    <label for="roughnessSlider">粗糙度：</label>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.01" value="0.5">
                    <div class="value-display" id="roughnessValue">0.5</div>
                </div>
                <div class="slider-control">
                    <label for="metalnessSlider">金属度：</label>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.01" value="0">
                    <div class="value-display" id="metalnessValue">0</div>
                </div>
            </div>

            <div class="control-group">
                <h4>调试工具</h4>
                <div class="button-row">
                    <button id="debugBtn" class="info">调试材质</button>
                    <button id="reapplyTextureBtn" class="info">重新应用纹理</button>
                </div>
                <div class="button-row">
                    <button id="resetMaterialBtn" class="primary">重置材质</button>
                </div>
            </div>
        </div>

        <div id="status">
            <h3>📊 模型状态</h3>
            <div class="status-item">
                <span class="status-label">名称:</span>
                <span class="status-value" id="nameStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">已加载:</span>
                <span class="status-value" id="loadedStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="positionStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="rotationStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="scaleStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="roughnessStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="metalnessStatus">-</span>
            </div>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { MeatModel } from '../src/entities/MeatModel.js';

        class MeatDemo {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.meat = null;
                
                this.init();
            }

            async init() {
                // 设置渲染器
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x87CEEB, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 设置相机位置
                this.camera.position.set(0, 2, 5);
                this.camera.lookAt(0, 0, 0);

                // 添加光照
                this.setupLights();

                // 添加地面
                this.addGround();

                // 创建肉块模型
                await this.createMeat();

                // 设置控制事件
                this.setupControls();

                // 开始渲染循环
                this.animate();

                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            }

            setupLights() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);

                // 方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(5, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 500;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async createMeat() {
                this.meat = new MeatModel(this.scene);
                
                // 设置事件回调
                this.meat.setEventCallbacks({
                    onLoadComplete: () => {
                        console.log('肉块模型加载完成');
                        this.updateStatus();
                    },
                    onTextureLoaded: () => {
                        console.log('肉块纹理加载完成');
                        this.updateStatus();
                    }
                });

                await this.meat.initialize();
            }

            setupControls() {
                // 重置按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.meat.setPosition(new THREE.Vector3(0, 0, 0));
                    this.meat.setRotation(new THREE.Euler(0, 0, 0));
                    this.meat.setScale(0.1);
                    this.updateSliders();
                    this.updateStatus();
                });

                // 缩放控制
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', () => {
                    const scale = parseFloat(scaleSlider.value);
                    this.meat.setScale(scale);
                    scaleValue.textContent = scale.toFixed(2);
                    this.updateStatus();
                });

                // 位置控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    const slider = document.getElementById(`pos${axis}Slider`);
                    const value = document.getElementById(`pos${axis}Value`);
                    slider.addEventListener('input', () => {
                        const pos = this.meat.getPosition();
                        pos[axis.toLowerCase()] = parseFloat(slider.value);
                        this.meat.setPosition(pos);
                        value.textContent = slider.value;
                        this.updateStatus();
                    });
                });

                // 旋转控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    const slider = document.getElementById(`rot${axis}Slider`);
                    const value = document.getElementById(`rot${axis}Value`);
                    slider.addEventListener('input', () => {
                        const rot = this.meat.getRotation();
                        rot[axis.toLowerCase()] = parseFloat(slider.value);
                        this.meat.setRotation(rot);
                        value.textContent = parseFloat(slider.value).toFixed(2);
                        this.updateStatus();
                    });
                });

                // 材质属性控制
                const roughnessSlider = document.getElementById('roughnessSlider');
                const roughnessValue = document.getElementById('roughnessValue');
                roughnessSlider.addEventListener('input', () => {
                    const roughness = parseFloat(roughnessSlider.value);
                    this.meat.setRoughness(roughness);
                    roughnessValue.textContent = roughness.toFixed(2);
                });

                const metalnessSlider = document.getElementById('metalnessSlider');
                const metalnessValue = document.getElementById('metalnessValue');
                metalnessSlider.addEventListener('input', () => {
                    const metalness = parseFloat(metalnessSlider.value);
                    this.meat.setMetalness(metalness);
                    metalnessValue.textContent = metalness.toFixed(2);
                });

                // 调试操作
                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.meat.debugMaterials();
                });

                document.getElementById('reapplyTextureBtn').addEventListener('click', async () => {
                    console.log('重新应用纹理...');
                    await this.meat.reapplyTexture();
                    console.log('纹理重新应用完成');
                });

                document.getElementById('resetMaterialBtn').addEventListener('click', () => {
                    console.log('重置材质属性...');
                    this.meat.resetMaterialProperties();
                    // 重置滑块值
                    roughnessSlider.value = '0.5';
                    roughnessValue.textContent = '0.5';
                    metalnessSlider.value = '0';
                    metalnessValue.textContent = '0';
                    console.log('材质属性重置完成');
                });

                // 窗口调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            updateSliders() {
                if (!this.meat) return;

                const status = this.meat.getStatus();
                
                // 更新滑块值
                document.getElementById('scaleSlider').value = status.scale;
                document.getElementById('scaleValue').textContent = status.scale.toFixed(2);
                
                document.getElementById('posXSlider').value = status.position.x;
                document.getElementById('posXValue').textContent = status.position.x.toFixed(1);
                
                document.getElementById('posYSlider').value = status.position.y;
                document.getElementById('posYValue').textContent = status.position.y.toFixed(1);
                
                document.getElementById('posZSlider').value = status.position.z;
                document.getElementById('posZValue').textContent = status.position.z.toFixed(1);
                
                document.getElementById('rotXSlider').value = status.rotation.x;
                document.getElementById('rotXValue').textContent = status.rotation.x.toFixed(2);
                
                document.getElementById('rotYSlider').value = status.rotation.y;
                document.getElementById('rotYValue').textContent = status.rotation.y.toFixed(2);
                
                document.getElementById('rotZSlider').value = status.rotation.z;
                document.getElementById('rotZValue').textContent = status.rotation.z.toFixed(2);
            }

            updateStatus() {
                if (!this.meat) return;

                const status = this.meat.getStatus();
                
                document.getElementById('nameStatus').textContent = status.name;
                document.getElementById('loadedStatus').textContent = status.loaded ? '是' : '否';
                document.getElementById('positionStatus').textContent = 
                    `(${status.position.x.toFixed(1)}, ${status.position.y.toFixed(1)}, ${status.position.z.toFixed(1)})`;
                document.getElementById('rotationStatus').textContent = 
                    `(${status.rotation.x.toFixed(2)}, ${status.rotation.y.toFixed(2)}, ${status.rotation.z.toFixed(2)})`;
                document.getElementById('scaleStatus').textContent = status.scale.toFixed(2);
                document.getElementById('roughnessStatus').textContent = status.roughness.toFixed(2);
                document.getElementById('metalnessStatus').textContent = status.metalness.toFixed(2);
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                this.renderer.render(this.scene, this.camera);
            }
        }

        // 启动演示
        new MeatDemo();
    </script>
</body>
</html> 