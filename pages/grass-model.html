<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grass Model Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #4ecdc4;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #4ecdc4;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #4ecdc4;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #388E3C, #2E7D32);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        button.secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
            transform: translateY(-2px);
        }

        .slider-group {
            margin-bottom: 12px;
        }

        .slider-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
            color: #ccc;
        }

        .slider-value {
            color: #4ecdc4;
            font-weight: 500;
        }

        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #fff;
            font-weight: 500;
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar,
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track,
        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb,
        #status::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover,
        #status::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox 滚动条 */
        #controls,
        #status {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .back-button:hover {
            background: linear-gradient(135deg, #388E3C, #2E7D32);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                position: relative;
                top: auto;
                left: auto;
                right: auto;
                width: calc(100% - 20px);
                margin: 10px;
                max-height: 300px;
            }

            #controls {
                order: 1;
            }

            #status {
                order: 2;
            }

            body {
                overflow-y: auto;
                display: flex;
                flex-direction: column;
            }

            #container {
                height: 60vh;
                order: 3;
            }

            .back-button {
                position: relative;
                bottom: auto;
                left: auto;
                margin: 10px;
                order: 4;
            }

            button {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            #controls, #status {
                font-size: 12px;
                padding: 10px;
            }

            button {
                padding: 6px 12px;
                font-size: 11px;
            }

            h1 {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载草地模型...</div>
        </div>

        <div id="controls">
            <h1>🌱 Grass 控制面板</h1>
            
            <div class="control-group">
                <h4>基础操作</h4>
                <div class="button-row">
                    <button id="resetBtn" class="secondary">重置</button>
                </div>
            </div>

            <div class="control-group">
                <h4>位置调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>X轴:</span>
                        <span class="slider-value" id="posXValue">0.0</span>
                    </div>
                    <input type="range" id="posXSlider" min="-10" max="10" step="0.1" value="0">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Y轴:</span>
                        <span class="slider-value" id="posYValue">0.0</span>
                    </div>
                    <input type="range" id="posYSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Z轴:</span>
                        <span class="slider-value" id="posZValue">0.0</span>
                    </div>
                    <input type="range" id="posZSlider" min="-10" max="10" step="0.1" value="0">
                </div>
            </div>

            <div class="control-group">
                <h4>变换调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>缩放:</span>
                        <span class="slider-value" id="scaleValue">0.500</span>
                    </div>
                    <input type="range" id="scaleSlider" min="0.1" max="2.0" step="0.001" value="0.5">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Y轴旋转:</span>
                        <span class="slider-value" id="rotYValue">0°</span>
                    </div>
                    <input type="range" id="rotYSlider" min="0" max="360" step="1" value="0">
                </div>
            </div>

            <div class="control-group">
                <h4>材质调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>粗糙度:</span>
                        <span class="slider-value" id="roughnessValue">0.8</span>
                    </div>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.01" value="0.8">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>金属度:</span>
                        <span class="slider-value" id="metalnessValue">0.0</span>
                    </div>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.01" value="0">
                </div>
                <div class="button-row">
                    <button id="resetMaterialBtn" class="secondary">重置材质</button>
                </div>
            </div>

            <div class="control-group">
                <h4>调试工具</h4>
                <div class="button-row">
                    <button id="debugBtn" class="secondary">材质调试</button>
                </div>
            </div>
        </div>

        <!-- 状态显示面板 -->
        <div id="status">
            <h1>📊 模型状态</h1>
            <div class="status-item">
                <span class="status-label">名称:</span>
                <span class="status-value" id="nameStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">已加载:</span>
                <span class="status-value" id="loadedStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="positionStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="rotationStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="scaleStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="roughnessStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="metalnessStatus">-</span>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <button class="back-button" onclick="window.location.href='../index.html'">
        ← 返回主页
    </button>

    <script type="module">
        import * as THREE from '/node_modules/three/build/three.module.js';
        import { OrbitControls } from '/node_modules/three/examples/jsm/controls/OrbitControls.js';
        import { GrassModel } from '/src/entities/GrassModel.js';

        class GrassApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.grass = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                // 背景色 - 基于Cocos项目天空颜色和主光源色调的混合
                this.scene.background = new THREE.Color(0xFFF8E9);  // 与主光源颜色一致的暖白色

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(3, 2, 3);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;

                document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;

                // 添加光照
                this.setupLighting();

                // 添加地面
                this.addGround();

                // 自动加载Grass模型
                this.loadGrass();

                // 开始渲染循环
                this.animate();

                // 窗口大小调整
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupLighting() {
                // 环境光 - 精确复现Cocos项目配置
                // 天空颜色: (1, 1, 1, 0.52) - 白色，强度0.52
                const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.52);
                this.scene.add(ambientLight);

                // 主光源 - 精确复现Cocos项目主光源配置
                // 颜色: RGB(255, 248, 233) - 暖白色调
                // 强度: 1.69（LDR）
                // 位置: 欧拉角(-117.9°, 174.1°, 38.6°) 转换后的近似位置
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(-8, 12, 6);  // 根据欧拉角转换的近似位置
                directionalLight.castShadow = true;

                // 阴影配置 - 参考Cocos项目设置
                directionalLight.shadow.mapSize.width = 1024;   // Cocos项目使用1024x1024
                directionalLight.shadow.mapSize.height = 1024;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 注意: 原Cocos项目使用单一主光源模型，不使用补充光源
                console.log('🎯 已应用Cocos项目精确光照配置');
            }

            addGround() {
                // 地面 - 精确复现Cocos项目地面反照率配置
                // 地面反照率（LDR）: (0.618, 0.578, 0.545, 0) - 暖黄色调
                // 转换为RGB: (157, 147, 139) = 0x9D938B
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x9D938B,  // Cocos项目地面反照率精确颜色
                    transparent: false
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async loadGrass() {
                try {
                    document.getElementById('loading').style.display = 'block';

                    // 创建Grass实例
                    this.grass = new GrassModel(this.scene);

                    // 设置回调
                    this.grass.setEventCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            this.updateStatus();
                            console.log('Grass模型加载完成');

                            // 启用模型阴影
                            const model = this.grass.getModel();
                            if (model) {
                                model.traverse((child) => {
                                    if (child instanceof THREE.Mesh) {
                                        child.castShadow = true;
                                        child.receiveShadow = true;
                                    }
                                });
                            }
                        },
                        onTextureLoaded: () => {
                            console.log('Grass纹理加载完成');
                        }
                    });

                    // 初始化模型
                    await this.grass.initialize();

                } catch (error) {
                    console.error('加载Grass模型失败:', error);
                    document.getElementById('loading').innerHTML = '<div style="color: red;">加载失败: ' + error.message + '</div>';
                }
            }

            setupEventListeners() {
                // 基础操作按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetGrass();
                });

                // 位置调整滑块
                const posXSlider = document.getElementById('posXSlider');
                const posXValue = document.getElementById('posXValue');
                posXSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posXValue.textContent = value.toFixed(1);
                    if (this.grass) {
                        const pos = this.grass.getPosition();
                        this.grass.setPosition(new THREE.Vector3(value, pos.y, pos.z));
                        this.updateStatus();
                    }
                });

                const posYSlider = document.getElementById('posYSlider');
                const posYValue = document.getElementById('posYValue');
                posYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posYValue.textContent = value.toFixed(1);
                    if (this.grass) {
                        const pos = this.grass.getPosition();
                        this.grass.setPosition(new THREE.Vector3(pos.x, value, pos.z));
                        this.updateStatus();
                    }
                });

                const posZSlider = document.getElementById('posZSlider');
                const posZValue = document.getElementById('posZValue');
                posZSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posZValue.textContent = value.toFixed(1);
                    if (this.grass) {
                        const pos = this.grass.getPosition();
                        this.grass.setPosition(new THREE.Vector3(pos.x, pos.y, value));
                        this.updateStatus();
                    }
                });

                // 变换调整滑块
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    scaleValue.textContent = value.toFixed(3);
                    if (this.grass) {
                        this.grass.setScale(value);
                        this.updateStatus();
                    }
                });

                const rotYSlider = document.getElementById('rotYSlider');
                const rotYValue = document.getElementById('rotYValue');
                rotYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    rotYValue.textContent = value + '°';
                    if (this.grass) {
                        const rot = this.grass.getRotation();
                        this.grass.setRotation(new THREE.Euler(rot.x, THREE.MathUtils.degToRad(value), rot.z));
                        this.updateStatus();
                    }
                });

                // 材质调整滑块
                const roughnessSlider = document.getElementById('roughnessSlider');
                const roughnessValue = document.getElementById('roughnessValue');
                roughnessSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    roughnessValue.textContent = value.toFixed(2);
                    if (this.grass) {
                        this.grass.setRoughness(value);
                        this.updateStatus();
                    }
                });

                const metalnessSlider = document.getElementById('metalnessSlider');
                const metalnessValue = document.getElementById('metalnessValue');
                metalnessSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    metalnessValue.textContent = value.toFixed(2);
                    if (this.grass) {
                        this.grass.setMetalness(value);
                        this.updateStatus();
                    }
                });

                // 重置材质按钮
                document.getElementById('resetMaterialBtn').addEventListener('click', () => {
                    if (this.grass) {
                        this.grass.resetMaterialProperties();
                        roughnessSlider.value = '0.8';
                        roughnessValue.textContent = '0.80';
                        metalnessSlider.value = '0';
                        metalnessValue.textContent = '0.00';
                        this.updateStatus();
                    }
                });

                // 调试按钮
                document.getElementById('debugBtn').addEventListener('click', () => {
                    if (this.grass) {
                        this.grass.debugMaterials();
                    }
                });
            }

            resetGrass() {
                if (!this.grass) return;

                // 重置位置、旋转、缩放
                this.grass.setPosition(new THREE.Vector3(0, 0, 0));
                this.grass.setRotation(new THREE.Euler(0, 0, 0));
                this.grass.setScale(0.5);

                // 重置滑块值
                document.getElementById('posXSlider').value = '0';
                document.getElementById('posXValue').textContent = '0.0';
                document.getElementById('posYSlider').value = '0';
                document.getElementById('posYValue').textContent = '0.0';
                document.getElementById('posZSlider').value = '0';
                document.getElementById('posZValue').textContent = '0.0';
                document.getElementById('scaleSlider').value = '0.5';
                document.getElementById('scaleValue').textContent = '0.500';
                document.getElementById('rotYSlider').value = '0';
                document.getElementById('rotYValue').textContent = '0°';

                // 重置材质
                this.grass.resetMaterialProperties();
                document.getElementById('roughnessSlider').value = '0.8';
                document.getElementById('roughnessValue').textContent = '0.80';
                document.getElementById('metalnessSlider').value = '0';
                document.getElementById('metalnessValue').textContent = '0.00';

                this.updateStatus();
            }

            updateStatus() {
                if (!this.grass) return;

                const status = this.grass.getStatus();
                const position = status.position;
                const rotation = status.rotation;

                // 更新状态面板
                document.getElementById('nameStatus').textContent = status.name;
                document.getElementById('loadedStatus').textContent = status.loaded ? '是' : '否';
                document.getElementById('positionStatus').textContent =
                    `(${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`;
                document.getElementById('rotationStatus').textContent =
                    `(${rotation.x.toFixed(2)}, ${rotation.y.toFixed(2)}, ${rotation.z.toFixed(2)})`;
                document.getElementById('scaleStatus').textContent = status.scale.toFixed(3);
                document.getElementById('roughnessStatus').textContent = status.roughness.toFixed(2);
                document.getElementById('metalnessStatus').textContent = status.metalness.toFixed(2);
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.grass) {
                    this.grass.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('GrassApp disposed');
            }
        }

        // 启动应用
        const app = new GrassApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
