<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rocks Model - 岩石模型展示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #8B7355 0%, #A0522D 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #D2B48C;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #D2B48C;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #D2B48C;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #8B7355, #A0522D);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #A0522D, #8B7355);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);
        }

        button.secondary {
            background: linear-gradient(135deg, #696969, #555555);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(135deg, #555555, #696969);
            transform: translateY(-2px);
        }

        .slider-container {
            margin-bottom: 12px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #BDC3C7;
            font-size: 12px;
        }

        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #34495E;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-container input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #D2B48C;
            cursor: pointer;
            border: 2px solid #8B7355;
        }

        .slider-container input[type="color"] {
            width: 100%;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: transparent;
        }

        .value-display {
            color: #D2B48C;
            font-weight: bold;
            font-size: 11px;
            float: right;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #BDC3C7;
        }

        .status-value {
            color: #D2B48C;
            font-weight: bold;
        }

        .status-success {
            color: #27AE60;
        }

        .status-error {
            color: #E74C3C;
        }

        .status-warning {
            color: #F39C12;
        }

        /* 网格选择器样式 */
        .mesh-selector {
            margin-bottom: 15px;
        }

        .mesh-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 12px;
        }

        .mesh-selector select option {
            background: #2C3E50;
            color: white;
        }

        .mesh-controls {
            display: none;
            margin-top: 10px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
        }

        .mesh-controls.active {
            display: block;
        }

        .visibility-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .visibility-toggle input[type="checkbox"] {
            transform: scale(1.2);
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            color: white;
            z-index: 1000;
            display: none;
        }

        .spinner {
            border: 4px solid rgba(210, 180, 140, 0.3);
            border-top: 4px solid #D2B48C;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                width: calc(100vw - 40px);
                position: relative;
                top: 10px;
                left: 20px;
                right: 20px;
                margin-bottom: 20px;
            }
            
            #status {
                top: 0;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <!-- 左侧控制面板 -->
        <div id="controls">
            <h1>🪨 岩石模型控制</h1>
            
            <div class="control-group">
                <h4>🎮 基础控制</h4>
                <div class="button-row">
                    <button class="primary" id="loadBtn">加载模型</button>
                    <button class="secondary" id="resetBtn">重置视角</button>
                </div>
                <div class="button-row">
                    <button class="secondary" id="debugBtn">调试信息</button>
                </div>
            </div>

            <div class="control-group">
                <h4>🎯 网格选择</h4>
                <div class="mesh-selector">
                    <label for="meshSelector">选择网格:</label>
                    <select id="meshSelector">
                        <option value="-1">整体控制</option>
                    </select>
                </div>

                <!-- 单个网格控制 -->
                <div id="meshControls" class="mesh-controls">
                    <div class="visibility-toggle">
                        <input type="checkbox" id="meshVisibility" checked>
                        <label for="meshVisibility">显示/隐藏</label>
                    </div>
                    <div class="button-row">
                        <button class="secondary" id="showAllBtn">显示所有</button>
                        <button class="secondary" id="hideAllBtn">隐藏所有</button>
                    </div>
                    <div class="button-row">
                        <button class="secondary" id="resetVisibilityBtn">重置可见性</button>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>📐 变换控制</h4>
                <div class="slider-container">
                    <label for="positionXSlider">X位置: <span id="positionXValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionXSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="positionYSlider">Y位置: <span id="positionYValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionYSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="positionZSlider">Z位置: <span id="positionZValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionZSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="scaleSlider">缩放: <span id="scaleValue" class="value-display">1.0</span></label>
                    <input type="range" id="scaleSlider" min="0.1" max="3.0" step="0.1" value="1.0">
                </div>
                <div class="slider-container">
                    <label for="rotationXSlider">X轴旋转: <span id="rotationXValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationXSlider" min="0" max="360" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationYSlider">Y轴旋转: <span id="rotationYValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationYSlider" min="0" max="360" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationZSlider">Z轴旋转: <span id="rotationZValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationZSlider" min="0" max="360" step="5" value="0">
                </div>
            </div>

            <div class="control-group">
                <h4>🎨 材质控制</h4>
                <div class="slider-container">
                    <label for="colorPicker">主颜色</label>
                    <input type="color" id="colorPicker" value="#949494">
                </div>
                <div class="slider-container">
                    <label for="roughnessSlider">粗糙度: <span id="roughnessValue" class="value-display">0.75</span></label>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.05" value="0.75">
                </div>
                <div class="slider-container">
                    <label for="metalnessSlider">金属度: <span id="metalnessValue" class="value-display">0.1</span></label>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.05" value="0.1">
                </div>
            </div>
        </div>

        <!-- 右侧状态面板 -->
        <div id="status">
            <h1>📊 模型状态</h1>

            <div class="status-item">
                <span class="status-label">模型状态:</span>
                <span class="status-value" id="modelStatus">未加载</span>
            </div>
            <div class="status-item">
                <span class="status-label">网格数量:</span>
                <span class="status-value" id="meshCount">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前选择:</span>
                <span class="status-value" id="selectedMesh">整体</span>
            </div>
            <div class="status-item">
                <span class="status-label">网格可见:</span>
                <span class="status-value" id="meshVisible">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="currentPosition">0, 0, 0</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="currentRotation">0°, 0°, 0°</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="currentScale">1.0</span>
            </div>
            <div class="status-item">
                <span class="status-label">材质颜色:</span>
                <span class="status-value" id="currentColor">#949494</span>
            </div>
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="currentRoughness">0.75</span>
            </div>
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="currentMetalness">0.1</span>
            </div>
            <div class="status-item">
                <span class="status-label">可见网格:</span>
                <span class="status-value" id="visibleMeshes">-</span>
            </div>
        </div>

        <!-- 加载动画 -->
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载岩石模型...</div>
        </div>
    </div>

    <script type="module">
        import * as THREE from '/node_modules/three/build/three.module.js';
        import { OrbitControls } from '/node_modules/three/examples/jsm/controls/OrbitControls.js';
        import { RocksModel } from '/src/entities/RocksModel.js';

        class RocksModelApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.rocks = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
                this.animate();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                // 背景色 - 基于Cocos项目天空颜色和主光源色调的混合
                this.scene.background = new THREE.Color(0xFFF8E9);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 3, 5);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({
                    antialias: true,
                    alpha: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;

                document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.minDistance = 2;
                this.controls.maxDistance = 20;

                // 添加光照
                this.setupLighting();

                // 添加地面
                this.addGround();

                // 自动加载Rocks模型
                this.loadRocks();

                // 开始渲染循环
                this.animate();

                // 窗口大小调整
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupLighting() {
                // 环境光 - 精确复现Cocos项目配置
                // 天空颜色: (1, 1, 1, 0.52) - 白色，强度0.52
                const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.52);
                this.scene.add(ambientLight);

                // 主光源 - 精确复现Cocos项目主光源配置
                // 颜色: RGB(255, 248, 233) - 暖白色调
                // 强度: 1.69（LDR）
                // 位置: 欧拉角(-117.9°, 174.1°, 38.6°) 转换后的近似位置
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(-8, 12, 6);  // 根据欧拉角转换的近似位置
                directionalLight.castShadow = true;

                // 阴影配置 - 参考Cocos项目设置
                directionalLight.shadow.mapSize.width = 1024;   // Cocos项目使用1024x1024
                directionalLight.shadow.mapSize.height = 1024;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 注意: 原Cocos项目使用单一主光源模型，不使用补充光源
                console.log('🎯 已应用Cocos项目精确光照配置');
            }

            addGround() {
                // 地面 - 精确复现Cocos项目地面反照率配置
                // 地面反照率（LDR）: (0.618, 0.578, 0.545, 0) - 暖黄色调
                // 转换为RGB: (157, 147, 139) = 0x9D938B
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x9D938B,  // Cocos项目地面反照率精确颜色
                    transparent: false
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            setupEventListeners() {
                // 加载按钮
                document.getElementById('loadBtn').addEventListener('click', () => {
                    this.loadRocks();
                });

                // 重置按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetCamera();
                });

                // 调试按钮
                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.debugRocks();
                });

                // 网格选择器
                document.getElementById('meshSelector').addEventListener('change', (e) => {
                    const meshIndex = parseInt(e.target.value);
                    if (this.rocks) {
                        this.rocks.selectMesh(meshIndex);
                        this.updateControlsForSelectedMesh(meshIndex);
                    }
                });

                // 网格可见性控制
                document.getElementById('meshVisibility').addEventListener('change', (e) => {
                    const meshIndex = this.rocks ? this.rocks.getSelectedMeshIndex() : -1;
                    if (this.rocks && meshIndex >= 0) {
                        this.rocks.setMeshVisibility(meshIndex, e.target.checked);
                        this.updateAllStatus();
                    }
                });

                // 显示所有网格按钮
                document.getElementById('showAllBtn').addEventListener('click', () => {
                    if (this.rocks) {
                        const rockMeshes = this.rocks.getRockMeshes();
                        rockMeshes.forEach((_, index) => {
                            this.rocks.setMeshVisibility(index, true);
                        });
                        this.updateAllStatus();
                        console.log('👁️ 已显示所有网格');
                    }
                });

                // 隐藏所有网格按钮
                document.getElementById('hideAllBtn').addEventListener('click', () => {
                    if (this.rocks) {
                        const rockMeshes = this.rocks.getRockMeshes();
                        rockMeshes.forEach((_, index) => {
                            this.rocks.setMeshVisibility(index, false);
                        });
                        this.updateAllStatus();
                        console.log('🙈 已隐藏所有网格');
                    }
                });

                // 重置可见性按钮
                document.getElementById('resetVisibilityBtn').addEventListener('click', () => {
                    if (this.rocks) {
                        this.rocks.resetMeshVisibility();
                        this.updateAllStatus();
                        console.log('🔄 已重置网格可见性到默认状态');
                    }
                });

                // 位置控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    document.getElementById(`position${axis}Slider`).addEventListener('input', (e) => {
                        const value = parseFloat(e.target.value);
                        document.getElementById(`position${axis}Value`).textContent = value.toFixed(1);

                        if (this.rocks) {
                            const meshIndex = this.rocks.getSelectedMeshIndex();
                            const currentPos = this.getCurrentPosition(meshIndex);

                            if (axis === 'X') currentPos.x = value;
                            else if (axis === 'Y') currentPos.y = value;
                            else if (axis === 'Z') currentPos.z = value;

                            this.rocks.setMeshPosition(meshIndex, currentPos);
                            this.updatePositionStatus();
                        }
                    });
                });

                // 缩放控制
                document.getElementById('scaleSlider').addEventListener('input', (e) => {
                    const scale = parseFloat(e.target.value);
                    document.getElementById('scaleValue').textContent = scale.toFixed(1);

                    if (this.rocks) {
                        const meshIndex = this.rocks.getSelectedMeshIndex();
                        this.rocks.setMeshScale(meshIndex, scale);
                        this.updateScaleStatus();
                    }
                });

                // 旋转控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    document.getElementById(`rotation${axis}Slider`).addEventListener('input', (e) => {
                        const rotation = parseInt(e.target.value);
                        document.getElementById(`rotation${axis}Value`).textContent = rotation + '°';

                        if (this.rocks) {
                            const meshIndex = this.rocks.getSelectedMeshIndex();
                            const currentRot = this.getCurrentRotation(meshIndex);
                            const radians = (rotation * Math.PI) / 180;

                            if (axis === 'X') currentRot.x = radians;
                            else if (axis === 'Y') currentRot.y = radians;
                            else if (axis === 'Z') currentRot.z = radians;

                            this.rocks.setMeshRotation(meshIndex, currentRot);
                            this.updateRotationStatus();
                        }
                    });
                });

                // 颜色控制
                document.getElementById('colorPicker').addEventListener('input', (e) => {
                    const color = new THREE.Color(e.target.value);
                    document.getElementById('currentColor').textContent = e.target.value.toUpperCase();

                    if (this.rocks) {
                        const meshIndex = this.rocks.getSelectedMeshIndex();
                        this.rocks.setMeshMaterialProperties(meshIndex, { color });
                    }
                });

                // 粗糙度控制
                document.getElementById('roughnessSlider').addEventListener('input', (e) => {
                    const roughness = parseFloat(e.target.value);
                    document.getElementById('roughnessValue').textContent = roughness.toFixed(2);
                    document.getElementById('currentRoughness').textContent = roughness.toFixed(2);

                    if (this.rocks) {
                        const meshIndex = this.rocks.getSelectedMeshIndex();
                        this.rocks.setMeshMaterialProperties(meshIndex, { roughness });
                    }
                });

                // 金属度控制
                document.getElementById('metalnessSlider').addEventListener('input', (e) => {
                    const metalness = parseFloat(e.target.value);
                    document.getElementById('metalnessValue').textContent = metalness.toFixed(2);
                    document.getElementById('currentMetalness').textContent = metalness.toFixed(2);

                    if (this.rocks) {
                        const meshIndex = this.rocks.getSelectedMeshIndex();
                        this.rocks.setMeshMaterialProperties(meshIndex, { metalness });
                    }
                });
            }

            async loadRocks() {
                try {
                    document.getElementById('loading').style.display = 'block';
                    document.getElementById('modelStatus').textContent = '加载中...';
                    document.getElementById('modelStatus').className = 'status-value status-warning';

                    // 创建岩石实例
                    this.rocks = new RocksModel(this.scene);

                    // 设置回调
                    this.rocks.setEventCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('modelStatus').textContent = '已加载';
                            document.getElementById('modelStatus').className = 'status-value status-success';
                            this.updateModelInfo();
                            this.populateMeshSelector();
                            console.log('✅ 岩石模型加载完成');
                        },
                        onMaterialApplied: () => {
                            console.log('✅ 岩石材质应用完成');
                        },
                        onMeshSelectionChanged: (meshIndex) => {
                            this.updateControlsForSelectedMesh(meshIndex);
                        }
                    });

                    // 初始化模型
                    await this.rocks.initialize();

                } catch (error) {
                    console.error('❌ 加载岩石模型失败:', error);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('modelStatus').textContent = '加载失败';
                    document.getElementById('modelStatus').className = 'status-value status-error';
                }
            }

            updateModelInfo() {
                if (!this.rocks) return;

                const rockMeshes = this.rocks.getRockMeshes();
                document.getElementById('meshCount').textContent = rockMeshes.length;
            }

            // 填充网格选择器
            populateMeshSelector() {
                if (!this.rocks) return;

                const selector = document.getElementById('meshSelector');
                const rockMeshes = this.rocks.getRockMeshes();

                // 清空现有选项（保留整体控制选项）
                while (selector.children.length > 1) {
                    selector.removeChild(selector.lastChild);
                }

                // 添加各个网格选项
                rockMeshes.forEach((rockMesh, index) => {
                    const option = document.createElement('option');
                    option.value = index.toString();
                    option.textContent = `${rockMesh.name} (网格 ${index + 1})`;
                    selector.appendChild(option);
                });

                // 默认选择第一个网格（索引0）
                selector.value = '0';
                this.rocks.selectMesh(0);
                this.updateControlsForSelectedMesh(0);

                console.log(`🎯 已添加 ${rockMeshes.length} 个网格到选择器，默认选择第一个`);
            }

            // 更新选中网格的控制面板
            updateControlsForSelectedMesh(meshIndex) {
                const meshControls = document.getElementById('meshControls');

                if (meshIndex === -1) {
                    // 整体控制
                    meshControls.classList.remove('active');
                    document.getElementById('selectedMesh').textContent = '整体';
                    document.getElementById('meshVisible').textContent = '-';
                } else {
                    // 单个网格控制
                    meshControls.classList.add('active');
                    const rockMeshes = this.rocks.getRockMeshes();
                    if (meshIndex < rockMeshes.length) {
                        const rockMesh = rockMeshes[meshIndex];
                        document.getElementById('selectedMesh').textContent = rockMesh.name;

                        // 同步可见性状态（从实际mesh获取最新状态）
                        const actualVisible = rockMesh.mesh.visible;
                        document.getElementById('meshVisibility').checked = actualVisible;
                        document.getElementById('meshVisible').textContent = actualVisible ? '是' : '否';

                        // 更新RockMeshInfo中的visible状态
                        rockMesh.visible = actualVisible;
                    }
                }

                this.updateAllStatus();
            }

            // 获取当前位置
            getCurrentPosition(meshIndex) {
                if (!this.rocks) return new THREE.Vector3(0, 0, 0);

                if (meshIndex === -1) {
                    return this.rocks.getPosition();
                } else {
                    const rockMeshes = this.rocks.getRockMeshes();
                    if (meshIndex < rockMeshes.length) {
                        return rockMeshes[meshIndex].mesh.position.clone();
                    }
                }
                return new THREE.Vector3(0, 0, 0);
            }

            // 获取当前旋转
            getCurrentRotation(meshIndex) {
                if (!this.rocks) return new THREE.Euler(0, 0, 0);

                if (meshIndex === -1) {
                    return this.rocks.getRotation();
                } else {
                    const rockMeshes = this.rocks.getRockMeshes();
                    if (meshIndex < rockMeshes.length) {
                        return rockMeshes[meshIndex].mesh.rotation.clone();
                    }
                }
                return new THREE.Euler(0, 0, 0);
            }

            // 更新状态显示
            updateAllStatus() {
                this.updatePositionStatus();
                this.updateRotationStatus();
                this.updateScaleStatus();
                this.updateMeshStatus();
                this.updateVisibleMeshesStatus();
            }

            updatePositionStatus() {
                if (!this.rocks) return;

                const meshIndex = this.rocks.getSelectedMeshIndex();
                const pos = this.getCurrentPosition(meshIndex);
                document.getElementById('currentPosition').textContent =
                    `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
            }

            updateRotationStatus() {
                if (!this.rocks) return;

                const meshIndex = this.rocks.getSelectedMeshIndex();
                const rot = this.getCurrentRotation(meshIndex);
                const degX = Math.round(rot.x * 180 / Math.PI);
                const degY = Math.round(rot.y * 180 / Math.PI);
                const degZ = Math.round(rot.z * 180 / Math.PI);
                document.getElementById('currentRotation').textContent =
                    `${degX}°, ${degY}°, ${degZ}°`;
            }

            updateScaleStatus() {
                if (!this.rocks) return;

                const meshIndex = this.rocks.getSelectedMeshIndex();
                let scale = 1.0;

                if (meshIndex === -1) {
                    scale = this.rocks.getScale();
                } else {
                    const rockMeshes = this.rocks.getRockMeshes();
                    if (meshIndex < rockMeshes.length) {
                        scale = rockMeshes[meshIndex].mesh.scale.x;
                    }
                }

                document.getElementById('currentScale').textContent = scale.toFixed(1);
            }

            updateMeshStatus() {
                if (!this.rocks) return;

                const meshIndex = this.rocks.getSelectedMeshIndex();
                if (meshIndex >= 0) {
                    const rockMeshes = this.rocks.getRockMeshes();
                    if (meshIndex < rockMeshes.length) {
                        // 从实际mesh获取最新的可见性状态
                        const actualVisible = rockMeshes[meshIndex].mesh.visible;
                        document.getElementById('meshVisible').textContent = actualVisible ? '是' : '否';

                        // 同步复选框状态
                        document.getElementById('meshVisibility').checked = actualVisible;

                        // 更新RockMeshInfo中的状态
                        rockMeshes[meshIndex].visible = actualVisible;
                    }
                } else {
                    document.getElementById('meshVisible').textContent = '-';
                }
            }

            updateVisibleMeshesStatus() {
                if (!this.rocks) {
                    document.getElementById('visibleMeshes').textContent = '-';
                    return;
                }

                const rockMeshes = this.rocks.getRockMeshes();
                const visibleMeshes = rockMeshes
                    .map((rockMesh, index) => rockMesh.mesh.visible ? (index + 1) : null)
                    .filter(index => index !== null);

                if (visibleMeshes.length === 0) {
                    document.getElementById('visibleMeshes').textContent = '无';
                } else if (visibleMeshes.length === rockMeshes.length) {
                    document.getElementById('visibleMeshes').textContent = '全部';
                } else {
                    document.getElementById('visibleMeshes').textContent = visibleMeshes.join(', ');
                }
            }

            resetCamera() {
                this.camera.position.set(5, 3, 5);
                this.controls.reset();
                console.log('📷 相机视角已重置');
            }

            debugRocks() {
                if (this.rocks) {
                    this.rocks.debugMaterials();
                } else {
                    console.log('❌ 岩石模型未加载');
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.rocks) {
                    this.rocks.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('RocksModelApp disposed');
            }
        }

        // 启动应用
        const app = new RocksModelApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
