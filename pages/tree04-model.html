<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tree04 Model - 3D树模型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #fff;
            font-weight: 500;
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar,
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track,
        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb,
        #status::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover,
        #status::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox 滚动条 */
        #controls,
        #status {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        h1 {
            color: #4ecdc4;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #4ecdc4;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #4ecdc4;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        button.secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
            transform: translateY(-2px);
        }

        button.success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        button.success:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
            transform: translateY(-2px);
        }

        button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        button.warning:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
            transform: translateY(-2px);
        }

        button.active {
            background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .slider-control {
            margin-bottom: 15px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }

        .slider-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input[type="range"] {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
        }

        .slider-value {
            min-width: 50px;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            background: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-info {
            background: rgba(255, 255, 255, 0.6);
            padding: 12px;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.6;
        }

        .status-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
        }

        .status-info div:last-child {
            margin-bottom: 0;
        }

        .status-info .label {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-info .value {
            color: #3498db;
            font-weight: 500;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb {
            background: rgba(52, 152, 219, 0.6);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover {
            background: rgba(52, 152, 219, 0.8);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls {
                width: calc(100vw - 40px);
                right: 20px;
                left: 20px;
                top: 20px;
                max-height: calc(100vh - 40px);
            }
            
            h1 {
                font-size: 20px;
            }
            
            .button-row {
                flex-direction: column;
            }
            
            button {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载Tree04模型...</div>
        </div>

        <div id="controls">
            <h1>🌳 Tree04 控制面板</h1>
            
            <div class="control-group">
                <h4>基础操作</h4>
                <div class="button-row">
                    <button id="resetBtn" class="secondary">重置</button>
                </div>
            </div>

            <div class="control-group">
                <h4>动画状态</h4>
                <div class="button-row">
                    <button id="staticBtn" class="secondary active">静态</button>
                    <button id="swayingBtn" class="success">摆动</button>
                    <button id="windyBtn" class="warning">强风</button>
                </div>
            </div>

            <div class="control-group">
                <h4>位置调整</h4>
                <div class="slider-control">
                    <label>X轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posXSlider" min="-10" max="10" step="0.1" value="0">
                        <span class="slider-value" id="posXValue">0.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Y轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posYSlider" min="-5" max="5" step="0.1" value="0">
                        <span class="slider-value" id="posYValue">0.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Z轴位置</label>
                    <div class="slider-row">
                        <input type="range" id="posZSlider" min="-10" max="10" step="0.1" value="0">
                        <span class="slider-value" id="posZValue">0.0</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>变换调整</h4>
                <div class="slider-control">
                    <label>缩放</label>
                    <div class="slider-row">
                        <input type="range" id="scaleSlider" min="0.005" max="0.1" step="0.001" value="0.02">
                        <span class="slider-value" id="scaleValue">0.020</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>Y轴旋转</label>
                    <div class="slider-row">
                        <input type="range" id="rotYSlider" min="0" max="360" step="1" value="0">
                        <span class="slider-value" id="rotYValue">0°</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>动画参数</h4>
                <div class="slider-control">
                    <label>摆动强度</label>
                    <div class="slider-row">
                        <input type="range" id="swayIntensitySlider" min="0" max="2" step="0.1" value="0.5">
                        <span class="slider-value" id="swayIntensityValue">0.5</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>摆动速度</label>
                    <div class="slider-row">
                        <input type="range" id="swaySpeedSlider" min="0.1" max="3" step="0.1" value="1.0">
                        <span class="slider-value" id="swaySpeedValue">1.0</span>
                    </div>
                </div>
                <div class="slider-control">
                    <label>风力强度</label>
                    <div class="slider-row">
                        <input type="range" id="windStrengthSlider" min="0" max="3" step="0.1" value="1.0">
                        <span class="slider-value" id="windStrengthValue">1.0</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>颜色调整</h4>
                <div class="slider-control">
                    <label>色温调整 (模拟Cocos效果)</label>
                    <div class="slider-row">
                        <input type="range" id="colorWarmthSlider" min="0" max="100" step="1" value="0">
                        <span class="slider-value" id="colorWarmthValue">0</span>
                    </div>
                </div>
                <div class="button-row">
                    <button id="resetColorBtn" class="secondary">重置颜色</button>
                </div>
            </div>

            <div class="control-group">
                <h4>状态信息</h4>
                <div class="status-info" id="statusInfo">
                    <div><span class="label">状态:</span> <span class="value" id="currentState">加载中...</span></div>
                    <div><span class="label">位置:</span> <span class="value" id="currentPosition">-</span></div>
                    <div><span class="label">缩放:</span> <span class="value" id="currentScale">-</span></div>
                    <div><span class="label">色温:</span> <span class="value" id="currentWarmth">0</span></div>
                </div>
            </div>

            <div class="control-group">
                <h4>调试工具</h4>
                <div class="button-row">
                    <button id="debugBtn" class="secondary">材质调试</button>
                </div>
            </div>
        </div>

        <!-- 状态显示面板 -->
        <div id="status">
            <h1>📊 模型状态</h1>
            <div class="status-item">
                <span class="status-label">名称:</span>
                <span class="status-value" id="nameStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">已加载:</span>
                <span class="status-value" id="loadedStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前状态:</span>
                <span class="status-value" id="currentStateStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="positionStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="rotationStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="scaleStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">色温:</span>
                <span class="status-value" id="colorWarmthStatus">-</span>
            </div>
        </div>
    </div>

    <script type="module">
        import * as THREE from '/node_modules/three/build/three.module.js';
        import { OrbitControls } from '/node_modules/three/examples/jsm/controls/OrbitControls.js';
        import { Tree04Model, Tree04State } from '/src/entities/Tree04Model.js';

        class TreeApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.tree = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 3, 5);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;

                document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;

                // 添加光照
                this.setupLighting();

                // 添加地面
                this.addGround();

                // 自动加载Tree04模型
                this.loadTree();

                // 开始渲染循环
                this.animate();

                // 窗口大小调整
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);

                // 主光源
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 补充光源
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
                fillLight.position.set(-5, 5, -5);
                this.scene.add(fillLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x90EE90,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async loadTree() {
                try {
                    document.getElementById('loading').style.display = 'block';

                    // 创建Tree04实例
                    this.tree = Tree04Model.create(this.scene, 'Tree04');

                    // 设置回调
                    this.tree.setCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            this.updateStatus();
                            console.log('Tree04模型加载完成');
                        },
                        onStateChange: (state) => {
                            this.updateStateButtons(state);
                            this.updateStatus();
                        }
                    });

                    // 初始化模型
                    await this.tree.initialize();

                } catch (error) {
                    console.error('加载Tree04模型失败:', error);
                    document.getElementById('loading').innerHTML = '<div style="color: red;">加载失败: ' + error.message + '</div>';
                }
            }

            setupEventListeners() {
                // 基础操作按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetTree();
                });

                // 状态切换按钮
                document.getElementById('staticBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(Tree04State.STATIC);
                    }
                });

                document.getElementById('swayingBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(Tree04State.SWAYING);
                    }
                });

                document.getElementById('windyBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.setState(Tree04State.WINDY);
                    }
                });

                // 位置调整滑块
                const posXSlider = document.getElementById('posXSlider');
                const posXValue = document.getElementById('posXValue');
                posXSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posXValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        const pos = this.tree.getPosition();
                        this.tree.setPosition(value, pos.y, pos.z);
                        this.updateStatus();
                    }
                });

                const posYSlider = document.getElementById('posYSlider');
                const posYValue = document.getElementById('posYValue');
                posYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posYValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        const pos = this.tree.getPosition();
                        this.tree.setPosition(pos.x, value, pos.z);
                        this.updateStatus();
                    }
                });

                const posZSlider = document.getElementById('posZSlider');
                const posZValue = document.getElementById('posZValue');
                posZSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posZValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        const pos = this.tree.getPosition();
                        this.tree.setPosition(pos.x, pos.y, value);
                        this.updateStatus();
                    }
                });

                // 变换调整滑块
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    scaleValue.textContent = value.toFixed(3);
                    if (this.tree) {
                        this.tree.setScale(value);
                        this.updateStatus();
                    }
                });

                const rotYSlider = document.getElementById('rotYSlider');
                const rotYValue = document.getElementById('rotYValue');
                rotYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    rotYValue.textContent = value + '°';
                    if (this.tree) {
                        const rot = this.tree.getRotation();
                        this.tree.setRotation(rot.x, THREE.MathUtils.degToRad(value), rot.z);
                        this.updateStatus();
                    }
                });

                // 动画参数滑块
                const swayIntensitySlider = document.getElementById('swayIntensitySlider');
                const swayIntensityValue = document.getElementById('swayIntensityValue');
                swayIntensitySlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    swayIntensityValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setSwayIntensity(value);
                    }
                });

                const swaySpeedSlider = document.getElementById('swaySpeedSlider');
                const swaySpeedValue = document.getElementById('swaySpeedValue');
                swaySpeedSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    swaySpeedValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setSwaySpeed(value);
                    }
                });

                const windStrengthSlider = document.getElementById('windStrengthSlider');
                const windStrengthValue = document.getElementById('windStrengthValue');
                windStrengthSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    windStrengthValue.textContent = value.toFixed(1);
                    if (this.tree) {
                        this.tree.setWindStrength(value);
                    }
                });

                // 色温调整滑块
                const colorWarmthSlider = document.getElementById('colorWarmthSlider');
                const colorWarmthValue = document.getElementById('colorWarmthValue');
                colorWarmthSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    colorWarmthValue.textContent = value.toFixed(0);
                    if (this.tree) {
                        this.tree.setColorWarmth(value);
                        this.updateStatus();
                    }
                });

                // 重置颜色按钮
                document.getElementById('resetColorBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.resetColor();
                        colorWarmthSlider.value = '0';
                        colorWarmthValue.textContent = '0';
                        this.updateStatus();
                    }
                });

                // 调试按钮
                document.getElementById('debugBtn').addEventListener('click', () => {
                    if (this.tree) {
                        this.tree.debugMaterials();
                    }
                });
            }

            resetTree() {
                if (!this.tree) return;

                // 重置树的状态和参数
                this.tree.setState(Tree04State.STATIC);
                this.tree.setPosition(0, 0, 0);
                this.tree.setRotation(0, 0, 0);
                this.tree.setScale(0.02);
                this.tree.setSwayIntensity(0.5);
                this.tree.setSwaySpeed(1.0);
                this.tree.setWindStrength(1.0);

                // 重置滑块值
                document.getElementById('scaleSlider').value = 0.02;
                document.getElementById('scaleValue').textContent = '0.020';
                document.getElementById('posXSlider').value = 0;
                document.getElementById('posXValue').textContent = '0.0';
                document.getElementById('posYSlider').value = 0;
                document.getElementById('posYValue').textContent = '0.0';
                document.getElementById('posZSlider').value = 0;
                document.getElementById('posZValue').textContent = '0.0';
                document.getElementById('rotYSlider').value = 0;
                document.getElementById('rotYValue').textContent = '0°';
                document.getElementById('colorWarmthSlider').value = 0;
                document.getElementById('colorWarmthValue').textContent = '0';

                // 重置色温
                this.tree.resetColor();

                this.updateStatus();
            }

            updateStatus() {
                if (!this.tree) return;

                const status = this.tree.getStatus();
                const position = status.position;
                const rotation = status.rotation;

                // 更新状态面板
                document.getElementById('nameStatus').textContent = status.name;
                document.getElementById('loadedStatus').textContent = status.loaded ? '是' : '否';
                document.getElementById('currentStateStatus').textContent = status.state;
                document.getElementById('positionStatus').textContent =
                    `(${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`;
                document.getElementById('rotationStatus').textContent =
                    `(${rotation.x.toFixed(2)}, ${rotation.y.toFixed(2)}, ${rotation.z.toFixed(2)})`;
                document.getElementById('scaleStatus').textContent = status.scale.toFixed(3);
                document.getElementById('colorWarmthStatus').textContent = status.colorWarmth.toFixed(0);

                // 更新控制面板中的状态信息
                const currentStateEl = document.getElementById('currentState');
                const currentPositionEl = document.getElementById('currentPosition');
                const currentScaleEl = document.getElementById('currentScale');
                const currentWarmthEl = document.getElementById('currentWarmth');

                if (currentStateEl) currentStateEl.textContent = status.state;
                if (currentPositionEl) currentPositionEl.textContent =
                    `(${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`;
                if (currentScaleEl) currentScaleEl.textContent = status.scale.toFixed(3);
                if (currentWarmthEl) currentWarmthEl.textContent = status.colorWarmth.toFixed(0);
            }

            updateStateButtons(currentState) {
                // 移除所有按钮的active类
                document.querySelectorAll('#controls button').forEach(btn => {
                    btn.classList.remove('active');
                });

                // 为当前状态的按钮添加active类
                switch (currentState) {
                    case Tree04State.STATIC:
                        document.getElementById('staticBtn').classList.add('active');
                        break;
                    case Tree04State.SWAYING:
                        document.getElementById('swayingBtn').classList.add('active');
                        break;
                    case Tree04State.WINDY:
                        document.getElementById('windyBtn').classList.add('active');
                        break;
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 更新树的动画
                if (this.tree) {
                    this.tree.update();
                }

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.tree) {
                    this.tree.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        // 创建应用实例
        const app = new TreeApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
