<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ground Main Model - 主地面模型展示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #8B7355 0%, #A0522D 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #D2B48C;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #D2B48C;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #D2B48C;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #8B7355, #A0522D);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #A0522D, #8B7355);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);
        }

        button.secondary {
            background: linear-gradient(135deg, #696969, #555555);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(135deg, #555555, #696969);
            transform: translateY(-2px);
        }

        .slider-container {
            margin-bottom: 12px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #BDC3C7;
            font-size: 12px;
        }

        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #34495E;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-container input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #D2B48C;
            cursor: pointer;
            border: 2px solid #8B7355;
        }

        .slider-container input[type="color"] {
            width: 100%;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: transparent;
        }

        .value-display {
            color: #D2B48C;
            font-weight: bold;
            font-size: 11px;
            float: right;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #BDC3C7;
        }

        .status-value {
            color: #D2B48C;
            font-weight: bold;
        }

        .status-success {
            color: #27AE60;
        }

        .status-error {
            color: #E74C3C;
        }

        .status-warning {
            color: #F39C12;
        }

        .visibility-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .visibility-toggle input[type="checkbox"] {
            transform: scale(1.2);
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            color: white;
            z-index: 1000;
            display: none;
        }

        .spinner {
            border: 4px solid rgba(210, 180, 140, 0.3);
            border-top: 4px solid #D2B48C;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                width: calc(100vw - 40px);
                position: relative;
                top: 10px;
                left: 20px;
                right: 20px;
                margin-bottom: 20px;
            }
            
            #status {
                top: 0;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <!-- 左侧控制面板 -->
        <div id="controls">
            <h1>🏔️ 主地面模型控制</h1>
            
            <div class="control-group">
                <h4>🎮 基础控制</h4>
                <div class="button-row">
                    <button class="primary" id="loadBtn">重新加载</button>
                    <button class="secondary" id="resetBtn">重置视角</button>
                </div>
                <div class="button-row">
                    <button class="secondary" id="debugBtn">调试信息</button>
                    <button class="secondary" id="resetModelBtn">重置模型</button>
                </div>
            </div>

            <div class="control-group">
                <h4>👁️ 可见性控制</h4>
                <div class="visibility-toggle">
                    <input type="checkbox" id="mainVisibility" checked>
                    <label for="mainVisibility">显示主地面</label>
                </div>
            </div>

            <div class="control-group">
                <h4>📐 变换控制</h4>
                <div class="slider-container">
                    <label for="positionXSlider">X位置: <span id="positionXValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionXSlider" min="-10" max="10" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="positionYSlider">Y位置: <span id="positionYValue" class="value-display">0.01</span></label>
                    <input type="range" id="positionYSlider" min="-5" max="5" step="0.01" value="0.01">
                </div>
                <div class="slider-container">
                    <label for="positionZSlider">Z位置: <span id="positionZValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionZSlider" min="-10" max="10" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="scaleSlider">缩放: <span id="scaleValue" class="value-display">1.0</span></label>
                    <input type="range" id="scaleSlider" min="0.1" max="5.0" step="0.1" value="1.0">
                </div>
                <div class="slider-container">
                    <label for="rotationXSlider">X轴旋转: <span id="rotationXValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationXSlider" min="0" max="360" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationYSlider">Y轴旋转: <span id="rotationYValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationYSlider" min="0" max="360" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationZSlider">Z轴旋转: <span id="rotationZValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationZSlider" min="0" max="360" step="5" value="0">
                </div>
            </div>

            <div class="control-group">
                <h4>🎨 材质控制</h4>
                <div class="slider-container">
                    <label for="colorPicker">主颜色</label>
                    <input type="color" id="colorPicker" value="#8B7355">
                </div>
                <div class="slider-container">
                    <label for="roughnessSlider">粗糙度: <span id="roughnessValue" class="value-display">1.0</span></label>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.05" value="1.0">
                </div>
                <div class="slider-container">
                    <label for="metalnessSlider">金属度: <span id="metalnessValue" class="value-display">0.0</span></label>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.05" value="0.0">
                </div>
            </div>
        </div>

        <!-- 右侧状态面板 -->
        <div id="status">
            <h1>📊 模型状态</h1>

            <div class="control-group">
                <h4>🔄 加载状态</h4>
                <div class="status-item">
                    <span class="status-label">模型状态:</span>
                    <span id="modelStatus" class="status-value">未加载</span>
                </div>
                <div class="status-item">
                    <span class="status-label">网格名称:</span>
                    <span id="meshName" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">顶点数:</span>
                    <span id="vertexCount" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">三角形数:</span>
                    <span id="triangleCount" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">贴图状态:</span>
                    <span id="textureStatus" class="status-value">-</span>
                </div>
            </div>

            <div class="control-group">
                <h4>🎨 材质信息</h4>
                <div class="status-item">
                    <span class="status-label">当前颜色:</span>
                    <span id="currentColor" class="status-value">#8B7355</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前粗糙度:</span>
                    <span id="currentRoughness" class="status-value">1.0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前金属度:</span>
                    <span id="currentMetalness" class="status-value">0.0</span>
                </div>
            </div>

            <div class="control-group">
                <h4>📐 变换信息</h4>
                <div class="status-item">
                    <span class="status-label">位置:</span>
                    <span id="currentPosition" class="status-value">(0, 0, 0)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">旋转:</span>
                    <span id="currentRotation" class="status-value">(0°, 0°, 0°)</span>
                </div>
                <div class="status-item">
                    <span class="status-label">缩放:</span>
                    <span id="currentScale" class="status-value">1.0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">可见性:</span>
                    <span id="currentVisibility" class="status-value">显示</span>
                </div>
            </div>

            <div class="control-group">
                <h4>🎮 相机信息</h4>
                <div class="status-item">
                    <span class="status-label">相机位置:</span>
                    <span id="cameraPosition" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">相机目标:</span>
                    <span id="cameraTarget" class="status-value">-</span>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading">
            <div class="spinner"></div>
            <div>正在加载主地面模型...</div>
        </div>
    </div>

    <!-- Three.js 和相关库 -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.178.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.178.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { GroundMainModel } from '../src/entities/GroundMainModel.js';

        class GroundMainModelViewer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.groundMain = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 3, 5);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;

                document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.target.set(0, 0, 0);

                // 添加光照 - 精确复现Cocos项目光照配置
                this.addLighting();

                // 添加地面
                this.addGround();

                // 开始渲染循环
                this.animate();

                // 监听窗口大小变化
                window.addEventListener('resize', () => this.onWindowResize());

                console.log('✅ GroundMainModelViewer 初始化完成');
            }

            addLighting() {
                // 环境光 - 精确复现Cocos项目配置
                // Cocos环境光: RGB(255, 255, 255) 强度0.52
                const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.52);
                this.scene.add(ambientLight);

                // 方向光 - 精确复现Cocos项目配置
                // Cocos方向光: RGB(255, 248, 233) 强度1.69
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                console.log('✅ Cocos项目光照配置已应用');
            }

            addGround() {
                // 地面 - 精确复现Cocos项目地面反照率配置
                // 地面反照率（LDR）: (0.618, 0.578, 0.545, 0) - 暖黄色调
                // 转换为RGB: (157, 147, 139) = 0x9D938B
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x9D938B,  // Cocos项目地面反照率精确颜色
                    transparent: false
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async loadGroundMainModel() {
                try {
                    // 如果已经加载过，先清理旧的模型
                    if (this.groundMain) {
                        console.log('🔄 清理旧的主地面模型...');
                        this.groundMain.dispose();
                        this.groundMain = null;
                    }

                    this.showLoading(true);

                    // 创建GroundMainModel实例
                    this.groundMain = new GroundMainModel(this.scene);

                    // 设置事件回调
                    this.groundMain.setEventCallbacks({
                        onLoadComplete: () => {
                            console.log('✅ 主地面模型加载完成');
                            this.updateModelStatus();
                            this.showLoading(false);
                        },
                        onMaterialApplied: () => {
                            console.log('✅ 主地面材质应用完成');
                            this.updateMaterialStatus();
                        }
                    });

                    // 初始化模型
                    await this.groundMain.initialize();

                } catch (error) {
                    console.error('❌ 主地面模型加载失败:', error);
                    this.showLoading(false);
                    alert('主地面模型加载失败: ' + error.message);
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                this.controls.update();
                this.renderer.render(this.scene, this.camera);

                // 更新相机状态显示
                this.updateCameraStatus();
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            showLoading(show) {
                document.getElementById('loading').style.display = show ? 'block' : 'none';
            }

            updateModelStatus() {
                if (!this.groundMain) {
                    document.getElementById('modelStatus').textContent = '未加载';
                    document.getElementById('meshName').textContent = '-';
                    document.getElementById('vertexCount').textContent = '-';
                    document.getElementById('triangleCount').textContent = '-';
                    document.getElementById('textureStatus').textContent = '-';
                    return;
                }

                const stats = this.groundMain.getModelStats();

                document.getElementById('modelStatus').textContent = stats.isLoaded ? '已加载' : '未加载';
                document.getElementById('modelStatus').className = stats.isLoaded ? 'status-value status-success' : 'status-value status-error';

                document.getElementById('meshName').textContent = stats.meshName || '-';
                document.getElementById('vertexCount').textContent = stats.vertices.toLocaleString();
                document.getElementById('triangleCount').textContent = stats.triangles.toLocaleString();
                document.getElementById('textureStatus').textContent = stats.hasTexture ? '已加载' : '无贴图';
                document.getElementById('textureStatus').className = stats.hasTexture ? 'status-value status-success' : 'status-value status-warning';
            }

            updateMaterialStatus() {
                if (!this.groundMain) return;

                const stats = this.groundMain.getModelStats();

                document.getElementById('currentColor').textContent = stats.materialProperties.color;
                document.getElementById('currentRoughness').textContent = stats.materialProperties.roughness.toFixed(2);
                document.getElementById('currentMetalness').textContent = stats.materialProperties.metalness.toFixed(2);
            }

            updateTransformStatus() {
                if (!this.groundMain) return;

                const config = this.groundMain.getConfig();

                document.getElementById('currentPosition').textContent =
                    `(${config.position.x.toFixed(1)}, ${config.position.y.toFixed(1)}, ${config.position.z.toFixed(1)})`;

                const rotationDeg = {
                    x: (config.rotation.x * 180 / Math.PI).toFixed(0),
                    y: (config.rotation.y * 180 / Math.PI).toFixed(0),
                    z: (config.rotation.z * 180 / Math.PI).toFixed(0)
                };
                document.getElementById('currentRotation').textContent =
                    `(${rotationDeg.x}°, ${rotationDeg.y}°, ${rotationDeg.z}°)`;

                document.getElementById('currentScale').textContent = config.scale.toFixed(1);

                const mainMesh = this.groundMain.getMainMesh();
                document.getElementById('currentVisibility').textContent =
                    (mainMesh && mainMesh.visible) ? '显示' : '隐藏';
            }

            updateCameraStatus() {
                const pos = this.camera.position;
                const target = this.controls.target;

                document.getElementById('cameraPosition').textContent =
                    `(${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)})`;
                document.getElementById('cameraTarget').textContent =
                    `(${target.x.toFixed(1)}, ${target.y.toFixed(1)}, ${target.z.toFixed(1)})`;
            }

            resetCamera() {
                this.camera.position.set(5, 3, 5);
                this.controls.reset();
                console.log('📷 相机视角已重置');
            }

            debugMaterials() {
                if (this.groundMain) {
                    this.groundMain.debugMaterials();
                } else {
                    console.log('❌ 主地面模型未加载');
                }
            }

            resetModel() {
                if (this.groundMain) {
                    this.groundMain.reset();
                    this.updateModelStatus();
                    this.updateMaterialStatus();
                    this.updateTransformStatus();

                    // 重置UI控件
                    this.resetUIControls();

                    console.log('🔄 主地面模型已重置');
                } else {
                    console.log('❌ 主地面模型未加载');
                }
            }

            resetUIControls() {
                // 重置变换控件
                document.getElementById('positionXSlider').value = '0';
                document.getElementById('positionYSlider').value = '0.01';  // 与默认配置保持一致
                document.getElementById('positionZSlider').value = '0';
                document.getElementById('scaleSlider').value = '1.0';
                document.getElementById('rotationXSlider').value = '0';
                document.getElementById('rotationYSlider').value = '0';
                document.getElementById('rotationZSlider').value = '0';

                // 重置材质控件
                document.getElementById('colorPicker').value = '#8B7355';
                document.getElementById('roughnessSlider').value = '1.0';
                document.getElementById('metalnessSlider').value = '0.0';

                // 重置可见性控件
                document.getElementById('mainVisibility').checked = true;

                // 更新显示值
                this.updateSliderDisplays();
            }

            updateSliderDisplays() {
                // 更新位置显示
                document.getElementById('positionXValue').textContent = parseFloat(document.getElementById('positionXSlider').value).toFixed(1);
                document.getElementById('positionYValue').textContent = parseFloat(document.getElementById('positionYSlider').value).toFixed(2);  // Y位置需要更高精度
                document.getElementById('positionZValue').textContent = parseFloat(document.getElementById('positionZSlider').value).toFixed(1);

                // 更新缩放显示
                document.getElementById('scaleValue').textContent = parseFloat(document.getElementById('scaleSlider').value).toFixed(1);

                // 更新旋转显示
                document.getElementById('rotationXValue').textContent = document.getElementById('rotationXSlider').value + '°';
                document.getElementById('rotationYValue').textContent = document.getElementById('rotationYSlider').value + '°';
                document.getElementById('rotationZValue').textContent = document.getElementById('rotationZSlider').value + '°';

                // 更新材质显示
                document.getElementById('roughnessValue').textContent = parseFloat(document.getElementById('roughnessSlider').value).toFixed(2);
                document.getElementById('metalnessValue').textContent = parseFloat(document.getElementById('metalnessSlider').value).toFixed(2);
            }

            setupEventListeners() {
                // 基础控制按钮
                document.getElementById('loadBtn').addEventListener('click', () => {
                    this.loadGroundMainModel();
                });

                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetCamera();
                });

                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.debugMaterials();
                });

                document.getElementById('resetModelBtn').addEventListener('click', () => {
                    this.resetModel();
                });

                // 可见性控制
                document.getElementById('mainVisibility').addEventListener('change', (e) => {
                    if (this.groundMain) {
                        this.groundMain.setVisibility(e.target.checked);
                        this.updateTransformStatus();
                    }
                });

                // 位置控制
                document.getElementById('positionXSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionXValue').textContent = value.toFixed(1);
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        this.groundMain.setPosition(new THREE.Vector3(value, config.position.y, config.position.z));
                        this.updateTransformStatus();
                    }
                });

                document.getElementById('positionYSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionYValue').textContent = value.toFixed(1);
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        this.groundMain.setPosition(new THREE.Vector3(config.position.x, value, config.position.z));
                        this.updateTransformStatus();
                    }
                });

                document.getElementById('positionZSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionZValue').textContent = value.toFixed(1);
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        this.groundMain.setPosition(new THREE.Vector3(config.position.x, config.position.y, value));
                        this.updateTransformStatus();
                    }
                });

                // 缩放控制
                document.getElementById('scaleSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('scaleValue').textContent = value.toFixed(1);
                    if (this.groundMain) {
                        this.groundMain.setScale(value);
                        this.updateTransformStatus();
                    }
                });

                // 旋转控制
                document.getElementById('rotationXSlider').addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    document.getElementById('rotationXValue').textContent = value + '°';
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        const radians = value * Math.PI / 180;
                        this.groundMain.setRotation(new THREE.Euler(radians, config.rotation.y, config.rotation.z));
                        this.updateTransformStatus();
                    }
                });

                document.getElementById('rotationYSlider').addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    document.getElementById('rotationYValue').textContent = value + '°';
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        const radians = value * Math.PI / 180;
                        this.groundMain.setRotation(new THREE.Euler(config.rotation.x, radians, config.rotation.z));
                        this.updateTransformStatus();
                    }
                });

                document.getElementById('rotationZSlider').addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    document.getElementById('rotationZValue').textContent = value + '°';
                    if (this.groundMain) {
                        const config = this.groundMain.getConfig();
                        const radians = value * Math.PI / 180;
                        this.groundMain.setRotation(new THREE.Euler(config.rotation.x, config.rotation.y, radians));
                        this.updateTransformStatus();
                    }
                });

                // 材质控制
                document.getElementById('colorPicker').addEventListener('input', (e) => {
                    if (this.groundMain) {
                        const color = new THREE.Color(e.target.value);
                        this.groundMain.updateColor(color);
                        this.updateMaterialStatus();
                    }
                });

                document.getElementById('roughnessSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('roughnessValue').textContent = value.toFixed(2);
                    if (this.groundMain) {
                        this.groundMain.updateRoughness(value);
                        this.updateMaterialStatus();
                    }
                });

                document.getElementById('metalnessSlider').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('metalnessValue').textContent = value.toFixed(2);
                    if (this.groundMain) {
                        this.groundMain.updateMetalness(value);
                        this.updateMaterialStatus();
                    }
                });

                // 初始化显示值
                this.updateSliderDisplays();
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.groundMain) {
                    this.groundMain.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('🗑️ GroundMainModelViewer 资源已清理');
            }
        }

        // 全局viewer实例
        let globalViewer = null;

        // 页面加载完成后初始化
        window.addEventListener('load', async () => {
            globalViewer = new GroundMainModelViewer();

            // 自动加载主地面模型
            try {
                await globalViewer.loadGroundMainModel();
                console.log('✅ 主地面模型自动加载完成');
            } catch (error) {
                console.error('❌ 主地面模型自动加载失败:', error);
            }

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                if (globalViewer) {
                    globalViewer.dispose();
                }
            });
        });
    </script>
</body>
</html>
