<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meat Perfect Stacking Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 280px;
            max-width: 320px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(15px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 250px;
            max-width: 300px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(15px);
        }

        #controls h3, #status h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            margin: 0 0 12px 0;
            color: #f39c12;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 100px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .primary {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }

        .warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .info {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .slider-control {
            margin-bottom: 15px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 8px;
            color: #ecf0f1;
            font-size: 13px;
            font-weight: 500;
        }

        .slider-control input[type="range"] {
            width: 100%;
            margin-bottom: 8px;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: linear-gradient(to right, #3498db, #2980b9);
            outline: none;
        }

        .slider-control input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .value-display {
            font-size: 12px;
            color: #f39c12;
            text-align: center;
            background: rgba(243, 156, 18, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 14px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            color: #bdc3c7;
            font-weight: 500;
        }

        .status-value {
            color: #fff;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            padding: 40px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #e74c3c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .layer-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #95a5a6;
        }

        .layer-indicator.completed {
            background: #27ae60;
        }

        .layer-indicator.active {
            background: #f39c12;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar,
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track,
        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb,
        #status::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover,
        #status::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                position: relative;
                top: auto;
                left: auto;
                right: auto;
                width: calc(100% - 20px);
                margin: 10px;
                max-height: 300px;
            }

            body {
                overflow-y: auto;
            }

            #container {
                height: auto;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }

            canvas {
                order: 3;
                height: 60vh !important;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Meat Perfect Stacking System...</div>
            <div style="font-size: 12px; margin-top: 10px; color: #bdc3c7;">
                Initializing models and textures...
            </div>
        </div>

        <div id="controls">
            <h3>🥩 肉块完美堆叠控制</h3>
            
            <div class="control-group">
                <h4>主要操作</h4>
                <div class="button-row">
                    <button id="startStackingBtn" class="success">开始堆叠</button>
                    <button id="restartBtn" class="warning">重新开始</button>
                </div>
                <div class="button-row">
                    <button id="pauseBtn" class="info">暂停/继续</button>
                    <button id="clearBtn" class="danger">清空所有</button>
                </div>
            </div>

            <div class="control-group">
                <h4>堆叠配置</h4>
                <div class="slider-control">
                    <label for="layersSlider">层数：</label>
                    <input type="range" id="layersSlider" min="1" max="15" step="1" value="12">
                    <div class="value-display" id="layersValue">12</div>
                </div>
                <div class="slider-control">
                    <label for="itemsPerLayerSlider">每排数量：</label>
                    <input type="range" id="itemsPerLayerSlider" min="1" max="8" step="1" value="1">
                    <div class="value-display" id="itemsPerLayerValue">1</div>
                </div>
                <div class="slider-control">
                    <label for="layerHeightSlider">层间距：</label>
                    <input type="range" id="layerHeightSlider" min="0.05" max="0.2" step="0.005" value="0.08">
                    <div class="value-display" id="layerHeightValue">0.08</div>
                </div>
                <div class="slider-control">
                    <label for="spreadRadiusSlider">排列间距：</label>
                    <input type="range" id="spreadRadiusSlider" min="0.8" max="3.0" step="0.1" value="1.0">
                    <div class="value-display" id="spreadRadiusValue">1.0</div>
                </div>
                <div class="slider-control">
                    <label for="stackingSpeedSlider">堆叠速度：</label>
                    <input type="range" id="stackingSpeedSlider" min="0.5" max="5.0" step="0.1" value="2.0">
                    <div class="value-display" id="stackingSpeedValue">2.0</div>
                </div>
            </div>

            <div class="control-group">
                <h4>效果选项</h4>
                <div style="margin-bottom: 10px;">
                    <label>
                        <input type="checkbox" id="randomOffsetCheck">
                        随机偏移
                    </label>
                </div>
                <div style="margin-bottom: 10px;">
                    <label>
                        <input type="checkbox" id="animateStackingCheck" checked>
                        播放动画
                    </label>
                </div>
            </div>

            <div class="control-group">
                <h4>手动添加</h4>
                <div class="button-row">
                    <button id="addRandomMeatBtn" class="info">添加随机肉块</button>
                </div>
            </div>
        </div>

        <div id="status">
            <h3>📊 堆叠状态</h3>
            
            <div class="status-item">
                <span class="status-label">总肉块数:</span>
                <span class="status-value" id="totalMeatStatus">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">已完成:</span>
                <span class="status-value" id="completedMeatStatus">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">正在堆叠:</span>
                <span class="status-value" id="isStackingStatus">否</span>
            </div>
            
            <div style="margin: 15px 0;">
                <div class="status-label" style="margin-bottom: 8px;">进度:</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="status-value" id="progressText">0%</div>
            </div>

            <div style="margin-top: 20px;">
                <div class="status-label" style="margin-bottom: 10px;">层状态:</div>
                <div id="layerStatus"></div>
            </div>

            <div style="margin-top: 20px;">
                <div class="status-label" style="margin-bottom: 10px;">配置信息:</div>
                <div class="status-item">
                    <span class="status-label">层数:</span>
                    <span class="status-value" id="configLayers">12</span>
                </div>
                <div class="status-item">
                    <span class="status-label">每排数量:</span>
                    <span class="status-value" id="configItemsPerLayer">1</span>
                </div>
                <div class="status-item">
                    <span class="status-label">层间距:</span>
                    <span class="status-value" id="configLayerHeight">0.08</span>
                </div>
                <div class="status-item">
                    <span class="status-label">排列间距:</span>
                    <span class="status-value" id="configSpreadRadius">1.0</span>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { MeatStackingDemo } from '../src/examples/meat-stacking.js';

        class StackingApp {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.stackingDemo = null;
                this.updateInterval = null;
                
                this.init();
            }

            async init() {
                // 设置渲染器
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x2c3e50, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.2;
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 设置相机位置
                this.camera.position.set(3, 4, 5);
                this.camera.lookAt(0, 0, 0);

                // 添加光照
                this.setupLights();

                // 添加地面
                this.addGround();

                // 创建堆叠系统 - 单列完全紧密堆叠配置
                this.stackingDemo = new MeatStackingDemo(this.scene, {
                    layers: 12,          // 12层堆叠，更高的塔
                    itemsPerLayer: 1,    // 单列堆叠
                    layerHeight: 0.06,   // 极小的层间距，完全紧贴
                    spreadRadius: 1.0,   // 排列间距
                    randomOffset: false, // 关闭随机偏移，保持完全对齐
                    animateStacking: true,
                    stackingSpeed: 1.2 // 更慢的速度，让抛物线轨迹更明显
                });

                // 设置事件回调
                this.stackingDemo.setEventCallbacks({
                    onStackingComplete: () => {
                        console.log('🎉 堆叠完成!');
                        this.updateControls();
                    },
                    onLayerComplete: (layer) => {
                        console.log(`✅ 第 ${layer + 1} 层完成`);
                        this.updateLayerStatus();
                    },
                    onMeatPlaced: (instance) => {
                        // 可以添加放置音效或粒子效果
                    }
                });

                // 设置控制事件
                this.setupControls();

                // 开始渲染循环
                this.animate();

                // 开始状态更新
                this.startStatusUpdates();

                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            }

            setupLights() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);

                // 主方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
                directionalLight.position.set(10, 15, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 补充光
                const fillLight = new THREE.DirectionalLight(0x4fc3f7, 0.3);
                fillLight.position.set(-5, 8, -3);
                this.scene.add(fillLight);

                // 顶部光
                const topLight = new THREE.PointLight(0xffffff, 0.5, 10);
                topLight.position.set(0, 8, 0);
                this.scene.add(topLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0x8b7355,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);

                // 添加网格辅助线
                const gridHelper = new THREE.GridHelper(10, 20, 0x666666, 0x333333);
                gridHelper.position.y = -0.49;
                this.scene.add(gridHelper);
            }

            setupControls() {
                // 主要操作按钮
                document.getElementById('startStackingBtn').addEventListener('click', async () => {
                    await this.startStacking();
                });

                document.getElementById('restartBtn').addEventListener('click', async () => {
                    await this.restartStacking();
                });

                document.getElementById('pauseBtn').addEventListener('click', () => {
                    this.stackingDemo.toggleStackingAnimation();
                });

                document.getElementById('clearBtn').addEventListener('click', () => {
                    this.clearAll();
                });

                document.getElementById('addRandomMeatBtn').addEventListener('click', async () => {
                    await this.addRandomMeat();
                });

                // 配置滑块
                this.setupConfigSliders();

                // 复选框
                document.getElementById('randomOffsetCheck').addEventListener('change', (e) => {
                    this.updateConfig({ randomOffset: e.target.checked });
                });

                document.getElementById('animateStackingCheck').addEventListener('change', (e) => {
                    this.updateConfig({ animateStacking: e.target.checked });
                });

                // 窗口调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            setupConfigSliders() {
                const sliders = [
                    { id: 'layers', property: 'layers', min: 1, max: 5 },
                    { id: 'itemsPerLayer', property: 'itemsPerLayer', min: 3, max: 12 },
                    { id: 'layerHeight', property: 'layerHeight', min: 0.1, max: 0.8 },
                    { id: 'spreadRadius', property: 'spreadRadius', min: 0.5, max: 2.5 },
                    { id: 'stackingSpeed', property: 'stackingSpeed', min: 0.5, max: 5.0 }
                ];

                sliders.forEach(({ id, property }) => {
                    const slider = document.getElementById(`${id}Slider`);
                    const value = document.getElementById(`${id}Value`);
                    
                    slider.addEventListener('input', () => {
                        const val = parseFloat(slider.value);
                        value.textContent = val.toFixed(property === 'layers' || property === 'itemsPerLayer' ? 0 : 1);
                        this.updateConfig({ [property]: val });
                    });
                });
            }

            async startStacking() {
                console.log('开始肉块堆叠...');
                this.updateControls(true);
                
                try {
                    await this.stackingDemo.initializeStacking();
                    console.log('堆叠初始化完成');
                } catch (error) {
                    console.error('堆叠初始化失败:', error);
                } finally {
                    this.updateControls(false);
                }
            }

            async restartStacking() {
                console.log('重新开始堆叠...');
                this.updateControls(true);
                
                try {
                    await this.stackingDemo.restartStacking();
                } catch (error) {
                    console.error('重新堆叠失败:', error);
                } finally {
                    this.updateControls(false);
                }
            }

            clearAll() {
                this.stackingDemo.dispose();
                this.stackingDemo = new MeatStackingDemo(this.scene);
                this.stackingDemo.setEventCallbacks({
                    onStackingComplete: () => {
                        console.log('🎉 堆叠完成!');
                        this.updateControls();
                    },
                    onLayerComplete: (layer) => {
                        console.log(`✅ 第 ${layer + 1} 层完成`);
                        this.updateLayerStatus();
                    }
                });
                this.updateStatus();
            }

            async addRandomMeat() {
                const x = (Math.random() - 0.5) * 4;
                const z = (Math.random() - 0.5) * 4;
                const y = Math.random() * 2;
                
                await this.stackingDemo.addMeatAtPosition(new THREE.Vector3(x, y, z));
            }

            updateConfig(newConfig) {
                this.stackingDemo.updateConfig(newConfig);
                this.updateConfigDisplay();
            }

            updateConfigDisplay() {
                const config = this.stackingDemo.getConfig();
                document.getElementById('configLayers').textContent = config.layers;
                document.getElementById('configItemsPerLayer').textContent = config.itemsPerLayer;
                document.getElementById('configLayerHeight').textContent = config.layerHeight.toFixed(1);
                document.getElementById('configSpreadRadius').textContent = config.spreadRadius.toFixed(1);
            }

            updateControls(loading = false) {
                document.getElementById('startStackingBtn').disabled = loading;
                document.getElementById('restartBtn').disabled = loading;
            }

            startStatusUpdates() {
                this.updateInterval = setInterval(() => {
                    this.updateStatus();
                }, 100);
            }

            updateStatus() {
                if (!this.stackingDemo) return;

                const status = this.stackingDemo.getStackingStatus();
                
                document.getElementById('totalMeatStatus').textContent = status.total;
                document.getElementById('completedMeatStatus').textContent = status.completed;
                document.getElementById('isStackingStatus').textContent = status.isStacking ? '是' : '否';
                
                const progress = Math.round(status.progress * 100);
                document.getElementById('progressFill').style.width = `${progress}%`;
                document.getElementById('progressText').textContent = `${progress}%`;
                
                this.updateLayerStatus();
                this.updateConfigDisplay();
            }

            updateLayerStatus() {
                if (!this.stackingDemo) return;

                const config = this.stackingDemo.getConfig();
                const instances = this.stackingDemo.getMeatInstances();
                const layerStatusEl = document.getElementById('layerStatus');
                
                let html = '';
                for (let i = 0; i < config.layers; i++) {
                    const layerInstances = instances.filter(inst => inst.layer === i);
                    const completed = layerInstances.filter(inst => !inst.isAnimating).length;
                    const total = layerInstances.length;
                    
                    let status = 'pending';
                    if (completed === total && total > 0) {
                        status = 'completed';
                    } else if (completed > 0) {
                        status = 'active';
                    }
                    
                    html += `<div style="margin-bottom: 8px;">
                        <span class="layer-indicator ${status}"></span>
                        第 ${i + 1} 层: ${completed}/${total}
                    </div>`;
                }
                
                layerStatusEl.innerHTML = html;
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                
                // 简单的相机旋转
                const time = Date.now() * 0.0005;
                this.camera.position.x = Math.cos(time) * 5;
                this.camera.position.z = Math.sin(time) * 5;
                this.camera.lookAt(0, 0, 0);
                
                this.renderer.render(this.scene, this.camera);
            }
        }

        // 启动应用
        new StackingApp();
    </script>
</body>
</html> 