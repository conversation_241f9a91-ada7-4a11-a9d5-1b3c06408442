<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ground02 Model - 3D模型展示系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            color: white;
        }

        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        #renderer {
            width: 100%;
            height: 100%;
            display: block;
        }

        /* 控制面板样式 */
        #controls {
            position: absolute;
            left: 20px;
            top: 20px;
            width: 320px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #D2B48C;
            backdrop-filter: blur(10px);
            z-index: 100;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        #controls h1 {
            color: #D2B48C;
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.4rem;
            text-shadow: 0 0 10px rgba(210, 180, 140, 0.5);
        }

        .section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(210, 180, 140, 0.3);
        }

        .section h3 {
            color: #D2B48C;
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 1px solid rgba(210, 180, 140, 0.3);
            padding-bottom: 8px;
        }

        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        button {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        button.primary {
            background: linear-gradient(45deg, #D2B48C, #F4E4BC);
            color: #8B4513;
        }

        button.primary:hover {
            background: linear-gradient(45deg, #F4E4BC, #D2B48C);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(210, 180, 140, 0.4);
        }

        button.secondary {
            background: linear-gradient(45deg, #708090, #A9A9A9);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(45deg, #A9A9A9, #708090);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(112, 128, 144, 0.4);
        }

        .slider-container {
            margin-bottom: 15px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 8px;
            color: #E6E6FA;
            font-weight: 500;
        }

        .value-display {
            color: #D2B48C;
            font-weight: bold;
            float: right;
        }

        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #D2B48C;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(210, 180, 140, 0.5);
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #D2B48C;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 10px rgba(210, 180, 140, 0.5);
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .color-picker-container label {
            color: #E6E6FA;
            font-weight: 500;
            flex: 1;
        }

        input[type="color"] {
            width: 50px;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: none;
        }

        /* 状态面板样式 */
        #status {
            position: absolute;
            right: 20px;
            top: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #D2B48C;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        #status h2 {
            color: #D2B48C;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            color: #E6E6FA;
            font-weight: 500;
        }

        .status-value {
            color: #D2B48C;
            font-weight: bold;
        }

        /* 加载动画 */
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            color: white;
            z-index: 1000;
            display: none;
        }

        .spinner {
            border: 4px solid rgba(210, 180, 140, 0.3);
            border-top: 4px solid #D2B48C;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                width: calc(100vw - 40px);
                position: relative;
                top: 10px;
                left: 20px;
                right: 20px;
                margin-bottom: 20px;
            }
            
            #status {
                top: 0;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="renderer"></canvas>
        
        <!-- 控制面板 -->
        <div id="controls">
            <h1>🗺️ Ground02 模型控制</h1>
            
            <!-- 基础控制 -->
            <div class="section">
                <h3>🎮 基础控制</h3>
                <div class="button-row">
                    <button class="primary" id="loadBtn">重新加载</button>
                    <button class="secondary" id="resetBtn">重置视角</button>
                </div>
            </div>

            <!-- 变换控制 -->
            <div class="section">
                <h3>📐 变换控制</h3>
                
                <!-- 位置控制 -->
                <div class="slider-container">
                    <label for="positionXSlider">X位置: <span id="positionXValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionXSlider" min="-10" max="10" step="0.1" value="0">
                </div>
                <div class="slider-container">
                    <label for="positionYSlider">Y位置: <span id="positionYValue" class="value-display">0.01</span></label>
                    <input type="range" id="positionYSlider" min="-5" max="5" step="0.01" value="0.01">
                </div>
                <div class="slider-container">
                    <label for="positionZSlider">Z位置: <span id="positionZValue" class="value-display">0.0</span></label>
                    <input type="range" id="positionZSlider" min="-10" max="10" step="0.1" value="0">
                </div>

                <!-- 缩放控制 -->
                <div class="slider-container">
                    <label for="scaleSlider">缩放: <span id="scaleValue" class="value-display">1.0</span></label>
                    <input type="range" id="scaleSlider" min="0.1" max="3.0" step="0.1" value="1.0">
                </div>

                <!-- 旋转控制 -->
                <div class="slider-container">
                    <label for="rotationXSlider">X旋转: <span id="rotationXValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationXSlider" min="-180" max="180" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationYSlider">Y旋转: <span id="rotationYValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationYSlider" min="-180" max="180" step="5" value="0">
                </div>
                <div class="slider-container">
                    <label for="rotationZSlider">Z旋转: <span id="rotationZValue" class="value-display">0°</span></label>
                    <input type="range" id="rotationZSlider" min="-180" max="180" step="5" value="0">
                </div>
            </div>

            <!-- 材质控制 -->
            <div class="section">
                <h3>🎨 材质控制</h3>
                
                <div class="color-picker-container">
                    <label for="colorPicker">基础颜色:</label>
                    <input type="color" id="colorPicker" value="#FFFFFF">
                </div>

                <div class="slider-container">
                    <label for="roughnessSlider">粗糙度: <span id="roughnessValue" class="value-display">1.00</span></label>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.01" value="1.0">
                </div>

                <div class="slider-container">
                    <label for="metalnessSlider">金属度: <span id="metalnessValue" class="value-display">0.00</span></label>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.01" value="0.0">
                </div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div id="status">
            <h2>📊 模型状态</h2>
            <div class="status-item">
                <span class="status-label">加载状态:</span>
                <span class="status-value" id="loadStatus">未加载</span>
            </div>
            <div class="status-item">
                <span class="status-label">网格名称:</span>
                <span class="status-value" id="meshName">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">顶点数:</span>
                <span class="status-value" id="vertexCount">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">三角形数:</span>
                <span class="status-value" id="triangleCount">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">纹理状态:</span>
                <span class="status-value" id="textureStatus">无</span>
            </div>
            <div class="status-item">
                <span class="status-label">材质颜色:</span>
                <span class="status-value" id="materialColor">#FFFFFF</span>
            </div>
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="materialRoughness">1.00</span>
            </div>
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="materialMetalness">0.00</span>
            </div>
        </div>

        <!-- 加载动画 -->
        <div id="loading">
            <div class="spinner"></div>
            <p>正在加载Ground02模型...</p>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { Ground02Model } from '../src/entities/Ground02Model.js';

        class Ground02ModelViewer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.ground02 = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);  // 天空蓝背景

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 5, 5);

                // 创建渲染器
                const canvas = document.getElementById('renderer');
                this.renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    alpha: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setPixelRatio(window.devicePixelRatio);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.target.set(0, 0, 0);

                // 添加光照 - 精确复现Cocos项目光照配置
                this.setupLighting();

                // 添加地面
                this.addGround();

                // 开始渲染循环
                this.animate();

                // 处理窗口大小变化
                window.addEventListener('resize', () => this.onWindowResize());

                console.log('✅ Ground02ModelViewer 初始化完成');
            }

            setupLighting() {
                // 环境光 - 精确复现Cocos项目配置
                // Cocos项目环境光: skyColor=0xFFFFFF, intensity=0.52
                const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.52);
                this.scene.add(ambientLight);

                // 主方向光 - 精确复现Cocos项目配置
                // Cocos项目主光源: color=0xFFF8E9, intensity=1.69, direction=(-0.4, -0.4, -0.2)
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(-4, 4, 2);  // 对应direction的反向
                directionalLight.target.position.set(0, 0, 0);
                directionalLight.castShadow = true;

                // 阴影配置
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;

                this.scene.add(directionalLight);
                this.scene.add(directionalLight.target);

                // 注意: 原Cocos项目使用单一主光源模型，不使用补充光源
                console.log('🎯 已应用Cocos项目精确光照配置');
            }

            addGround() {
                // 地面 - 精确复现Cocos项目地面反照率配置
                // 地面反照率（LDR）: (0.618, 0.578, 0.545, 0) - 暖黄色调
                // 转换为RGB: (157, 147, 139) = 0x9D938B
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x9D938B,  // Cocos项目地面反照率精确颜色
                    transparent: false
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async loadGround02Model() {
                try {
                    // 如果已经加载过，先清理旧的模型
                    if (this.ground02) {
                        console.log('🔄 清理旧的Ground02模型...');
                        this.ground02.dispose();
                        this.ground02 = null;
                    }

                    this.showLoading(true);

                    // 创建Ground02Model实例
                    this.ground02 = new Ground02Model(this.scene);

                    // 设置事件回调
                    this.ground02.setEventCallbacks({
                        onLoadComplete: () => {
                            console.log('✅ Ground02模型加载完成');
                            this.updateModelStatus();
                            this.showLoading(false);
                        },
                        onMaterialApplied: () => {
                            console.log('✅ Ground02材质应用完成');
                            this.updateMaterialStatus();
                        }
                    });

                    // 初始化模型
                    await this.ground02.initialize();

                } catch (error) {
                    console.error('❌ Ground02模型加载失败:', error);
                    this.showLoading(false);
                    alert('Ground02模型加载失败: ' + error.message);
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                if (this.controls) {
                    this.controls.update();
                }

                if (this.renderer && this.scene && this.camera) {
                    this.renderer.render(this.scene, this.camera);
                }
            }

            onWindowResize() {
                if (this.camera && this.renderer) {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                }
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                loading.style.display = show ? 'block' : 'none';
            }

            setupEventListeners() {
                // 基础控制按钮
                document.getElementById('loadBtn').addEventListener('click', () => {
                    this.loadGround02Model();
                });

                document.getElementById('resetBtn').addEventListener('click', () => {
                    if (this.controls) {
                        this.controls.reset();
                    }
                });

                // 位置控制
                document.getElementById('positionXSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const position = this.ground02.getPosition();
                        position.x = parseFloat(e.target.value);
                        this.ground02.setPosition(position);
                    }
                    this.updateSliderDisplays();
                });

                document.getElementById('positionYSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const position = this.ground02.getPosition();
                        position.y = parseFloat(e.target.value);
                        this.ground02.setPosition(position);
                    }
                    this.updateSliderDisplays();
                });

                document.getElementById('positionZSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const position = this.ground02.getPosition();
                        position.z = parseFloat(e.target.value);
                        this.ground02.setPosition(position);
                    }
                    this.updateSliderDisplays();
                });

                // 缩放控制
                document.getElementById('scaleSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        this.ground02.setScale(parseFloat(e.target.value));
                    }
                    this.updateSliderDisplays();
                });

                // 旋转控制
                document.getElementById('rotationXSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const rotation = this.ground02.getRotation();
                        rotation.x = THREE.MathUtils.degToRad(parseFloat(e.target.value));
                        this.ground02.setRotation(rotation);
                    }
                    this.updateSliderDisplays();
                });

                document.getElementById('rotationYSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const rotation = this.ground02.getRotation();
                        rotation.y = THREE.MathUtils.degToRad(parseFloat(e.target.value));
                        this.ground02.setRotation(rotation);
                    }
                    this.updateSliderDisplays();
                });

                document.getElementById('rotationZSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const rotation = this.ground02.getRotation();
                        rotation.z = THREE.MathUtils.degToRad(parseFloat(e.target.value));
                        this.ground02.setRotation(rotation);
                    }
                    this.updateSliderDisplays();
                });

                // 材质控制
                document.getElementById('colorPicker').addEventListener('input', (e) => {
                    if (this.ground02) {
                        const color = new THREE.Color(e.target.value);
                        this.ground02.updateMaterialColor(color);
                        this.updateMaterialStatus();
                    }
                });

                document.getElementById('roughnessSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        this.ground02.updateMaterialRoughness(parseFloat(e.target.value));
                        this.updateMaterialStatus();
                    }
                    this.updateSliderDisplays();
                });

                document.getElementById('metalnessSlider').addEventListener('input', (e) => {
                    if (this.ground02) {
                        this.ground02.updateMaterialMetalness(parseFloat(e.target.value));
                        this.updateMaterialStatus();
                    }
                    this.updateSliderDisplays();
                });

                // 初始化滑块显示
                this.updateSliderDisplays();
            }

            resetUIControls() {
                // 重置变换控件
                document.getElementById('positionXSlider').value = '0';
                document.getElementById('positionYSlider').value = '0.01';  // 与默认配置保持一致
                document.getElementById('positionZSlider').value = '0';
                document.getElementById('scaleSlider').value = '1.0';
                document.getElementById('rotationXSlider').value = '0';
                document.getElementById('rotationYSlider').value = '0';
                document.getElementById('rotationZSlider').value = '0';

                // 重置材质控件
                document.getElementById('colorPicker').value = '#FFFFFF';
                document.getElementById('roughnessSlider').value = '1.0';
                document.getElementById('metalnessSlider').value = '0.0';

                // 更新显示
                this.updateSliderDisplays();
            }

            updateSliderDisplays() {
                // 更新位置显示
                document.getElementById('positionXValue').textContent = parseFloat(document.getElementById('positionXSlider').value).toFixed(1);
                document.getElementById('positionYValue').textContent = parseFloat(document.getElementById('positionYSlider').value).toFixed(2);  // Y位置需要更高精度
                document.getElementById('positionZValue').textContent = parseFloat(document.getElementById('positionZSlider').value).toFixed(1);

                // 更新缩放显示
                document.getElementById('scaleValue').textContent = parseFloat(document.getElementById('scaleSlider').value).toFixed(1);

                // 更新旋转显示
                document.getElementById('rotationXValue').textContent = parseFloat(document.getElementById('rotationXSlider').value).toFixed(0) + '°';
                document.getElementById('rotationYValue').textContent = parseFloat(document.getElementById('rotationYSlider').value).toFixed(0) + '°';
                document.getElementById('rotationZValue').textContent = parseFloat(document.getElementById('rotationZSlider').value).toFixed(0) + '°';

                // 更新材质显示
                document.getElementById('roughnessValue').textContent = parseFloat(document.getElementById('roughnessSlider').value).toFixed(2);
                document.getElementById('metalnessValue').textContent = parseFloat(document.getElementById('metalnessSlider').value).toFixed(2);
            }

            updateModelStatus() {
                if (!this.ground02) {
                    document.getElementById('loadStatus').textContent = '未加载';
                    document.getElementById('meshName').textContent = '-';
                    document.getElementById('vertexCount').textContent = '0';
                    document.getElementById('triangleCount').textContent = '0';
                    document.getElementById('textureStatus').textContent = '无';
                    return;
                }

                const info = this.ground02.getModelInfo();
                document.getElementById('loadStatus').textContent = info.isLoaded ? '已加载' : '未加载';
                document.getElementById('meshName').textContent = info.meshName;
                document.getElementById('vertexCount').textContent = info.vertices.toLocaleString();
                document.getElementById('triangleCount').textContent = info.triangles.toLocaleString();
                document.getElementById('textureStatus').textContent = info.hasTexture ? '已应用' : '无';
            }

            updateMaterialStatus() {
                if (!this.ground02) {
                    document.getElementById('materialColor').textContent = '#FFFFFF';
                    document.getElementById('materialRoughness').textContent = '1.00';
                    document.getElementById('materialMetalness').textContent = '0.00';
                    return;
                }

                const info = this.ground02.getModelInfo();
                document.getElementById('materialColor').textContent = info.materialProperties.color;
                document.getElementById('materialRoughness').textContent = info.materialProperties.roughness.toFixed(2);
                document.getElementById('materialMetalness').textContent = info.materialProperties.metalness.toFixed(2);
            }

            dispose() {
                // 停止动画循环
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }

                // 清理Ground02模型
                if (this.ground02) {
                    this.ground02.dispose();
                    this.ground02 = null;
                }

                // 清理Three.js资源
                if (this.renderer) {
                    this.renderer.dispose();
                    this.renderer = null;
                }

                if (this.controls) {
                    this.controls.dispose();
                    this.controls = null;
                }

                this.scene = null;
                this.camera = null;

                console.log('🗑️ Ground02ModelViewer 资源已清理');
            }
        }

        // 全局viewer实例
        let globalViewer = null;

        // 页面加载完成后初始化
        window.addEventListener('load', async () => {
            globalViewer = new Ground02ModelViewer();

            // 自动加载Ground02模型
            try {
                await globalViewer.loadGround02Model();
                console.log('✅ Ground02模型自动加载完成');
            } catch (error) {
                console.error('❌ Ground02模型自动加载失败:', error);
            }

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                if (globalViewer) {
                    globalViewer.dispose();
                }
            });
        });
    </script>
</body>
</html>
