<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>熊模型动画控制模块 - Three.js 项目</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        overflow: hidden;
        background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 50%, #0f0f0f 100%);
        font-family: 'Arial', sans-serif;
      }
      
      #app {
        width: 100vw;
        height: 100vh;
        display: block;
      }
      
      #loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #cccccc;
        font-size: 18px;
        z-index: 100;
        text-align: center;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(204, 204, 204, 0.3);
        border-radius: 50%;
        border-top-color: #cccccc;
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto 15px;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      #info {
        position: absolute;
        top: 10px;
        left: 10px;
        color: #d4a574;
        font-size: 14px;
        z-index: 100;
        background: rgba(44, 24, 16, 0.9);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid rgba(212, 165, 116, 0.3);
        max-width: 300px;
        backdrop-filter: blur(5px);
      }

      #info h3 {
        color: #f4c595;
        margin-bottom: 10px;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      #info div {
        margin-bottom: 5px;
      }

      .info-section {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid rgba(212, 165, 116, 0.2);
        font-size: 12px;
        opacity: 0.8;
      }

      #back-button {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        padding: 10px 20px;
        background: linear-gradient(45deg, #d4a574, #b8935f);
        color: #2c1810;
        text-decoration: none;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: 1px solid rgba(212, 165, 116, 0.3);
      }

      #back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(212, 165, 116, 0.3);
        background: linear-gradient(45deg, #f4c595, #d4a574);
      }

      #error-message {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ff6b6b;
        font-size: 16px;
        z-index: 100;
        text-align: center;
        background: rgba(44, 24, 16, 0.9);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid rgba(255, 107, 107, 0.3);
        backdrop-filter: blur(5px);
        display: none;
      }

      /* Debug Panel Styles */
      #debug-panel {
        position: absolute;
        top: 10px;
        right: 260px;
        z-index: 1000;
        background: rgba(44, 24, 16, 0.95);
        border: 2px solid #d4a574;
        border-radius: 10px;
        padding: 15px;
        width: 300px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: Arial, sans-serif;
        font-size: 13px;
        backdrop-filter: blur(8px);
        box-shadow: 0 10px 30px rgba(212, 165, 116, 0.2);
      }

      #debug-panel h3 {
        color: #f4c595;
        margin-bottom: 15px;
        text-align: center;
        font-size: 16px;
        border-bottom: 1px solid rgba(212, 165, 116, 0.3);
        padding-bottom: 8px;
      }

      .debug-section {
        margin-bottom: 15px;
        padding: 10px;
        background: rgba(212, 165, 116, 0.1);
        border-radius: 6px;
        border-left: 3px solid #d4a574;
      }

      .debug-section h4 {
        color: #d4a574;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .debug-control {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        color: #cccccc;
      }

      .debug-control label {
        flex: 1;
        margin-right: 10px;
        font-size: 12px;
      }

      .debug-control input, .debug-control select {
        flex: 1;
        padding: 4px 8px;
        background: rgba(0, 0, 0, 0.5);
        border: 1px solid #d4a574;
        border-radius: 4px;
        color: #cccccc;
        font-size: 12px;
      }

      .debug-control button {
        padding: 6px 12px;
        background: linear-gradient(45deg, #d4a574, #b8935f);
        border: none;
        border-radius: 4px;
        color: #2c1810;
        font-weight: bold;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.2s ease;
      }

      .debug-control button:hover {
        background: linear-gradient(45deg, #f4c595, #d4a574);
        transform: translateY(-1px);
      }

      .debug-status {
        background: rgba(0, 0, 0, 0.3);
        padding: 8px;
        border-radius: 4px;
        margin-bottom: 8px;
        font-family: monospace;
        font-size: 11px;
        border-left: 3px solid #4caf50;
      }

      .debug-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 5px;
        margin-top: 8px;
      }

      .debug-buttons button {
        padding: 8px 4px;
        background: linear-gradient(45deg, #d4a574, #b8935f);
        border: none;
        border-radius: 4px;
        color: #2c1810;
        font-weight: bold;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.2s ease;
      }

      .debug-buttons button:hover {
        background: linear-gradient(45deg, #f4c595, #d4a574);
      }

      /* 状态指示器 - 熊主题色 */
      .status-indicator {
        position: absolute;
        bottom: 20px;
        left: 20px;
        z-index: 100;
        background: rgba(44, 24, 16, 0.9);
        padding: 10px 15px;
        border-radius: 20px;
        border: 1px solid rgba(212, 165, 116, 0.3);
        backdrop-filter: blur(5px);
        font-size: 12px;
        color: #d4a574;
      }

      .status-indicator.loading {
        border-color: rgba(255, 152, 0, 0.3);
        color: #ff9800;
      }

      .status-indicator.ready {
        border-color: rgba(76, 175, 80, 0.3);
        color: #4caf50;
      }

      .status-indicator.error {
        border-color: rgba(255, 107, 107, 0.3);
        color: #ff6b6b;
      }

      /* 熊爪印背景装饰 */
      body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
          radial-gradient(circle at 20% 20%, rgba(212, 165, 116, 0.03) 1px, transparent 1px),
          radial-gradient(circle at 80% 80%, rgba(212, 165, 116, 0.03) 1px, transparent 1px);
        background-size: 100px 100px, 150px 150px;
        pointer-events: none;
        z-index: -1;
      }

      /* 动画效果 */
      @keyframes bearFootstep {
        0%, 100% {
          transform: scale(1);
          opacity: 0.3;
        }
        50% {
          transform: scale(1.1);
          opacity: 0.6;
        }
      }

      .footstep-animation {
        animation: bearFootstep 2s ease-in-out infinite;
      }

      /* 滚动条样式 */
      #debug-panel::-webkit-scrollbar {
        width: 6px;
      }

      #debug-panel::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }

      #debug-panel::-webkit-scrollbar-thumb {
        background: #d4a574;
        border-radius: 3px;
      }

      #debug-panel::-webkit-scrollbar-thumb:hover {
        background: #f4c595;
      }
    </style>
  </head>
  <body>
    <div id="loading">
      <div class="loading-spinner"></div>
      <div>正在加载熊模型...</div>
      <div style="font-size: 14px; margin-top: 10px; opacity: 0.7;">
        🐻 正在加载熊模型动画
      </div>
    </div>

    <div id="error-message">
      <h3>🐻 加载失败</h3>
      <p>无法加载熊模型文件</p>
      <p style="font-size: 14px; margin-top: 10px; opacity: 0.8;">
        请检查控制台获取详细错误信息
      </p>
    </div>
    
    <div id="info">
      <h3>🐻 熊模型动画控制模块</h3>
      <div><strong>控制方式：</strong></div>
      <div>• 鼠标左键：旋转视角</div>
      <div>• 鼠标滚轮：缩放视图</div>
      <div>• 鼠标右键：平移视图</div>
      
      <div class="info-section">
        <div><strong>🎮 动画状态：</strong></div>
        <div>• 待机 - 自然呼吸动作</div>
        <div>• 行走 - 普通行走</div>
        <div>• 攻击 - 熊爪攻击</div>
        <div>• 死亡 - 倒下动画</div>
      </div>

      <div class="info-section">
        <div><strong>🛠️ 调试工具：</strong></div>
        <div>• 右侧面板控制动画</div>
        <div>• 实时状态监控</div>
        <div>• F12 查看详细日志</div>
      </div>
    </div>

    <!-- Debug Panel -->
    <div id="debug-panel" style="display: none;">
      <h3>🐻 熊调试面板</h3>
      
      <!-- 状态显示 -->
      <div class="debug-section">
        <h4>📊 当前状态</h4>
        <div id="bear-status" class="debug-status">
          等待熊模型加载...
        </div>
      </div>

      <!-- 动画控制 -->
      <div class="debug-section">
        <h4>🎬 动画控制</h4>
        <div class="debug-control">
          <label>动画速度:</label>
          <input type="range" id="animation-speed" min="0.1" max="3" step="0.1" value="1">
          <span id="speed-value">1.0</span>
        </div>
        <div class="debug-buttons">
          <button onclick="playAnimation('idle')">🛌 Idle</button>
          <button onclick="testIdleState()">🔍 测试IDLE</button>
          <button onclick="playAnimation('walk')">🚶 Walk</button>
          <button onclick="playAnimation('attack')">⚔️ 攻击</button>
          <button onclick="playAnimation('death')">💀 死亡</button>
        </div>
      </div>

      <!-- 生命值控制 -->
      <div class="debug-section">
        <h4>💖 生命值控制</h4>
        <div class="debug-control">
          <label>生命值:</label>
          <input type="range" id="health-slider" min="0" max="100" value="100">
          <span id="health-value">100</span>
        </div>
        <div class="debug-buttons">
          <button onclick="takeDamage(25)">-25 伤害</button>
          <button onclick="takeDamage(50)">-50 伤害</button>
          <button onclick="killBear()">💀 杀死</button>
          <button onclick="testDeathAnimation()">🎬 测试死亡动画</button>
          <button onclick="forceDeathAnimation()">⚡ 强制死亡</button>
          <button onclick="reviveBear()">✨ 复活</button>
        </div>
      </div>

      <!-- 位置控制 -->
      <div class="debug-section">
        <h4>📐 位置控制</h4>
        <div class="debug-control">
          <label>X:</label>
          <input type="range" id="pos-x" min="-10" max="10" step="0.1" value="0">
          <span id="pos-x-value">0</span>
        </div>
        <div class="debug-control">
          <label>Y:</label>
          <input type="range" id="pos-y" min="-2" max="2" step="0.1" value="0">
          <span id="pos-y-value">0</span>
        </div>
        <div class="debug-control">
          <label>Z:</label>
          <input type="range" id="pos-z" min="-10" max="10" step="0.1" value="0">
          <span id="pos-z-value">0</span>
        </div>
        <div class="debug-control">
          <label>旋转Y:</label>
          <input type="range" id="rot-y" min="-3.14" max="3.14" step="0.1" value="0">
          <span id="rot-y-value">0</span>
        </div>
        <div class="debug-control">
          <label>缩放:</label>
          <input type="range" id="scale" min="0.1" max="3" step="0.1" value="1">
          <span id="scale-value">1.0</span>
        </div>
      </div>

      <!-- 行走控制 -->
      <div class="debug-section">
        <h4>🚶 行走控制</h4>
        <div class="debug-control">
          <label>行走速度:</label>
          <input type="range" id="walk-speed" min="0.5" max="5" step="0.1" value="2">
          <span id="walk-speed-value">2.0</span>
        </div>
        <div class="debug-buttons">
          <button onclick="startPatrol()">🔄 开始巡逻</button>
          <button onclick="stopPatrol()">⏹️ 停止巡逻</button>
        </div>
      </div>
    </div>

    <a href="/" id="back-button">← 返回主页</a>
    
    <canvas id="app"></canvas>
    <script type="module">
      import * as THREE from 'three';
      import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
      import { BearModel, BearState } from '/src/entities/BearModel.js';

      class BearApp {
        constructor() {
          this.scene = null;
          this.camera = null;
          this.renderer = null;
          this.controls = null;
          this.bear = null;
          this.isLoadingBear = false;
          this.ambientLight = null;
          this.directionalLight = null;

          this.init();
          this.createScene();
          this.createLights();
          this.setupControls();
          this.initializeBear();
          this.animate();
          this.handleResize();
        }

        /**
         * 初始化基础场景
         */
        init() {
          // 创建场景
          this.scene = new THREE.Scene();
          this.scene.background = new THREE.Color(0x222222); // 中性灰色背景

          // 创建相机
          this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
          );
          this.camera.position.set(8, 6, 8);

          // 创建渲染器
          const canvas = document.getElementById('app');
          this.renderer = new THREE.WebGLRenderer({
            canvas,
            antialias: true,
            alpha: true,
          });
          this.renderer.setSize(window.innerWidth, window.innerHeight);
          this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
          this.renderer.shadowMap.enabled = true;
          this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
          this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        }

        /**
         * 创建场景环境
         */
        createScene() {
          // 添加网格地面
          const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x333333);
          gridHelper.position.y = -0.01;
          this.scene.add(gridHelper);

          // 添加坐标轴辅助器
          const axesHelper = new THREE.AxesHelper(3);
          this.scene.add(axesHelper);
        }

        /**
         * 创建光照系统
         */
        createLights() {
          // 环境光
          this.ambientLight = new THREE.AmbientLight(0x404040, 0.8);
          this.scene.add(this.ambientLight);

          // 方向光
          this.directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
          this.directionalLight.position.set(10, 20, 5);
          this.directionalLight.castShadow = true;
          this.directionalLight.shadow.mapSize.width = 2048;
          this.directionalLight.shadow.mapSize.height = 2048;
          this.directionalLight.shadow.camera.near = 0.5;
          this.directionalLight.shadow.camera.far = 50;
          this.directionalLight.shadow.camera.left = -15;
          this.directionalLight.shadow.camera.right = 15;
          this.directionalLight.shadow.camera.top = 15;
          this.directionalLight.shadow.camera.bottom = -15;
          this.scene.add(this.directionalLight);
        }

        /**
         * 设置相机控制
         */
        setupControls() {
          this.controls = new OrbitControls(this.camera, this.renderer.domElement);
          this.controls.enableDamping = true;
          this.controls.dampingFactor = 0.05;
          this.controls.screenSpacePanning = false;
          this.controls.minDistance = 3;
          this.controls.maxDistance = 25;
          this.controls.maxPolarAngle = Math.PI / 2;
          this.controls.target.set(0, 1, 0); // 聚焦在熊的高度
        }

        /**
         * 初始化熊系统
         */
        async initializeBear() {
          try {
            this.isLoadingBear = true;

            // 创建熊实例 (使用默认配置)
            this.bear = BearModel.create(this.scene, 'ForestGuardian');

            // 初始化熊
            await this.bear.initialize();
            
            this.isLoadingBear = false;
            console.log('🐻 Bear system initialized successfully');

            // 发送成功消息
            window.postMessage({
              type: 'bear-load-success',
              message: '熊模型加载成功'
            }, '*');

                         // 暴露熊实例到全局，供调试面板使用
             window.bearInstance = this.bear;
             window.BearState = BearState;

          } catch (error) {
            console.error('Failed to initialize bear system:', error);
            this.isLoadingBear = false;

            // 发送错误消息
            window.postMessage({
              type: 'bear-load-error',
              message: '熊模型加载失败'
            }, '*');
          }
        }

        /**
         * 动画循环
         */
        animate() {
          requestAnimationFrame(() => this.animate());

          // 更新熊
          if (this.bear && !this.isLoadingBear) {
            this.bear.update();
          }

          // 更新控制器
          this.controls.update();

          // 渲染场景
          this.renderer.render(this.scene, this.camera);
        }

        /**
         * 处理窗口大小变化
         */
        handleResize() {
          window.addEventListener('resize', () => {
            // 更新相机
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();

            // 更新渲染器
            this.renderer.setSize(window.innerWidth, window.innerHeight);
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
          });
        }

        /**
         * 清理资源
         */
        dispose() {
          if (this.bear) {
            this.bear.dispose();
          }

          if (this.renderer) {
            this.renderer.dispose();
          }
        }
      }

      // 全局变量
      let bearApp = null;

      // 当页面加载完成后启动应用
      window.addEventListener('DOMContentLoaded', () => {
        try {
          bearApp = new BearApp();
        } catch (error) {
          console.error('Failed to start bear application:', error);
          const loading = document.getElementById('loading');
          const errorMsg = document.getElementById('error-message');
          if (loading) loading.style.display = 'none';
          if (errorMsg) errorMsg.style.display = 'block';
        }
      });

      // 页面卸载时清理资源
      window.addEventListener('beforeunload', () => {
        if (bearApp) {
          bearApp.dispose();
        }
      });
    </script>
    
    <script>
      let bearInstance = null;
      let updateInterval = null;

      // 等待熊实例加载
      function waitForBear() {
        if (window.bearInstance) {
          bearInstance = window.bearInstance;
          console.log('🐻 Bear instance found:', bearInstance);
          console.log('🐻 Bear status:', bearInstance.getStatus());
          initializeDebugPanel();
          document.getElementById('debug-panel').style.display = 'block';
          console.log('🐻 Debug panel initialized');
        } else {
          console.log('🐻 Waiting for bear instance...');
          setTimeout(waitForBear, 100);
        }
      }

      // 初始化调试面板
      function initializeDebugPanel() {
        // 设置事件监听器
        setupEventListeners();
        
        // 开始状态更新
        updateInterval = setInterval(updateBearStatus, 100);
        
        // 初始状态更新
        updateBearStatus();
      }

      // 设置事件监听器
      function setupEventListeners() {
        // 动画速度控制
        const speedSlider = document.getElementById('animation-speed');
        speedSlider.addEventListener('input', (e) => {
          const speed = parseFloat(e.target.value);
          document.getElementById('speed-value').textContent = speed.toFixed(1);
          if (bearInstance) {
            bearInstance.getAnimationController().setGlobalTimeScale(speed);
          }
        });

        // 生命值控制
        const healthSlider = document.getElementById('health-slider');
        healthSlider.addEventListener('input', (e) => {
          const health = parseInt(e.target.value);
          document.getElementById('health-value').textContent = health;
          if (bearInstance) {
            const currentHealth = bearInstance.getStatus().health;
            const damage = currentHealth - health;
            if (damage > 0) {
              bearInstance.takeDamage(damage);
            }
          }
        });

        // 位置控制
        setupPositionControls();
        
        // 行走速度控制
        const walkSpeedSlider = document.getElementById('walk-speed');
        walkSpeedSlider.addEventListener('input', (e) => {
          const speed = parseFloat(e.target.value);
          document.getElementById('walk-speed-value').textContent = speed.toFixed(1);
          if (bearInstance) {
            bearInstance.setWalkSpeed(speed);
          }
        });
      }

      // 设置位置控制
      function setupPositionControls() {
        ['pos-x', 'pos-y', 'pos-z', 'rot-y', 'scale'].forEach(id => {
          const slider = document.getElementById(id);
          slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById(id + '-value').textContent = value.toFixed(1);
            updateBearTransform();
          });
        });
      }

      // 更新熊的变换
      function updateBearTransform() {
        if (!bearInstance) return;

        const posX = parseFloat(document.getElementById('pos-x').value);
        const posY = parseFloat(document.getElementById('pos-y').value);
        const posZ = parseFloat(document.getElementById('pos-z').value);
        const rotY = parseFloat(document.getElementById('rot-y').value);
        const scale = parseFloat(document.getElementById('scale').value);

        bearInstance.setPosition(new THREE.Vector3(posX, posY, posZ));
        bearInstance.setRotation(new THREE.Euler(0, rotY, 0));
        bearInstance.setScale(scale);
      }

      // 更新熊状态显示
      function updateBearStatus() {
        if (!bearInstance) return;

        const status = bearInstance.getStatus();
        const statusDiv = document.getElementById('bear-status');
        
        statusDiv.innerHTML = `
          状态: ${status.currentState}<br>
          生命值: ${status.health}/100<br>
          存活: ${status.isAlive ? '是' : '否'}<br>
          行走中: ${status.isWalking ? '是' : '否'}<br>
          已加载: ${status.isLoaded ? '是' : '否'}
        `;

        // 同步滑块值
        document.getElementById('health-slider').value = status.health;
        document.getElementById('health-value').textContent = status.health;
      }

      // 播放动画
      async function playAnimation(state) {
        console.log(`🐻 playAnimation called with state: ${state}`);
        console.log(`🐻 bearInstance:`, bearInstance);
        console.log(`🐻 window.BearState:`, window.BearState);
        
        if (!bearInstance) {
          console.error('🐻 No bear instance available');
          return;
        }
        
        if (!window.BearState) {
          console.error('🐻 BearState not available');
          return;
        }
        
        const stateMap = {
          'idle': window.BearState.IDLE,
          'walk': window.BearState.WALK,
          'attack': window.BearState.ATTACK,
          'death': window.BearState.DEATH
        };
        
        const targetState = stateMap[state];
        console.log(`🐻 Setting state to: ${targetState}`);
        
        try {
          await bearInstance.setState(targetState);
          console.log(`🐻 Successfully set animation state to: ${state}`);
        } catch (error) {
          console.error(`🐻 Error setting animation state:`, error);
        }
      }

      // 造成伤害
      function takeDamage(damage) {
        if (!bearInstance) return;
        bearInstance.takeDamage(damage);
        console.log(`🐻 Bear took ${damage} damage`);
      }

      // 杀死熊
      function killBear() {
        if (!bearInstance) return;
        console.log('🐻 Manually killing bear...');
        bearInstance.takeDamage(1000);
        console.log('🐻 Bear killed via takeDamage(1000)');
      }

      // 测试死亡动画
      function testDeathAnimation() {
        if (!bearInstance) return;
        console.log('🐻 Testing death animation directly...');
        
        // 获取熊的状态
        const status = bearInstance.getStatus();
        console.log('🐻 Bear status before death:', status);
        
        // 直接播放死亡动画
        playAnimation('death').then(() => {
          console.log('🐻 Death animation playback completed');
        }).catch(error => {
          console.error('🐻 Death animation playback failed:', error);
        });
      }

      // 强制死亡动画
      function forceDeathAnimation() {
        if (!bearInstance) return;
        console.log('🐻 Forcing death animation...');
        
        // 先设置血量为0
        bearInstance.setHealth(0);
        
        // 然后检查健康状态
        bearInstance.checkHealthStatus();
        
        console.log('🐻 Forced death sequence initiated');
      }

      // 复活熊
      function reviveBear() {
        if (!bearInstance) return;
        bearInstance.revive();
        console.log('🐻 Bear revived');
      }

      // 开始巡逻
      function startPatrol() {
        if (!bearInstance) return;
        bearInstance.startPatrol();
        console.log('🐻 Bear started patrol');
      }

      // 停止巡逻
      function stopPatrol() {
        if (!bearInstance) return;
        bearInstance.stopPatrol();
        console.log('🐻 Bear stopped patrol');
      }

      // 测试IDLE状态
      function testIdleState() {
        if (!bearInstance) return;
        console.log('🐻 Testing IDLE state (should use walk animation but not move)...');
        
        // 获取熊的状态
        const status = bearInstance.getStatus();
        console.log('🐻 Bear status before IDLE:', status);
        
        // 直接播放IDLE动画
        playAnimation('idle').then(() => {
          console.log('🐻 IDLE state set successfully');
          
          // 检查是否正确设置了isWalking=false
          setTimeout(() => {
            const newStatus = bearInstance.getStatus();
            console.log('🐻 Bear status after IDLE:', newStatus);
            console.log(`🐻 IDLE state verification: isWalking=${newStatus.isWalking}, currentState=${newStatus.currentState}`);
          }, 500);
        }).catch(error => {
          console.error('🐻 IDLE state test failed:', error);
        });
      }

      // 错误处理
      window.addEventListener('error', (e) => {
        console.error('页面错误:', e.error);
        const loading = document.getElementById('loading');
        const errorMsg = document.getElementById('error-message');
        if (loading) loading.style.display = 'none';
        if (errorMsg) errorMsg.style.display = 'block';
      });

      // 监听来自主应用的消息
      window.addEventListener('message', (e) => {
        if (e.data.type === 'bear-load-error') {
          const loading = document.getElementById('loading');
          const errorMsg = document.getElementById('error-message');
          if (loading) loading.style.display = 'none';
          if (errorMsg) {
            errorMsg.style.display = 'block';
            errorMsg.querySelector('p').textContent = e.data.message || '熊模型加载失败';
          }
        } else if (e.data.type === 'bear-load-success') {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.innerHTML = `
              <div style="color: #cccccc;">
                <div class="loading-spinner"></div>
                <div>🐻 熊模型加载完成！</div>
              </div>
            `;
            setTimeout(() => {
              loading.style.display = 'none';
              // 开始等待熊实例
              waitForBear();
            }, 2000);
          }
        }
      });

      // 添加脚步动画效果
      document.addEventListener('DOMContentLoaded', () => {
        const info = document.getElementById('info');
        if (info) {
          info.classList.add('footstep-animation');
        }
      });

      // 页面卸载时清理
      window.addEventListener('beforeunload', () => {
        if (updateInterval) {
          clearInterval(updateInterval);
        }
      });
    </script>
  </body>
</html> 