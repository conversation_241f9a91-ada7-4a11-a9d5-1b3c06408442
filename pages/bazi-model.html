<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字模型展示系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 50%, #2c1810 100%);
            color: white;
            overflow: hidden;
            height: 100vh;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #d2691e;
        }

        #loading h2 {
            color: #d2691e;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #d2691e;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 控制面板样式 */
        #controls {
            position: absolute;
            left: 20px;
            top: 20px;
            width: 320px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #d2691e;
            backdrop-filter: blur(10px);
            z-index: 100;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        #controls h1 {
            color: #d2691e;
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.4rem;
            text-shadow: 0 0 10px rgba(210, 105, 30, 0.5);
        }

        .control-section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(210, 105, 30, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(210, 105, 30, 0.3);
        }

        .control-section h3 {
            color: #d2691e;
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 1px solid rgba(210, 105, 30, 0.3);
            padding-bottom: 8px;
        }

        .slider-container {
            margin-bottom: 15px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 8px;
            color: #fff;
            font-size: 0.9rem;
        }

        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-container input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #d2691e;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(210, 105, 30, 0.5);
        }

        .slider-container input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #d2691e;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 10px rgba(210, 105, 30, 0.5);
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-button {
            flex: 1;
            min-width: 80px;
            padding: 10px;
            background: linear-gradient(45deg, #d2691e, #ff8c42);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .control-button:hover {
            background: linear-gradient(45deg, #ff8c42, #d2691e);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(210, 105, 30, 0.4);
        }

        .control-button:active {
            transform: translateY(0);
        }

        /* 状态面板样式 */
        #status {
            position: absolute;
            right: 20px;
            top: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #d2691e;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        #status h1 {
            color: #d2691e;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
            text-shadow: 0 0 10px rgba(210, 105, 30, 0.5);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            background: rgba(210, 105, 30, 0.1);
            border-radius: 6px;
        }

        .status-label {
            color: #ccc;
            font-size: 0.9rem;
        }

        .status-value {
            color: #fff;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .status-success {
            color: #4CAF50;
        }

        .status-warning {
            color: #FF9800;
        }

        .status-error {
            color: #F44336;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                width: calc(100vw - 40px);
                position: relative;
                margin: 20px;
            }
            
            #controls {
                margin-bottom: 10px;
            }
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb {
            background: #d2691e;
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover {
            background: #ff8c42;
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- 加载提示 -->
        <div id="loading">
            <h2>🍽️ 加载八字模型中...</h2>
            <div class="loading-spinner"></div>
        </div>

        <!-- 控制面板 -->
        <div id="controls">
            <h1>🍽️ 八字模型控制</h1>
            
            <!-- 变换控制 -->
            <div class="control-section">
                <h3>🔄 变换控制</h3>
                
                <div class="slider-container">
                    <label for="positionX">位置 X: <span id="positionXValue">0.0</span></label>
                    <input type="range" id="positionX" min="-5" max="5" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="positionY">位置 Y: <span id="positionYValue">0.0</span></label>
                    <input type="range" id="positionY" min="-3" max="3" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="positionZ">位置 Z: <span id="positionZValue">0.0</span></label>
                    <input type="range" id="positionZ" min="-5" max="5" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="rotationY">旋转 Y: <span id="rotationYValue">0°</span></label>
                    <input type="range" id="rotationY" min="0" max="360" step="5" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="scale">缩放: <span id="scaleValue">0.7</span></label>
                    <input type="range" id="scale" min="0.1" max="2" step="0.001" value="0.7">
                </div>
            </div>

            <!-- 材质控制 -->
            <div class="control-section">
                <h3>🎨 材质控制</h3>
                
                <div class="slider-container">
                    <label for="roughness">粗糙度: <span id="roughnessValue">0.80</span></label>
                    <input type="range" id="roughness" min="0" max="1" step="0.01" value="0.8">
                </div>
                
                <div class="slider-container">
                    <label for="metalness">金属度: <span id="metalnessValue">0.10</span></label>
                    <input type="range" id="metalness" min="0" max="1" step="0.01" value="0.1">
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="control-section">
                <h3>⚙️ 操作</h3>
                <div class="button-group">
                    <button class="control-button" id="resetBtn">重置</button>
                    <button class="control-button" id="debugBtn">调试</button>
                    <button class="control-button" id="resetCameraBtn">重置相机</button>
                </div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div id="status">
            <h1>📊 状态信息</h1>
            
            <div class="status-item">
                <span class="status-label">模型状态:</span>
                <span class="status-value status-warning" id="modelStatus">加载中</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">纹理状态:</span>
                <span class="status-value status-warning" id="textureStatus">加载中</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前位置:</span>
                <span class="status-value" id="currentPosition">0.0, 0.0, 0.0</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前旋转:</span>
                <span class="status-value" id="currentRotation">0°</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前缩放:</span>
                <span class="status-value" id="currentScale">0.7</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="currentRoughness">0.80</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="currentMetalness">0.10</span>
            </div>
        </div>
    </div>

    <!-- Three.js 和相关库 -->
    <script type="importmap">
        {
            "imports": {
                "three": "/node_modules/three/build/three.module.js",
                "three/addons/": "/node_modules/three/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { BaziModel } from '../src/entities/BaziModel.js';

        class BaziModelApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.bazi = null;
                this.animationId = null;

                this.init();
            }

            async init() {
                this.setupScene();
                this.setupLighting();
                this.setupControls();
                this.setupEventListeners();

                // 自动加载八字模型
                await this.loadBazi();

                this.animate();
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupScene() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x2c1810);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(3, 2, 3);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;

                document.getElementById('container').appendChild(this.renderer.domElement);

                // 添加地面
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshStandardMaterial({
                    color: 0x8B4513,
                    roughness: 0.8,
                    metalness: 0.1
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -1;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            setupLighting() {
                // Cocos项目精确光照配置
                // 环境光 (intensity: 0.52)
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.52);
                this.scene.add(ambientLight);

                // 主光源 (color: 0xFFF8E9, intensity: 1.69)
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(5, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                console.log('💡 Cocos项目光照配置已应用');
            }

            setupControls() {
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.screenSpacePanning = false;
                this.controls.minDistance = 1;
                this.controls.maxDistance = 20;
                this.controls.maxPolarAngle = Math.PI / 2;
            }

            async loadBazi() {
                try {
                    this.bazi = new BaziModel(this.scene);

                    // 设置回调
                    this.bazi.setEventCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('modelStatus').textContent = '已加载';
                            document.getElementById('modelStatus').className = 'status-value status-success';
                            this.updateModelInfo();
                            console.log('✅ 八字模型加载完成');
                        },
                        onTextureLoaded: () => {
                            document.getElementById('textureStatus').textContent = '已加载';
                            document.getElementById('textureStatus').className = 'status-value status-success';
                            console.log('✅ 八字纹理加载完成');
                        }
                    });

                    await this.bazi.initialize();
                } catch (error) {
                    console.error('❌ 八字加载失败:', error);
                    document.getElementById('loading').textContent = '加载失败';
                    document.getElementById('modelStatus').textContent = '加载失败';
                    document.getElementById('modelStatus').className = 'status-value status-error';
                }
            }

            setupEventListeners() {
                // 位置控制
                document.getElementById('positionX').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionXValue').textContent = value.toFixed(1);
                    if (this.bazi) {
                        const pos = this.bazi.getPosition();
                        this.bazi.setPosition(new THREE.Vector3(value, pos.y, pos.z));
                        this.updatePositionStatus();
                    }
                });

                document.getElementById('positionY').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionYValue').textContent = value.toFixed(1);
                    if (this.bazi) {
                        const pos = this.bazi.getPosition();
                        this.bazi.setPosition(new THREE.Vector3(pos.x, value, pos.z));
                        this.updatePositionStatus();
                    }
                });

                document.getElementById('positionZ').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionZValue').textContent = value.toFixed(1);
                    if (this.bazi) {
                        const pos = this.bazi.getPosition();
                        this.bazi.setPosition(new THREE.Vector3(pos.x, pos.y, value));
                        this.updatePositionStatus();
                    }
                });

                // 旋转控制
                document.getElementById('rotationY').addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    document.getElementById('rotationYValue').textContent = value + '°';
                    if (this.bazi) {
                        const rot = this.bazi.getRotation();
                        this.bazi.setRotation(new THREE.Euler(rot.x, value * Math.PI / 180, rot.z));
                        this.updateRotationStatus();
                    }
                });

                // 缩放控制
                document.getElementById('scale').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('scaleValue').textContent = value.toFixed(3);
                    if (this.bazi) {
                        this.bazi.setScale(value);
                        this.updateScaleStatus();
                    }
                });

                // 材质控制
                document.getElementById('roughness').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('roughnessValue').textContent = value.toFixed(2);
                    document.getElementById('currentRoughness').textContent = value.toFixed(2);
                    if (this.bazi) {
                        this.bazi.setMaterialProperties({ roughness: value });
                    }
                });

                document.getElementById('metalness').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('metalnessValue').textContent = value.toFixed(2);
                    document.getElementById('currentMetalness').textContent = value.toFixed(2);
                    if (this.bazi) {
                        this.bazi.setMaterialProperties({ metalness: value });
                    }
                });

                // 按钮事件
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetBazi();
                });

                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.debugBazi();
                });

                document.getElementById('resetCameraBtn').addEventListener('click', () => {
                    this.resetCamera();
                });
            }

            updateModelInfo() {
                if (!this.bazi) return;

                const model = this.bazi.getModel();
                if (!model) return;

                let meshCount = 0;
                let triangleCount = 0;

                model.traverse((child) => {
                    if (child instanceof THREE.Mesh) {
                        meshCount++;
                        if (child.geometry) {
                            const positionAttribute = child.geometry.getAttribute('position');
                            if (positionAttribute) {
                                triangleCount += positionAttribute.count / 3;
                            }
                        }
                    }
                });

                console.log(`📊 八字模型信息: ${meshCount} 个网格, ${Math.floor(triangleCount)} 个三角形`);
            }

            updatePositionStatus() {
                if (!this.bazi) return;
                const pos = this.bazi.getPosition();
                document.getElementById('currentPosition').textContent =
                    `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
            }

            updateRotationStatus() {
                if (!this.bazi) return;
                const rot = this.bazi.getRotation();
                const degY = Math.round(rot.y * 180 / Math.PI);
                document.getElementById('currentRotation').textContent = `${degY}°`;
            }

            updateScaleStatus() {
                if (!this.bazi) return;
                const scale = this.bazi.getScale();
                document.getElementById('currentScale').textContent = scale.toFixed(3);
            }

            resetBazi() {
                if (!this.bazi) return;

                // 重置变换
                this.bazi.setPosition(new THREE.Vector3(0, 0, 0));
                this.bazi.setRotation(new THREE.Euler(0, 0, 0));
                this.bazi.setScale(0.7);

                // 重置材质
                this.bazi.setMaterialProperties({
                    roughness: 0.8,
                    metalness: 0.1
                });

                // 重置UI控件
                document.getElementById('positionX').value = '0';
                document.getElementById('positionY').value = '0';
                document.getElementById('positionZ').value = '0';
                document.getElementById('rotationY').value = '0';
                document.getElementById('scale').value = '0.7';
                document.getElementById('roughness').value = '0.8';
                document.getElementById('metalness').value = '0.1';

                // 更新显示值
                document.getElementById('positionXValue').textContent = '0.0';
                document.getElementById('positionYValue').textContent = '0.0';
                document.getElementById('positionZValue').textContent = '0.0';
                document.getElementById('rotationYValue').textContent = '0°';
                document.getElementById('scaleValue').textContent = '0.7';
                document.getElementById('roughnessValue').textContent = '0.80';
                document.getElementById('metalnessValue').textContent = '0.10';

                // 更新状态
                this.updatePositionStatus();
                this.updateRotationStatus();
                this.updateScaleStatus();
                document.getElementById('currentRoughness').textContent = '0.80';
                document.getElementById('currentMetalness').textContent = '0.10';
                document.getElementById('currentScale').textContent = '0.700';

                console.log('🔄 八字已重置');
            }

            resetCamera() {
                this.camera.position.set(3, 2, 3);
                this.controls.reset();
                console.log('📷 相机视角已重置');
            }

            debugBazi() {
                if (this.bazi) {
                    this.bazi.debugMaterials();
                } else {
                    console.log('❌ 八字模型未加载');
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.bazi) {
                    this.bazi.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('BaziModelApp disposed');
            }
        }

        // 启动应用
        const app = new BaziModelApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
