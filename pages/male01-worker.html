<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Male01 Worker Model - 工人模型展示</title>
    <link rel="stylesheet" href="../src/styles/common.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 12px;
            padding: 20px;
            color: white;
            min-width: 280px;
            max-width: 320px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        .control-group h3 {
            margin: 0 0 10px 0;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 8px;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        button.primary {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        button.danger {
            background: linear-gradient(135deg, #ff512f 0%, #f09819 100%);
        }

        button.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .slider-control {
            margin-bottom: 10px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ccc;
        }

        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #444;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            border: none;
        }

        .value-display {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 12px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status h3 {
            margin: 0 0 10px 0;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #fff;
            font-weight: 500;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            padding: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #navigation {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }

        #navigation button {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        #navigation button:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #fff;
        }

        /* 自定义滚动条样式 */
        #controls::-webkit-scrollbar {
            width: 8px;
        }

        #controls::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        #controls::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        #controls::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        /* Firefox 滚动条样式 */
        #controls, #status {
            scrollbar-width: thin;
            scrollbar-color: #667eea rgba(255, 255, 255, 0.1);
        }

        /* 状态面板滚动条样式 */
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #status::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 3px;
        }

        #status::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            #controls {
                position: fixed;
                top: 10px;
                left: 10px;
                right: auto;
                min-width: 260px;
                max-width: calc(100vw - 20px);
                max-height: calc(100vh - 80px);
                font-size: 14px;
                padding: 15px;
            }

            #status {
                position: fixed;
                top: 10px;
                right: 10px;
                left: auto;
                min-width: 180px;
                max-width: calc(50vw - 15px);
                max-height: calc(50vh - 20px);
                font-size: 12px;
                padding: 12px;
            }

            #navigation {
                position: fixed;
                bottom: 10px;
                left: 10px;
                right: 10px;
                text-align: center;
            }

            .control-group h3 {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .slider-control label {
                font-size: 12px;
            }

            button {
                padding: 6px 12px;
                font-size: 12px;
                min-width: 60px;
            }

            .button-row {
                gap: 6px;
            }

            h2 {
                font-size: 16px !important;
            }
        }

        @media (max-width: 480px) {
            #controls {
                max-width: calc(100vw - 10px);
                left: 5px;
                min-width: auto;
            }

            #status {
                position: fixed;
                top: auto;
                bottom: 80px;
                right: 5px;
                left: 5px;
                max-width: none;
                max-height: 150px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <div>正在加载Male01工人模型...</div>
        </div>

        <div id="controls" style="display: none;">
            <h2 style="margin: 0 0 20px 0; color: #fff; font-size: 18px;">Male01 工人控制</h2>
            
            <div class="control-group">
                <h3>基础动作</h3>
                <div class="button-row">
                    <button id="idleBtn">待机</button>
                    <button id="walkBtn">行走</button>
                    <button id="runBtn">跑步</button>
                </div>
            </div>

            <div class="control-group">
                <h3>战斗动作</h3>
                <div class="button-row">
                    <button id="shootBtn" class="danger">射击</button>
                    <button id="standingShootBtn" class="danger">站立射击</button>
                </div>
            </div>

            <div class="control-group">
                <h3>生活动作</h3>
                <div class="button-row">
                    <button id="readBtn" class="info">阅读</button>
                    <button id="coldBtn" class="info">寒冷</button>
                </div>
            </div>

            <div class="control-group">
                <h3>移动控制</h3>
                <div class="button-row">
                    <button id="startPatrolBtn" class="primary">开始巡逻</button>
                    <button id="stopPatrolBtn">停止巡逻</button>
                </div>
            </div>

            <div class="control-group">
                <h3>模型参数</h3>
                <div class="slider-control">
                    <label for="scaleSlider">缩放：</label>
                    <input type="range" id="scaleSlider" min="0.01" max="0.05" step="0.001" value="0.02">
                    <div class="value-display" id="scaleValue">0.02</div>
                </div>
                <div class="slider-control">
                    <label for="walkSpeedSlider">行走速度：</label>
                    <input type="range" id="walkSpeedSlider" min="0.5" max="3" step="0.1" value="1.5">
                    <div class="value-display" id="walkSpeedValue">1.5</div>
                </div>
                <div class="slider-control">
                    <label for="runSpeedSlider">跑步速度：</label>
                    <input type="range" id="runSpeedSlider" min="2" max="5" step="0.1" value="3">
                    <div class="value-display" id="runSpeedValue">3.0</div>
                </div>
            </div>

            <div class="control-group">
                <h3>武器位置调整</h3>
                <div class="slider-control">
                    <label for="weaponPosXSlider">位置 X：</label>
                    <input type="range" id="weaponPosXSlider" min="-2" max="2" step="0.01" value="-0.11000">
                    <div class="value-display" id="weaponPosXValue">-0.11000</div>
                </div>
                <div class="slider-control">
                    <label for="weaponPosYSlider">位置 Y：</label>
                    <input type="range" id="weaponPosYSlider" min="-2" max="2" step="0.01" value="-1.77000">
                    <div class="value-display" id="weaponPosYValue">-1.77000</div>
                </div>
                <div class="slider-control">
                    <label for="weaponPosZSlider">位置 Z：</label>
                    <input type="range" id="weaponPosZSlider" min="-2" max="2" step="0.01" value="-1.68000">
                    <div class="value-display" id="weaponPosZValue">-1.68000</div>
                </div>
                <div class="slider-control">
                    <label for="weaponRotXSlider">旋转 X (度)：</label>
                    <input type="range" id="weaponRotXSlider" min="-180" max="180" step="1" value="30">
                    <div class="value-display" id="weaponRotXValue">30</div>
                </div>
                <div class="slider-control">
                    <label for="weaponRotYSlider">旋转 Y (度)：</label>
                    <input type="range" id="weaponRotYSlider" min="-180" max="180" step="1" value="90">
                    <div class="value-display" id="weaponRotYValue">90</div>
                </div>
                <div class="slider-control">
                    <label for="weaponRotZSlider">旋转 Z (度)：</label>
                    <input type="range" id="weaponRotZSlider" min="-180" max="180" step="1" value="-47">
                    <div class="value-display" id="weaponRotZValue">-47</div>
                </div>
            </div>

            <div class="control-group">
                <h3>其他操作</h3>
                <div class="button-row">
                    <button id="damageBtn" class="danger">造成伤害</button>
                    <button id="reviveBtn" class="primary">复活</button>
                </div>
                <div class="button-row">
                    <button id="debugBtn" class="info">调试材质</button>
                    <button id="reapplyTextureBtn" class="info">重新应用纹理</button>
                </div>

                <div class="button-row">
                    <button id="debugBonesBtn" class="info">调试骨骼</button>
                    <button id="adjustWeaponBtn" class="info">调整武器位置</button>
                </div>
                <div class="button-row">
                    <button id="findWristRBtn" class="primary">查找 Wrist_R</button>
                    <button id="reattachWeaponBtn" class="primary">重新挂载武器</button>
                </div>
                <div class="button-row">
                    <button id="showWeaponBtn" class="info">显示武器</button>
                    <button id="hideWeaponBtn" class="info">隐藏武器</button>
                </div>
            </div>
        </div>

        <div id="status" style="display: none;">
            <h3>工人状态</h3>
            <div class="status-item">
                <span class="status-label">生命值：</span>
                <span class="status-value" id="healthStatus">100/100</span>
            </div>
            <div class="status-item">
                <span class="status-label">状态：</span>
                <span class="status-value" id="stateStatus">待机</span>
            </div>
            <div class="status-item">
                <span class="status-label">是否移动：</span>
                <span class="status-value" id="movingStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置：</span>
                <span class="status-value" id="positionStatus">0, 0, 0</span>
            </div>
            <div class="status-item">
                <span class="status-label">武器显示：</span>
                <span class="status-value" id="weaponVisibleStatus">否</span>
            </div>
        </div>

        <div id="navigation">
            <button onclick="window.location.href='../index.html'">← 返回主页</button>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { Male01Model, Male01State } from '../src/entities/Male01Model.js';

        class Male01Demo {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.worker = null;
                this.animationId = null;

                this.init();
            }

            async init() {
                try {
                    // 创建场景
                    this.setupScene();
                    this.setupLighting();
                    this.setupCamera();
                    this.setupRenderer();

                    // 创建并加载工人模型
                    await this.createWorker();

                    // 隐藏加载界面，显示控制界面
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('controls').style.display = 'block';
                    document.getElementById('status').style.display = 'block';

                    // 设置控件事件
                    this.setupControls();

                    // 开始渲染循环
                    this.animate();

                } catch (error) {
                    console.error('初始化失败:', error);
                    document.getElementById('loading').innerHTML = `
                        <div style="color: #ff6b6b;">
                            <h3>加载失败</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }

            setupScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);
                
                // 添加雾效
                this.scene.fog = new THREE.Fog(0x87CEEB, 10, 50);

                // 添加地面
                const groundGeometry = new THREE.PlaneGeometry(100, 100);
                const groundMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0x90EE90,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -1;
                this.scene.add(ground);
            }

            setupLighting() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);

                // 主方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);

                // 补充光源
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
                fillLight.position.set(-10, 5, -5);
                this.scene.add(fillLight);
            }

            setupCamera() {
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(5, 3, 5);
                this.camera.lookAt(0, 1, 0);
            }

            setupRenderer() {
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.setClearColor(0x87CEEB);
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 处理窗口大小变化
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            async createWorker() {
                // 创建工人模型
                this.worker = Male01Model.create(this.scene, 'TestWorker');

                // 设置事件回调
                this.worker.setEventCallbacks({
                    onLoadComplete: () => {
                        console.log('工人模型加载完成');
                        this.updateStatus();
                    },
                    onAnimationChange: (newState) => {
                        console.log('动画状态改变:', newState);
                        this.updateStatus();
                    },
                    onDeath: () => {
                        console.log('工人死亡');
                        this.updateStatus();
                    },
                    onMoveDirectionChange: (direction) => {
                        console.log('移动方向改变:', direction);
                    },
                    onTextureLoaded: () => {
                        console.log('纹理加载完成');
                    }
                });

                // 初始化模型
                await this.worker.initialize();
            }

            setupControls() {
                // 基础动作控制
                document.getElementById('idleBtn').addEventListener('click', () => {
                    this.worker.setState(Male01State.IDLE);
                });

                document.getElementById('walkBtn').addEventListener('click', () => {
                    this.worker.setState(Male01State.WALK);
                });

                document.getElementById('runBtn').addEventListener('click', () => {
                    this.worker.setState(Male01State.RUN);
                });

                // 战斗动作控制
                document.getElementById('shootBtn').addEventListener('click', () => {
                    this.worker.shoot(false);
                });

                document.getElementById('standingShootBtn').addEventListener('click', () => {
                    this.worker.shoot(true);
                });

                // 生活动作控制
                document.getElementById('readBtn').addEventListener('click', () => {
                    this.worker.startReading();
                });

                document.getElementById('coldBtn').addEventListener('click', () => {
                    this.worker.startShivering();
                });

                // 移动控制
                document.getElementById('startPatrolBtn').addEventListener('click', () => {
                    this.worker.startPatrol();
                });

                document.getElementById('stopPatrolBtn').addEventListener('click', () => {
                    this.worker.stopPatrol();
                });

                // 参数滑块控制
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', (e) => {
                    const scale = parseFloat(e.target.value);
                    this.worker.setScale(scale);
                    scaleValue.textContent = scale.toFixed(3);
                });

                const walkSpeedSlider = document.getElementById('walkSpeedSlider');
                const walkSpeedValue = document.getElementById('walkSpeedValue');
                walkSpeedSlider.addEventListener('input', (e) => {
                    const speed = parseFloat(e.target.value);
                    this.worker.setWalkSpeed(speed);
                    walkSpeedValue.textContent = speed.toFixed(1);
                });

                const runSpeedSlider = document.getElementById('runSpeedSlider');
                const runSpeedValue = document.getElementById('runSpeedValue');
                runSpeedSlider.addEventListener('input', (e) => {
                    const speed = parseFloat(e.target.value);
                    this.worker.setRunSpeed(speed);
                    runSpeedValue.textContent = speed.toFixed(1);
                });

                // 其他操作
                document.getElementById('damageBtn').addEventListener('click', () => {
                    this.worker.takeDamage(25);
                    this.updateStatus();
                });

                document.getElementById('reviveBtn').addEventListener('click', () => {
                    this.worker.revive();
                    this.updateStatus();
                });

                // 调试操作
                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.worker.debugMaterials();
                });

                document.getElementById('reapplyTextureBtn').addEventListener('click', async () => {
                    console.log('重新应用纹理...');
                    await this.worker.reapplyTexture();
                    console.log('纹理重新应用完成');
                });



                document.getElementById('debugBonesBtn').addEventListener('click', () => {
                    this.worker.debugBoneStructure();
                });

                document.getElementById('adjustWeaponBtn').addEventListener('click', () => {
                    this.worker.adjustWeaponPosition();
                });

                document.getElementById('findWristRBtn').addEventListener('click', () => {
                    console.log('=== 手动查找 Wrist_R 骨骼 ===');
                    this.worker.debugBoneStructure();
                });

                document.getElementById('reattachWeaponBtn').addEventListener('click', async () => {
                    console.log('=== 重新挂载武器到 Wrist_R ===');
                    this.worker.reattachWeapon();
                });

                // 武器显示控制
                document.getElementById('showWeaponBtn').addEventListener('click', () => {
                    this.worker.setWeaponVisible(true);
                });

                document.getElementById('hideWeaponBtn').addEventListener('click', () => {
                    this.worker.setWeaponVisible(false);
                });

                // 武器位置调整滑块
                const weaponSliders = [
                    { id: 'weaponPosXSlider', valueId: 'weaponPosXValue', type: 'position', axis: 'x' },
                    { id: 'weaponPosYSlider', valueId: 'weaponPosYValue', type: 'position', axis: 'y' },
                    { id: 'weaponPosZSlider', valueId: 'weaponPosZValue', type: 'position', axis: 'z' },
                    { id: 'weaponRotXSlider', valueId: 'weaponRotXValue', type: 'rotation', axis: 'x' },
                    { id: 'weaponRotYSlider', valueId: 'weaponRotYValue', type: 'rotation', axis: 'y' },
                    { id: 'weaponRotZSlider', valueId: 'weaponRotZValue', type: 'rotation', axis: 'z' }
                ];

                weaponSliders.forEach(slider => {
                    const sliderElement = document.getElementById(slider.id);
                    const valueElement = document.getElementById(slider.valueId);
                    
                    sliderElement.addEventListener('input', (e) => {
                        const value = parseFloat(e.target.value);
                        valueElement.textContent = value.toFixed(slider.type === 'position' ? 5 : 0);
                        this.updateWeaponTransform();
                    });
                });
            }

            updateStatus() {
                if (!this.worker) return;

                const status = this.worker.getStatus();
                document.getElementById('healthStatus').textContent = `${status.health}/${status.maxHealth}`;
                document.getElementById('stateStatus').textContent = this.getStateDisplayName(status.currentState);
                document.getElementById('movingStatus').textContent = status.isMoving ? '是' : '否';
                document.getElementById('weaponVisibleStatus').textContent = status.weaponVisible ? '是' : '否';
                
                if (status.position) {
                    document.getElementById('positionStatus').textContent = 
                        `${status.position.x.toFixed(1)}, ${status.position.y.toFixed(1)}, ${status.position.z.toFixed(1)}`;
                }
            }

            updateWeaponTransform() {
                if (!this.worker) return;

                // 获取滑块值
                const posX = parseFloat(document.getElementById('weaponPosXSlider').value);
                const posY = parseFloat(document.getElementById('weaponPosYSlider').value);
                const posZ = parseFloat(document.getElementById('weaponPosZSlider').value);
                
                const rotX = parseFloat(document.getElementById('weaponRotXSlider').value);
                const rotY = parseFloat(document.getElementById('weaponRotYSlider').value);
                const rotZ = parseFloat(document.getElementById('weaponRotZSlider').value);

                // 应用到武器
                const position = new THREE.Vector3(posX, posY, posZ);
                const rotation = new THREE.Vector3(rotX, rotY, rotZ);
                
                this.worker.setWeaponTransform(position, rotation);
            }

            getStateDisplayName(state) {
                const stateNames = {
                    [Male01State.IDLE]: '待机',
                    [Male01State.WALK]: '行走',
                    [Male01State.RUN]: '跑步',
                    [Male01State.SHOOTING]: '射击',
                    [Male01State.STANDING_SHOOT]: '站立射击',
                    [Male01State.READING]: '阅读',
                    [Male01State.COLD]: '寒冷',
                    [Male01State.DEATH]: '死亡'
                };
                return stateNames[state] || state;
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新工人模型
                if (this.worker) {
                    this.worker.update();
                }

                // 更新状态显示
                this.updateStatus();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.worker) {
                    this.worker.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        // 初始化演示
        window.addEventListener('load', () => {
            new Male01Demo();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (window.demo) {
                window.demo.dispose();
            }
        });
    </script>
</body>
</html> 