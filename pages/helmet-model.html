<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helmet Model Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #FF6B35;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #FF6B35;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #FF6B35;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #E55A2B, #E8851A);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        button.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        button.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .slider-container {
            margin-bottom: 12px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            font-size: 13px;
            color: #ccc;
        }

        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #34495E;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-container input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider-container input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            font-size: 13px;
            color: #ccc;
        }

        .status-value {
            font-size: 13px;
            font-weight: 600;
            color: #FF6B35;
        }

        .status-value.status-success {
            color: #4CAF50;
        }

        .status-value.status-warning {
            color: #FFC107;
        }

        .status-value.status-error {
            color: #F44336;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 16px;
            z-index: 1000;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #FF6B35;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .back-button:hover {
            background: rgba(255, 107, 53, 0.8);
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- 返回按钮 -->
        <button class="back-button" onclick="window.location.href='../index.html'">
            ← 返回主页
        </button>

        <!-- 加载提示 -->
        <div id="loading">
            <div class="loading-spinner"></div>
            正在加载头盔模型...
        </div>

        <!-- 控制面板 -->
        <div id="controls">
            <h1>🪖 头盔控制</h1>
            
            <!-- 变换控制 -->
            <div class="control-group">
                <h4>变换控制</h4>
                
                <div class="slider-container">
                    <label for="positionX">位置 X: <span id="positionXValue">0.0</span></label>
                    <input type="range" id="positionX" min="-5" max="5" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="positionY">位置 Y: <span id="positionYValue">0.0</span></label>
                    <input type="range" id="positionY" min="-5" max="5" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="positionZ">位置 Z: <span id="positionZValue">0.0</span></label>
                    <input type="range" id="positionZ" min="-5" max="5" step="0.1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="rotationY">旋转 Y: <span id="rotationYValue">0°</span></label>
                    <input type="range" id="rotationY" min="0" max="360" step="1" value="0">
                </div>
                
                <div class="slider-container">
                    <label for="scale">缩放: <span id="scaleValue">0.01</span></label>
                    <input type="range" id="scale" min="0.001" max="0.1" step="0.001" value="0.01">
                </div>
            </div>

            <!-- 材质控制 -->
            <div class="control-group">
                <h4>材质控制</h4>
                
                <div class="slider-container">
                    <label for="roughness">粗糙度: <span id="roughnessValue">0.8</span></label>
                    <input type="range" id="roughness" min="0" max="1" step="0.01" value="0.8">
                </div>
                
                <div class="slider-container">
                    <label for="metalness">金属度: <span id="metalnessValue">0.1</span></label>
                    <input type="range" id="metalness" min="0" max="1" step="0.01" value="0.1">
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="control-group">
                <h4>操作</h4>
                <div class="button-row">
                    <button class="secondary" id="resetBtn">重置</button>
                    <button class="secondary" id="debugBtn">调试</button>
                </div>
                <div class="button-row">
                    <button class="secondary" id="resetCameraBtn">重置相机</button>
                </div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div id="status">
            <h1>📊 状态信息</h1>
            
            <div class="status-item">
                <span class="status-label">模型状态:</span>
                <span class="status-value status-warning" id="modelStatus">加载中</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">纹理状态:</span>
                <span class="status-value status-warning" id="textureStatus">加载中</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前位置:</span>
                <span class="status-value" id="currentPosition">0.0, 0.0, 0.0</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前旋转:</span>
                <span class="status-value" id="currentRotation">0°</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">当前缩放:</span>
                <span class="status-value" id="currentScale">0.01</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="currentRoughness">0.8</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="currentMetalness">0.1</span>
            </div>
        </div>
    </div>

    <script type="module">
        import * as THREE from '/node_modules/three/build/three.module.js';
        import { OrbitControls } from '/node_modules/three/examples/jsm/controls/OrbitControls.js';
        import { HelmetModel } from '/src/entities/HelmetModel.js';

        class HelmetModelApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.helmet = null;
                this.animationId = null;

                this.init();
            }

            async init() {
                this.setupScene();
                this.setupLighting();
                this.setupControls();
                this.setupEventListeners();

                // 自动加载头盔模型
                await this.loadHelmet();

                this.animate();
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupScene() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x2c3e50);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(3, 2, 3);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;

                document.getElementById('container').appendChild(this.renderer.domElement);

                // 添加地面
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshStandardMaterial({
                    color: 0x8B4513,
                    roughness: 0.8,
                    metalness: 0.1
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -1;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            setupLighting() {
                // Cocos项目精确光照配置
                // 环境光 (intensity: 0.52)
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.52);
                this.scene.add(ambientLight);

                // 主光源 (color: 0xFFF8E9, intensity: 1.69)
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(5, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                console.log('💡 Cocos项目光照配置已应用');
            }

            setupControls() {
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.screenSpacePanning = false;
                this.controls.minDistance = 1;
                this.controls.maxDistance = 20;
                this.controls.maxPolarAngle = Math.PI / 2;
            }

            async loadHelmet() {
                try {
                    this.helmet = new HelmetModel(this.scene);

                    // 设置回调
                    this.helmet.setEventCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('modelStatus').textContent = '已加载';
                            document.getElementById('modelStatus').className = 'status-value status-success';
                            this.updateModelInfo();
                            console.log('✅ 头盔模型加载完成');
                        },
                        onTextureLoaded: () => {
                            document.getElementById('textureStatus').textContent = '已加载';
                            document.getElementById('textureStatus').className = 'status-value status-success';
                            console.log('✅ 头盔纹理加载完成');
                        }
                    });

                    await this.helmet.initialize();
                } catch (error) {
                    console.error('❌ 头盔加载失败:', error);
                    document.getElementById('loading').textContent = '加载失败';
                    document.getElementById('modelStatus').textContent = '加载失败';
                    document.getElementById('modelStatus').className = 'status-value status-error';
                }
            }

            setupEventListeners() {
                // 位置控制
                document.getElementById('positionX').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionXValue').textContent = value.toFixed(1);
                    if (this.helmet) {
                        const pos = this.helmet.getPosition();
                        this.helmet.setPosition(new THREE.Vector3(value, pos.y, pos.z));
                        this.updatePositionStatus();
                    }
                });

                document.getElementById('positionY').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionYValue').textContent = value.toFixed(1);
                    if (this.helmet) {
                        const pos = this.helmet.getPosition();
                        this.helmet.setPosition(new THREE.Vector3(pos.x, value, pos.z));
                        this.updatePositionStatus();
                    }
                });

                document.getElementById('positionZ').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('positionZValue').textContent = value.toFixed(1);
                    if (this.helmet) {
                        const pos = this.helmet.getPosition();
                        this.helmet.setPosition(new THREE.Vector3(pos.x, pos.y, value));
                        this.updatePositionStatus();
                    }
                });

                // 旋转控制
                document.getElementById('rotationY').addEventListener('input', (e) => {
                    const value = parseInt(e.target.value);
                    document.getElementById('rotationYValue').textContent = value + '°';
                    if (this.helmet) {
                        const rot = this.helmet.getRotation();
                        this.helmet.setRotation(new THREE.Euler(rot.x, value * Math.PI / 180, rot.z));
                        this.updateRotationStatus();
                    }
                });

                // 缩放控制
                document.getElementById('scale').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('scaleValue').textContent = value.toFixed(3);
                    if (this.helmet) {
                        this.helmet.setScale(value);
                        this.updateScaleStatus();
                    }
                });

                // 材质控制
                document.getElementById('roughness').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('roughnessValue').textContent = value.toFixed(2);
                    document.getElementById('currentRoughness').textContent = value.toFixed(2);
                    if (this.helmet) {
                        this.helmet.setMaterialProperties({ roughness: value });
                    }
                });

                document.getElementById('metalness').addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    document.getElementById('metalnessValue').textContent = value.toFixed(2);
                    document.getElementById('currentMetalness').textContent = value.toFixed(2);
                    if (this.helmet) {
                        this.helmet.setMaterialProperties({ metalness: value });
                    }
                });

                // 按钮事件
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.resetHelmet();
                });

                document.getElementById('debugBtn').addEventListener('click', () => {
                    this.debugHelmet();
                });

                document.getElementById('resetCameraBtn').addEventListener('click', () => {
                    this.resetCamera();
                });
            }

            updateModelInfo() {
                if (!this.helmet) return;

                const model = this.helmet.getModel();
                if (!model) return;

                let meshCount = 0;
                let triangleCount = 0;

                model.traverse((child) => {
                    if (child instanceof THREE.Mesh) {
                        meshCount++;
                        if (child.geometry) {
                            const positionAttribute = child.geometry.getAttribute('position');
                            if (positionAttribute) {
                                triangleCount += positionAttribute.count / 3;
                            }
                        }
                    }
                });

                console.log(`📊 头盔模型信息: ${meshCount} 个网格, ${Math.floor(triangleCount)} 个三角形`);
            }

            updatePositionStatus() {
                if (!this.helmet) return;
                const pos = this.helmet.getPosition();
                document.getElementById('currentPosition').textContent =
                    `${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}`;
            }

            updateRotationStatus() {
                if (!this.helmet) return;
                const rot = this.helmet.getRotation();
                const degY = Math.round(rot.y * 180 / Math.PI);
                document.getElementById('currentRotation').textContent = `${degY}°`;
            }

            updateScaleStatus() {
                if (!this.helmet) return;
                const scale = this.helmet.getScale();
                document.getElementById('currentScale').textContent = scale.toFixed(3);
            }

            resetHelmet() {
                if (!this.helmet) return;

                // 重置变换
                this.helmet.setPosition(new THREE.Vector3(0, 0, 0));
                this.helmet.setRotation(new THREE.Euler(0, 0, 0));
                this.helmet.setScale(0.01);

                // 重置材质
                this.helmet.setMaterialProperties({
                    roughness: 0.8,
                    metalness: 0.1
                });

                // 重置UI控件
                document.getElementById('positionX').value = '0';
                document.getElementById('positionY').value = '0';
                document.getElementById('positionZ').value = '0';
                document.getElementById('rotationY').value = '0';
                document.getElementById('scale').value = '0.01';
                document.getElementById('roughness').value = '0.8';
                document.getElementById('metalness').value = '0.1';

                // 更新显示值
                document.getElementById('positionXValue').textContent = '0.0';
                document.getElementById('positionYValue').textContent = '0.0';
                document.getElementById('positionZValue').textContent = '0.0';
                document.getElementById('rotationYValue').textContent = '0°';
                document.getElementById('scaleValue').textContent = '0.01';
                document.getElementById('roughnessValue').textContent = '0.80';
                document.getElementById('metalnessValue').textContent = '0.10';

                // 更新状态
                this.updatePositionStatus();
                this.updateRotationStatus();
                this.updateScaleStatus();
                document.getElementById('currentRoughness').textContent = '0.80';
                document.getElementById('currentMetalness').textContent = '0.10';
                document.getElementById('currentScale').textContent = '0.010';

                console.log('🔄 头盔已重置');
            }

            resetCamera() {
                this.camera.position.set(3, 2, 3);
                this.controls.reset();
                console.log('📷 相机视角已重置');
            }

            debugHelmet() {
                if (this.helmet) {
                    this.helmet.debugMaterials();
                } else {
                    console.log('❌ 头盔模型未加载');
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.helmet) {
                    this.helmet.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('HelmetModelApp disposed');
            }
        }

        // 启动应用
        const app = new HelmetModelApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
