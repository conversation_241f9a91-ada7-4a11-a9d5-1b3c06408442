<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ore Model Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #FF8C00 0%, #B8860B 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #FFD700;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            border-bottom: 1px solid #FFD700;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            color: #FFD700;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        button.primary {
            background: linear-gradient(135deg, #FF8C00, #FF6347);
            color: white;
        }

        button.primary:hover {
            background: linear-gradient(135deg, #FF6347, #FF4500);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 140, 0, 0.4);
        }

        button.secondary {
            background: linear-gradient(135deg, #B8860B, #DAA520);
            color: white;
        }

        button.secondary:hover {
            background: linear-gradient(135deg, #DAA520, #FFD700);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(218, 165, 32, 0.4);
        }

        button.danger {
            background: linear-gradient(135deg, #DC143C, #B22222);
            color: white;
        }

        button.danger:hover {
            background: linear-gradient(135deg, #B22222, #8B0000);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 20, 60, 0.4);
        }

        .slider-group {
            margin-bottom: 12px;
        }

        .slider-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .slider-value {
            color: #FFD700;
            font-weight: bold;
            min-width: 40px;
            text-align: right;
        }

        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #FFD700;
            font-weight: bold;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 16px;
            z-index: 1000;
        }

        #canvas-container {
            width: 100%;
            height: 100%;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #FF8C00, #FF6347);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
            z-index: 100;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #FF6347, #FF4500);
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 140, 0, 0.4);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #FF8C00, #FF6347);
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">🔶 加载矿石模型中...</div>
        
        <div id="canvas-container"></div>
        
        <!-- 控制面板 -->
        <div id="controls">
            <h1>🔶 矿石控制面板</h1>
            
            <!-- 基础控制 -->
            <div class="control-group">
                <h4>基础操作</h4>
                <div class="button-row">
                    <button id="debugBtn" class="secondary">调试信息</button>
                </div>
            </div>

            <!-- 位置调整 -->
            <div class="control-group">
                <h4>位置调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>X轴:</span>
                        <span class="slider-value" id="posXValue">0.0</span>
                    </div>
                    <input type="range" id="posXSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Y轴:</span>
                        <span class="slider-value" id="posYValue">0.0</span>
                    </div>
                    <input type="range" id="posYSlider" min="-5" max="5" step="0.1" value="0">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Z轴:</span>
                        <span class="slider-value" id="posZValue">0.0</span>
                    </div>
                    <input type="range" id="posZSlider" min="-5" max="5" step="0.1" value="0">
                </div>
            </div>

            <!-- 变换调整 -->
            <div class="control-group">
                <h4>变换调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>缩放:</span>
                        <span class="slider-value" id="scaleValue">0.010</span>
                    </div>
                    <input type="range" id="scaleSlider" min="0.001" max="0.1" step="0.001" value="0.01">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>Y轴旋转:</span>
                        <span class="slider-value" id="rotYValue">0°</span>
                    </div>
                    <input type="range" id="rotYSlider" min="0" max="360" step="1" value="0">
                </div>
            </div>

            <!-- 材质调整 -->
            <div class="control-group">
                <h4>材质调整</h4>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>粗糙度:</span>
                        <span class="slider-value" id="roughnessValue">0.5</span>
                    </div>
                    <input type="range" id="roughnessSlider" min="0" max="1" step="0.01" value="0.5">
                </div>
                <div class="slider-group">
                    <div class="slider-label">
                        <span>金属度:</span>
                        <span class="slider-value" id="metalnessValue">0.5</span>
                    </div>
                    <input type="range" id="metalnessSlider" min="0" max="1" step="0.01" value="0.5">
                </div>
                <div class="button-row">
                    <button id="resetMaterialBtn" class="secondary">重置材质</button>
                </div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div id="status">
            <h1>🔶 矿石状态</h1>
            <div class="status-item">
                <span class="status-label">名称:</span>
                <span class="status-value" id="nameStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">已加载:</span>
                <span class="status-value" id="loadedStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="positionStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="rotationStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="scaleStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">粗糙度:</span>
                <span class="status-value" id="roughnessStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">金属度:</span>
                <span class="status-value" id="metalnessStatus">-</span>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <button class="back-button" onclick="window.location.href='../index.html'">
        ← 返回主页
    </button>

    <script type="module">
        import * as THREE from '/node_modules/three/build/three.module.js';
        import { OrbitControls } from '/node_modules/three/examples/jsm/controls/OrbitControls.js';
        import { OreModel } from '/src/entities/OreModel.js';

        class OreApp {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.controls = null;
                this.ore = null;
                this.animationId = null;

                this.init();
                this.setupEventListeners();
            }

            init() {
                // 创建场景
                this.scene = new THREE.Scene();
                // 背景色 - 基于Cocos项目天空颜色和主光源色调的混合
                this.scene.background = new THREE.Color(0xFFF8E9);  // 与主光源颜色一致的暖白色

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(3, 2, 3);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.outputColorSpace = THREE.SRGBColorSpace;

                document.getElementById('canvas-container').appendChild(this.renderer.domElement);

                // 创建控制器
                this.controls = new OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.05;
                this.controls.screenSpacePanning = false;
                this.controls.minDistance = 1;
                this.controls.maxDistance = 100;
                this.controls.maxPolarAngle = Math.PI / 2;

                // 设置光照
                this.setupLighting();

                // 添加地面
                this.addGround();

                // 创建并加载矿石模型
                this.createOreModel();

                // 开始渲染循环
                this.animate();

                // 窗口大小调整
                window.addEventListener('resize', () => this.onWindowResize());
            }

            setupLighting() {
                // 环境光 - 精确复现Cocos项目配置
                // 天空颜色: (1, 1, 1, 0.52) - 白色，强度0.52
                const ambientLight = new THREE.AmbientLight(0xFFFFFF, 0.52);
                this.scene.add(ambientLight);

                // 主光源 - 精确复现Cocos项目主光源配置
                // 颜色: RGB(255, 248, 233) - 暖白色调
                // 强度: 1.69（LDR）
                // 位置: 欧拉角(-117.9°, 174.1°, 38.6°) 转换后的近似位置
                const directionalLight = new THREE.DirectionalLight(0xFFF8E9, 1.69);
                directionalLight.position.set(-8, 12, 6);  // 根据欧拉角转换的近似位置
                directionalLight.castShadow = true;

                // 阴影配置 - 参考Cocos项目设置
                directionalLight.shadow.mapSize.width = 1024;   // Cocos项目使用1024x1024
                directionalLight.shadow.mapSize.height = 1024;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 注意: 原Cocos项目使用单一主光源模型，不使用补充光源
                console.log('🔶 已应用Cocos项目精确光照配置');
            }

            addGround() {
                // 地面 - 精确复现Cocos项目地面反照率配置
                // 地面反照率（LDR）: (0.618, 0.578, 0.545, 0) - 暖黄色调
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: new THREE.Color(0.618, 0.578, 0.545)  // 精确的地面反照率颜色
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async createOreModel() {
                try {
                    // 创建矿石模型实例
                    this.ore = new OreModel(this.scene);

                    // 设置回调
                    this.ore.setEventCallbacks({
                        onLoadComplete: () => {
                            document.getElementById('loading').style.display = 'none';
                            this.updateStatus();
                            console.log('矿石模型加载完成');

                            // 启用模型阴影
                            const model = this.ore.getModel();
                            if (model) {
                                model.traverse((child) => {
                                    if (child instanceof THREE.Mesh) {
                                        child.castShadow = true;
                                        child.receiveShadow = true;
                                    }
                                });
                            }
                        },
                        onTextureLoaded: () => {
                            console.log('矿石纹理加载完成');
                        }
                    });

                    // 初始化模型
                    await this.ore.initialize();

                } catch (error) {
                    console.error('矿石模型创建失败:', error);
                    document.getElementById('loading').textContent = '❌ 模型加载失败';
                }
            }

            setupEventListeners() {
                // 位置调整滑块
                const posXSlider = document.getElementById('posXSlider');
                const posXValue = document.getElementById('posXValue');
                posXSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posXValue.textContent = value.toFixed(1);
                    if (this.ore) {
                        const pos = this.ore.getPosition();
                        this.ore.setPosition(new THREE.Vector3(value, pos.y, pos.z));
                        this.updateStatus();
                    }
                });

                const posYSlider = document.getElementById('posYSlider');
                const posYValue = document.getElementById('posYValue');
                posYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posYValue.textContent = value.toFixed(1);
                    if (this.ore) {
                        const pos = this.ore.getPosition();
                        this.ore.setPosition(new THREE.Vector3(pos.x, value, pos.z));
                        this.updateStatus();
                    }
                });

                const posZSlider = document.getElementById('posZSlider');
                const posZValue = document.getElementById('posZValue');
                posZSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    posZValue.textContent = value.toFixed(1);
                    if (this.ore) {
                        const pos = this.ore.getPosition();
                        this.ore.setPosition(new THREE.Vector3(pos.x, pos.y, value));
                        this.updateStatus();
                    }
                });

                // 变换调整滑块
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    scaleValue.textContent = value.toFixed(3);
                    if (this.ore) {
                        this.ore.setScale(value);
                        this.updateStatus();
                    }
                });

                const rotYSlider = document.getElementById('rotYSlider');
                const rotYValue = document.getElementById('rotYValue');
                rotYSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    rotYValue.textContent = value + '°';
                    if (this.ore) {
                        const rot = this.ore.getRotation();
                        this.ore.setRotation(new THREE.Euler(rot.x, THREE.MathUtils.degToRad(value), rot.z));
                        this.updateStatus();
                    }
                });

                // 材质调整滑块
                const roughnessSlider = document.getElementById('roughnessSlider');
                const roughnessValue = document.getElementById('roughnessValue');
                roughnessSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    roughnessValue.textContent = value.toFixed(2);
                    if (this.ore) {
                        this.ore.setRoughness(value);
                        this.updateStatus();
                    }
                });

                const metalnessSlider = document.getElementById('metalnessSlider');
                const metalnessValue = document.getElementById('metalnessValue');
                metalnessSlider.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    metalnessValue.textContent = value.toFixed(2);
                    if (this.ore) {
                        this.ore.setMetalness(value);
                        this.updateStatus();
                    }
                });

                // 重置材质按钮
                document.getElementById('resetMaterialBtn').addEventListener('click', () => {
                    if (this.ore) {
                        this.ore.resetMaterialProperties();
                        roughnessSlider.value = '0.5';
                        roughnessValue.textContent = '0.50';
                        metalnessSlider.value = '0.5';
                        metalnessValue.textContent = '0.50';
                        this.updateStatus();
                    }
                });

                // 调试按钮
                document.getElementById('debugBtn').addEventListener('click', () => {
                    if (this.ore) {
                        this.ore.debugMaterials();
                    }
                });
            }

            loadOre() {
                if (this.ore) {
                    this.ore.initialize().then(() => {
                        console.log('矿石模型重新加载完成');
                    }).catch((error) => {
                        console.error('矿石模型重新加载失败:', error);
                    });
                }

                // 重置材质
                this.ore.resetMaterialProperties();
                document.getElementById('roughnessSlider').value = '0.5';
                document.getElementById('roughnessValue').textContent = '0.50';
                document.getElementById('metalnessSlider').value = '0.5';
                document.getElementById('metalnessValue').textContent = '0.50';

                this.updateStatus();
            }

            updateStatus() {
                if (!this.ore) return;

                const status = this.ore.getStatus();
                const position = status.position;
                const rotation = status.rotation;

                // 更新状态面板
                document.getElementById('nameStatus').textContent = status.name;
                document.getElementById('loadedStatus').textContent = status.loaded ? '是' : '否';
                document.getElementById('positionStatus').textContent =
                    `(${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`;
                document.getElementById('rotationStatus').textContent =
                    `(${rotation.x.toFixed(2)}, ${rotation.y.toFixed(2)}, ${rotation.z.toFixed(2)})`;
                document.getElementById('scaleStatus').textContent = status.scale.toFixed(3);
                document.getElementById('roughnessStatus').textContent = status.roughness.toFixed(2);
                document.getElementById('metalnessStatus').textContent = status.metalness.toFixed(2);
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // 更新控制器
                this.controls.update();

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            dispose() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                if (this.ore) {
                    this.ore.dispose();
                }

                if (this.renderer) {
                    this.renderer.dispose();
                }

                console.log('🔶 矿石应用已清理');
            }
        }

        // 创建应用实例
        const app = new OreApp();

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            app.dispose();
        });
    </script>
</body>
</html>
