<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tree02 Model - 3D树模型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            position: relative;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 15px;
            color: white;
            min-width: 200px;
            max-width: 250px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        #controls h3, #status h3 {
            margin: 0 0 10px 0;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group h4 {
            margin: 0 0 8px 0;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
        }

        .button-row {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        button {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 80px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .danger {
            background: linear-gradient(45deg, #ff416c, #ff4b2b);
            color: white;
        }

        .info {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .slider-control {
            margin-bottom: 12px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-size: 12px;
        }

        .slider-control input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .value-display {
            font-size: 11px;
            color: #fff;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3px 6px;
            border-radius: 4px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-label {
            color: #ccc;
        }

        .status-value {
            color: #fff;
            font-weight: 500;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 12px;
            padding: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 滚动条样式 */
        #controls::-webkit-scrollbar,
        #status::-webkit-scrollbar {
            width: 6px;
        }

        #controls::-webkit-scrollbar-track,
        #status::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb,
        #status::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        #controls::-webkit-scrollbar-thumb:hover,
        #status::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox 滚动条 */
        #controls,
        #status {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        /* 返回按钮 */
        .back-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            z-index: 100;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(76, 175, 80, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls, #status {
                position: relative;
                top: auto;
                left: auto;
                right: auto;
                width: calc(100% - 20px);
                margin: 10px;
                max-height: 200px;
            }

            #controls {
                order: 1;
            }

            #status {
                order: 2;
            }

            body {
                overflow-y: auto;
            }

            #container {
                height: auto;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }

            canvas {
                order: 3;
                height: 60vh !important;
            }

            .back-button {
                bottom: 10px;
                right: 10px;
                padding: 10px 16px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            #controls, #status {
                font-size: 12px;
                padding: 10px;
            }

            button {
                padding: 6px 8px;
                font-size: 11px;
            }

            .slider-control label {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Tree02 Model...</div>
        </div>

        <div id="controls">
            <h3>🌳 Tree02模型控制</h3>

            <div class="control-group">
                <h4>树状态控制</h4>
                <div class="button-row">
                    <button id="staticBtn" class="success">🟢 静态</button>
                    <button id="swayingBtn" class="info">🌿 摆动</button>
                </div>
                <div class="button-row">
                    <button id="windyBtn" class="danger">💨 强风</button>
                </div>
            </div>

            <div class="control-group">
                <h4>变换控制</h4>
                <div class="slider-control">
                    <label for="scaleSlider">缩放：</label>
                    <input type="range" id="scaleSlider" min="0.005" max="0.1" step="0.001" value="0.02">
                    <div class="value-display" id="scaleValue">0.02</div>
                </div>
                <div class="slider-control">
                    <label for="posXSlider">位置 X：</label>
                    <input type="range" id="posXSlider" min="-10" max="10" step="0.1" value="0">
                    <div class="value-display" id="posXValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="posYSlider">位置 Y：</label>
                    <input type="range" id="posYSlider" min="-5" max="5" step="0.1" value="0">
                    <div class="value-display" id="posYValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="posZSlider">位置 Z：</label>
                    <input type="range" id="posZSlider" min="-10" max="10" step="0.1" value="0">
                    <div class="value-display" id="posZValue">0</div>
                </div>
                <div class="slider-control">
                    <label for="rotYSlider">旋转 Y：</label>
                    <input type="range" id="rotYSlider" min="0" max="6.28" step="0.1" value="0">
                    <div class="value-display" id="rotYValue">0</div>
                </div>
            </div>

            <div class="control-group">
                <h4>摆动参数</h4>
                <div class="slider-control">
                    <label for="swayIntensitySlider">摆动强度：</label>
                    <input type="range" id="swayIntensitySlider" min="0" max="2" step="0.1" value="0.5">
                    <div class="value-display" id="swayIntensityValue">0.5</div>
                </div>
                <div class="slider-control">
                    <label for="swaySpeedSlider">摆动速度：</label>
                    <input type="range" id="swaySpeedSlider" min="0.1" max="5" step="0.1" value="1.0">
                    <div class="value-display" id="swaySpeedValue">1.0</div>
                </div>
                <div class="slider-control">
                    <label for="windStrengthSlider">风力强度：</label>
                    <input type="range" id="windStrengthSlider" min="0" max="3" step="0.1" value="1.0">
                    <div class="value-display" id="windStrengthValue">1.0</div>
                </div>
            </div>

            <div class="control-group">
                <h4>调试工具</h4>
                <div class="button-row">
                    <button id="resetBtn" class="primary">重置位置</button>
                </div>
                <div class="button-row">
                    <button id="debugBtn" class="info">调试材质</button>
                </div>

                <h4>颜色调整</h4>
                <div class="slider-control">
                    <label for="colorWarmth">色温调整 (模拟Cocos效果)</label>
                    <input type="range" id="colorWarmth" min="0" max="100" value="0" step="1">
                    <div class="value-display">当前值: <span id="colorWarmthValue">0</span></div>
                </div>
                <div class="button-row">
                    <button id="resetColorBtn" class="secondary">重置颜色</button>
                </div>
            </div>
        </div>

        <div id="status">
            <h3>📊 模型状态</h3>
            <div class="status-item">
                <span class="status-label">名称:</span>
                <span class="status-value" id="nameStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">已加载:</span>
                <span class="status-value" id="loadedStatus">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前状态:</span>
                <span class="status-value" id="currentStateStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">位置:</span>
                <span class="status-value" id="positionStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">旋转:</span>
                <span class="status-value" id="rotationStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">缩放:</span>
                <span class="status-value" id="scaleStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">摆动强度:</span>
                <span class="status-value" id="swayIntensityStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">摆动速度:</span>
                <span class="status-value" id="swaySpeedStatus">-</span>
            </div>
            <div class="status-item">
                <span class="status-label">风力强度:</span>
                <span class="status-value" id="windStrengthStatus">-</span>
            </div>
        </div>

        <!-- 返回按钮 -->
        <button class="back-button" onclick="window.location.href='../index.html'">
            ← 返回主页
        </button>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { Tree02Model, Tree02State } from '../src/entities/Tree02Model.js';

        class Tree02Demo {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.tree = null;

                this.init();
            }

            async init() {
                // 设置渲染器
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x87CEEB, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 设置相机位置
                this.camera.position.set(5, 3, 5);
                this.camera.lookAt(0, 0, 0);

                // 添加光照
                this.setupLights();

                // 添加地面
                this.addGround();

                // 创建Tree02模型
                await this.createTree();

                // 设置控制事件
                this.setupControls();

                // 开始渲染循环
                this.animate();

                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            }

            setupLights() {
                // 更温暖的环境光，模拟Cocos Creator的光照环境
                const ambientLight = new THREE.AmbientLight(0xfff4e6, 0.6); // 温暖的环境光
                this.scene.add(ambientLight);

                // 主方向光 - 模拟阳光
                const directionalLight = new THREE.DirectionalLight(0xfff8dc, 1.2); // 温暖的阳光色
                directionalLight.position.set(10, 15, 8);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 50;
                directionalLight.shadow.camera.left = -10;
                directionalLight.shadow.camera.right = 10;
                directionalLight.shadow.camera.top = 10;
                directionalLight.shadow.camera.bottom = -10;
                this.scene.add(directionalLight);

                // 添加补充光源，增强金黄色效果
                const fillLight = new THREE.DirectionalLight(0xffeb99, 0.3);
                fillLight.position.set(-5, 8, -5);
                this.scene.add(fillLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(20, 20);
                const groundMaterial = new THREE.MeshLambertMaterial({
                    color: 0x8B4513,  // 棕色地面，与Tree02的暖棕色调协调
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);
            }

            async createTree() {
                this.tree = Tree02Model.create(this.scene, 'Tree02Model');

                // 设置事件回调
                this.tree.setEventCallbacks({
                    onLoadComplete: () => {
                        console.log('🌳 Tree02模型加载完成');
                        this.updateStatus();

                        // 启用模型阴影
                        const model = this.tree.getModel();
                        if (model) {
                            model.traverse((child) => {
                                if (child instanceof THREE.Mesh) {
                                    child.castShadow = true;
                                    child.receiveShadow = true;
                                }
                            });
                        }
                    },
                    onStateChange: (state) => {
                        console.log(`Tree02 state changed to: ${state}`);
                        this.updateStatus();
                    }
                });

                await this.tree.initialize();
            }

            setupControls() {
                // 状态控制按钮
                document.getElementById('staticBtn').addEventListener('click', () => {
                    this.tree.setState(Tree02State.STATIC);
                    this.updateStatus();
                });

                document.getElementById('swayingBtn').addEventListener('click', () => {
                    this.tree.setState(Tree02State.SWAYING);
                    this.updateStatus();
                });

                document.getElementById('windyBtn').addEventListener('click', () => {
                    this.tree.setState(Tree02State.WINDY);
                    this.updateStatus();
                });

                // 重置按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.tree.setPosition(new THREE.Vector3(0, 0, 0));
                    this.tree.setRotation(new THREE.Euler(0, 0, 0));
                    this.tree.setScale(0.02);
                    this.updateSliders();
                    this.updateStatus();
                });

                // 缩放控制
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                scaleSlider.addEventListener('input', () => {
                    const scale = parseFloat(scaleSlider.value);
                    this.tree.setScale(scale);
                    scaleValue.textContent = scale.toFixed(3);
                    this.updateStatus();
                });

                // 位置控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    const slider = document.getElementById(`pos${axis}Slider`);
                    const value = document.getElementById(`pos${axis}Value`);
                    slider.addEventListener('input', () => {
                        const pos = this.tree.getPosition();
                        pos[axis.toLowerCase()] = parseFloat(slider.value);
                        this.tree.setPosition(pos);
                        value.textContent = slider.value;
                        this.updateStatus();
                    });
                });

                // 旋转控制
                const rotYSlider = document.getElementById('rotYSlider');
                const rotYValue = document.getElementById('rotYValue');
                rotYSlider.addEventListener('input', () => {
                    const rot = this.tree.getRotation();
                    rot.y = parseFloat(rotYSlider.value);
                    this.tree.setRotation(rot);
                    rotYValue.textContent = parseFloat(rotYSlider.value).toFixed(2);
                    this.updateStatus();
                });

                // 摆动参数控制
                const swayIntensitySlider = document.getElementById('swayIntensitySlider');
                const swayIntensityValue = document.getElementById('swayIntensityValue');
                swayIntensitySlider.addEventListener('input', () => {
                    const intensity = parseFloat(swayIntensitySlider.value);
                    this.updateSwayParameters();
                    swayIntensityValue.textContent = intensity.toFixed(1);
                });

                const swaySpeedSlider = document.getElementById('swaySpeedSlider');
                const swaySpeedValue = document.getElementById('swaySpeedValue');
                swaySpeedSlider.addEventListener('input', () => {
                    const speed = parseFloat(swaySpeedSlider.value);
                    this.updateSwayParameters();
                    swaySpeedValue.textContent = speed.toFixed(1);
                });

                const windStrengthSlider = document.getElementById('windStrengthSlider');
                const windStrengthValue = document.getElementById('windStrengthValue');
                windStrengthSlider.addEventListener('input', () => {
                    const strength = parseFloat(windStrengthSlider.value);
                    this.updateSwayParameters();
                    windStrengthValue.textContent = strength.toFixed(1);
                });

                // 调试按钮
                document.getElementById('debugBtn').addEventListener('click', async () => {
                    await this.tree.debugMaterials();
                });

                // 颜色调整
                const colorWarmthSlider = document.getElementById('colorWarmth');
                const colorWarmthValue = document.getElementById('colorWarmthValue');

                colorWarmthSlider.addEventListener('input', (e) => {
                    const warmth = parseFloat(e.target.value);
                    colorWarmthValue.textContent = warmth;
                    this.adjustColorWarmth(warmth);
                });

                // 重置颜色按钮
                document.getElementById('resetColorBtn').addEventListener('click', () => {
                    colorWarmthSlider.value = 0;
                    colorWarmthValue.textContent = '0';
                    this.adjustColorWarmth(0);
                });

                // 窗口调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            updateSwayParameters() {
                const intensity = parseFloat(document.getElementById('swayIntensitySlider').value);
                const speed = parseFloat(document.getElementById('swaySpeedSlider').value);
                const strength = parseFloat(document.getElementById('windStrengthSlider').value);

                this.tree.setSwayParameters(intensity, speed, strength);
                this.updateStatus();
            }

            updateSliders() {
                if (!this.tree) return;

                const status = this.tree.getStatus();

                // 更新滑块值
                document.getElementById('scaleSlider').value = status.scale;
                document.getElementById('scaleValue').textContent = status.scale.toFixed(3);

                document.getElementById('posXSlider').value = status.position.x;
                document.getElementById('posXValue').textContent = status.position.x.toFixed(1);

                document.getElementById('posYSlider').value = status.position.y;
                document.getElementById('posYValue').textContent = status.position.y.toFixed(1);

                document.getElementById('posZSlider').value = status.position.z;
                document.getElementById('posZValue').textContent = status.position.z.toFixed(1);

                document.getElementById('rotYSlider').value = status.rotation.y;
                document.getElementById('rotYValue').textContent = status.rotation.y.toFixed(2);

                document.getElementById('swayIntensitySlider').value = status.swayIntensity;
                document.getElementById('swayIntensityValue').textContent = status.swayIntensity.toFixed(1);

                document.getElementById('swaySpeedSlider').value = status.swaySpeed;
                document.getElementById('swaySpeedValue').textContent = status.swaySpeed.toFixed(1);

                document.getElementById('windStrengthSlider').value = status.windStrength;
                document.getElementById('windStrengthValue').textContent = status.windStrength.toFixed(1);
            }

            updateStatus() {
                if (!this.tree) return;

                const status = this.tree.getStatus();

                document.getElementById('nameStatus').textContent = status.name;
                document.getElementById('loadedStatus').textContent = status.loaded ? '是' : '否';
                document.getElementById('currentStateStatus').textContent = status.state;
                document.getElementById('positionStatus').textContent =
                    `(${status.position.x.toFixed(1)}, ${status.position.y.toFixed(1)}, ${status.position.z.toFixed(1)})`;
                document.getElementById('rotationStatus').textContent =
                    `(${status.rotation.x.toFixed(2)}, ${status.rotation.y.toFixed(2)}, ${status.rotation.z.toFixed(2)})`;
                document.getElementById('scaleStatus').textContent = status.scale.toFixed(3);
                document.getElementById('swayIntensityStatus').textContent = status.swayIntensity.toFixed(1);
                document.getElementById('swaySpeedStatus').textContent = status.swaySpeed.toFixed(1);
                document.getElementById('windStrengthStatus').textContent = status.windStrength.toFixed(1);
            }

            /**
             * 调整颜色温度，模拟Cocos Creator的效果
             * @param {number} warmth - 温度值 (0-100)
             */
            adjustColorWarmth(warmth) {
                if (!this.tree || !this.tree.model) return;

                // 基础颜色 RGB(216, 148, 73) -> #d89449
                const baseColor = new THREE.Color(0xd89449);

                // 根据warmth值调整颜色，增加金黄色调
                const warmthFactor = warmth / 100;

                // 向金黄色调整
                const warmColor = new THREE.Color(0xffd700); // 金色
                const adjustedColor = baseColor.clone().lerp(warmColor, warmthFactor * 0.3);

                // 应用到所有材质
                this.tree.model.traverse((child) => {
                    if (child instanceof THREE.Mesh && child.material) {
                        const materials = Array.isArray(child.material) ? child.material : [child.material];

                        materials.forEach((material) => {
                            if (material.color) {
                                material.color.copy(adjustedColor);
                            }
                        });
                    }
                });

                console.log(`🎨 颜色温度调整: ${warmth}%, 颜色: #${adjustedColor.getHexString()}`);
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                // 更新树
                if (this.tree) {
                    this.tree.update();
                }

                this.renderer.render(this.scene, this.camera);
            }
        }

        // 启动演示
        new Tree02Demo();
    </script>
</body>
</html>
