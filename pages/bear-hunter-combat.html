<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bear vs Hunter Combat Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 320px;
            max-width: 400px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 300px;
            max-width: 400px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #battle-log {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.85);
            border-radius: 15px;
            padding: 15px;
            color: white;
            height: 150px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            margin: 0 0 12px 0;
            color: #64b5f6;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        button {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 100px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .battle-start {
            background: linear-gradient(45deg, #4caf50, #66bb6a);
            color: white;
        }

        .battle-stop {
            background: linear-gradient(45deg, #f44336, #ef5350);
            color: white;
        }

        .battle-reset {
            background: linear-gradient(45deg, #ff9800, #ffb74d);
            color: white;
        }

        .config-btn {
            background: linear-gradient(45deg, #2196f3, #42a5f5);
            color: white;
        }

        .slider-control {
            margin-bottom: 15px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 8px;
            color: #e3f2fd;
            font-size: 13px;
            font-weight: 500;
        }

        .slider-control input[type="range"] {
            width: 100%;
            margin-bottom: 8px;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: linear-gradient(to right, #2196f3, #1976d2);
            outline: none;
        }

        .slider-control input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .value-display {
            font-size: 12px;
            color: #64b5f6;
            text-align: center;
            background: rgba(100, 181, 246, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            color: #b0bec5;
            font-weight: 500;
        }

        .status-value {
            color: #fff;
            font-weight: 600;
        }

        .team-status {
            background: rgba(255, 255, 255, 0.05);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid;
        }

        .team-bears {
            border-left-color: #d32f2f;
        }

        .team-hunters {
            border-left-color: #1976d2;
        }

        .team-title {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .health-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }

        .health-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .health-bears {
            background: linear-gradient(45deg, #d32f2f, #f44336);
        }

        .health-hunters {
            background: linear-gradient(45deg, #1976d2, #2196f3);
        }

        .battle-timer {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            color: #64b5f6;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .winner-announcement {
            background: linear-gradient(45deg, #4caf50, #66bb6a);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin: 15px 0;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            animation: winner-glow 2s infinite alternate;
        }

        @keyframes winner-glow {
            0% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.5); }
            100% { box-shadow: 0 0 30px rgba(76, 175, 80, 0.8); }
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.95);
            border-radius: 15px;
            padding: 40px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.8);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #2196f3;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-damage {
            color: #ff5722;
        }

        .log-death {
            color: #f44336;
            font-weight: bold;
        }

        .log-meat {
            color: #8bc34a;
        }

        .log-battle {
            color: #2196f3;
            font-weight: bold;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Bear vs Hunter Combat System...</div>
            <div style="font-size: 12px; margin-top: 10px; color: #bdc3c7;">
                Initializing models, weapons, and combat AI...
            </div>
        </div>

        <div id="controls">
            <h3>⚔️ 熊猎人战斗系统</h3>
            
            <div class="control-group">
                <h4>战斗控制</h4>
                <div class="button-row">
                    <button id="startBattleBtn" class="battle-start">开始战斗</button>
                    <button id="stopBattleBtn" class="battle-stop" disabled>停止战斗</button>
                </div>
                <div class="button-row">
                    <button id="resetBattleBtn" class="battle-reset">重置战斗</button>
                    <button id="collectMeatBtn" class="config-btn">收集肉块</button>
                </div>
            </div>

            <div class="control-group">
                <h4>操作说明</h4>
                <div style="background: rgba(100, 181, 246, 0.1); padding: 12px; border-radius: 8px; border-left: 3px solid #64b5f6;">
                    <div style="color: #64b5f6; font-weight: bold; margin-bottom: 8px;">🎮 自动控制模式</div>
                    <div style="font-size: 14px; line-height: 1.5; color: #e3f2fd;">
                        • 战斗开始后自动启用玩家控制<br>
                        • <strong>WASD</strong> - 移动猎人<br>
                        • <strong>Shift</strong> - 跑步<br>
                        • <strong>Space</strong> - 射击<br>
                        • <strong>C</strong> - 瞄准射击
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>战斗配置</h4>
                <div class="slider-control">
                    <label for="bearCountSlider">熊的数量：</label>
                    <input type="range" id="bearCountSlider" min="1" max="5" step="1" value="3">
                    <div class="value-display" id="bearCountValue">3</div>
                </div>
                <div class="slider-control">
                    <label for="hunterCountSlider">猎人数量：</label>
                    <input type="range" id="hunterCountSlider" min="1" max="4" step="1" value="1">
                    <div class="value-display" id="hunterCountValue">1</div>
                </div>
                <div class="slider-control">
                    <label for="bearHealthSlider">熊血量：</label>
                    <input type="range" id="bearHealthSlider" min="50" max="200" step="10" value="100">
                    <div class="value-display" id="bearHealthValue">100</div>
                </div>
                <div class="slider-control">
                    <label for="hunterHealthSlider">猎人血量：</label>
                    <input type="range" id="hunterHealthSlider" min="50" max="200" step="10" value="100">
                    <div class="value-display" id="hunterHealthValue">100</div>
                </div>
                <div class="slider-control">
                    <label for="meatDropChanceSlider">肉块掉落率：</label>
                    <input type="range" id="meatDropChanceSlider" min="0" max="1" step="0.1" value="0.8">
                    <div class="value-display" id="meatDropChanceValue">80%</div>
                </div>
            </div>
        </div>

        <div id="status">
            <h3>📊 战斗状态</h3>
            
            <div class="battle-timer" id="battleTimer">00:00</div>
            
            <div id="winnerAnnouncement" class="winner-announcement" style="display: none;"></div>
            
            <div class="team-status team-bears">
                <div class="team-title">🐻 熊队</div>
                <div class="status-item">
                    <span class="status-label">存活:</span>
                    <span class="status-value" id="bearsAlive">0/0</span>
                </div>
                <div class="health-bar">
                    <div class="health-fill health-bears" id="bearsHealthBar" style="width: 100%;"></div>
                </div>
                <div class="status-item">
                    <span class="status-label">击杀:</span>
                    <span class="status-value" id="bearsKills">0</span>
                </div>
            </div>

            <div class="team-status team-hunters">
                <div class="team-title">🔫 猎人队</div>
                <div class="status-item">
                    <span class="status-label">存活:</span>
                    <span class="status-value" id="huntersAlive">0/0</span>
                </div>
                <div class="health-bar">
                    <div class="health-fill health-hunters" id="huntersHealthBar" style="width: 100%;"></div>
                </div>
                <div class="status-item">
                    <span class="status-label">击杀:</span>
                    <span class="status-value" id="huntersKills">0</span>
                </div>
            </div>

            <div class="control-group">
                <h4>战利品</h4>
                <div class="status-item">
                    <span class="status-label">🥩 肉块掉落:</span>
                    <span class="status-value" id="meatDropped">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">📦 可收集:</span>
                    <span class="status-value" id="meatAvailable">0</span>
                </div>
            </div>

            <div class="control-group">
                <h4>猎人状态</h4>
                <div class="status-item">
                    <span class="status-label">👨 控制角色:</span>
                    <span class="status-value" id="controlledHunter">无</span>
                </div>
                <div class="status-item">
                    <span class="status-label">❤️ 角色血量:</span>
                    <span class="status-value" id="hunterHealth">-/-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">🏃 移动状态:</span>
                    <span class="status-value" id="hunterMovement">静止</span>
                </div>
            </div>
        </div>

        <div id="battle-log">
            <div id="logContent"></div>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { BearHunterCombat } from '../src/examples/bear-hunter-combat.js';

        class CombatApp {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.combat = null;
                this.battleStartTime = 0;
                this.updateInterval = null;
                
                // 玩家控制
                this.keys = {};
                this.isPlayerControlActive = false;
                
                this.init();
            }

            async init() {
                // 设置渲染器
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0xffffff, 1); // 白色背景
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.0;
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 设置相机位置 - 固定俯视角度，拉近观察
                this.camera.position.set(0, 20, 12);
                this.camera.lookAt(0, 0, 0);

                // 添加光照
                this.setupLights();

                // 添加地面
                this.addGround();

                // 创建战斗系统
                this.combat = new BearHunterCombat(this.scene);

                // 设置事件回调
                this.combat.setEventCallbacks({
                    onBattleStart: () => {
                        this.log('⚔️ 战斗开始！', 'battle');
                        this.battleStartTime = Date.now();
                        this.updateControls(true);
                    },
                    onBattleEnd: (winner, stats) => {
                        this.log(`🏁 战斗结束！获胜者: ${this.getWinnerText(winner)}`, 'battle');
                        
                        // 战斗结束时自动禁用玩家控制
                        if (this.isPlayerControlActive) {
                            this.disablePlayerControl();
                        }
                        
                        this.showWinner(winner);
                        this.updateControls(false);
                    },
                    onParticipantDeath: (participant, killer) => {
                        const killerText = killer ? ` (被 ${killer.id} 杀死)` : '';
                        this.log(`💀 ${participant.id} 死亡${killerText}`, 'death');
                    },
                    onMeatDrop: (meat, deadBear) => {
                        this.log(`🥩 ${deadBear.id} 掉落了肉块！`, 'meat');
                    },
                    onDamageDealt: (attacker, target, damage) => {
                        const emoji = attacker.type === 'bear' ? '🐻' : '🔫';
                        this.log(`${emoji} ${attacker.id} 对 ${target.id} 造成 ${damage} 伤害`, 'damage');
                    }
                });

                // 设置控制事件
                this.setupControls();

                // 初始化战斗系统
                await this.combat.initializeCombat();

                // 开始渲染循环
                this.animate();

                // 开始状态更新
                this.startStatusUpdates();

                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            }

            setupLights() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x606060, 0.4); // 更亮的环境光适配白色背景
                this.scene.add(ambientLight);

                // 主方向光（太阳光）
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(20, 30, 10);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                directionalLight.shadow.camera.near = 0.5;
                directionalLight.shadow.camera.far = 100;
                directionalLight.shadow.camera.left = -25;
                directionalLight.shadow.camera.right = 25;
                directionalLight.shadow.camera.top = 25;
                directionalLight.shadow.camera.bottom = -25;
                this.scene.add(directionalLight);

                // 补充光
                const fillLight = new THREE.DirectionalLight(0xf0f0f0, 0.25); // 柔和的白色补充光
                fillLight.position.set(-10, 20, -10);
                this.scene.add(fillLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(50, 50);
                const groundMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0xf5f5f5,  // 浅灰白色
                    transparent: true,
                    opacity: 0.9
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);

                // 添加网格辅助线
                const gridHelper = new THREE.GridHelper(50, 50, 0xcccccc, 0xe0e0e0); // 浅灰色网格
                gridHelper.position.y = -0.49;
                this.scene.add(gridHelper);
            }

            setupControls() {
                // 战斗控制按钮
                document.getElementById('startBattleBtn').addEventListener('click', () => {
                    this.startBattle();
                });

                document.getElementById('stopBattleBtn').addEventListener('click', () => {
                    this.stopBattle();
                });

                document.getElementById('resetBattleBtn').addEventListener('click', async () => {
                    await this.resetBattle();
                });

                document.getElementById('collectMeatBtn').addEventListener('click', () => {
                    this.collectAllMeat();
                });

                // 玩家控制已自动启用，无需手动按钮

                // 键盘控制事件
                document.addEventListener('keydown', (e) => this.onKeyDown(e));
                document.addEventListener('keyup', (e) => this.onKeyUp(e));

                // 配置滑块
                this.setupConfigSliders();

                // 窗口调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            setupConfigSliders() {
                const sliders = [
                    { id: 'bearCount', property: 'bearCount' },
                    { id: 'hunterCount', property: 'hunterCount' },
                    { id: 'bearHealth', property: 'bearHealth' },
                    { id: 'hunterHealth', property: 'hunterHealth' },
                    { id: 'meatDropChance', property: 'meatDropChance', isPercent: true }
                ];

                sliders.forEach(({ id, property, isPercent }) => {
                    const slider = document.getElementById(`${id}Slider`);
                    const value = document.getElementById(`${id}Value`);
                    
                    slider.addEventListener('input', () => {
                        const val = parseFloat(slider.value);
                        if (isPercent) {
                            value.textContent = `${Math.round(val * 100)}%`;
                        } else {
                            value.textContent = val.toString();
                        }
                        
                        this.combat.updateConfig({ [property]: val });
                    });
                });
            }

            startBattle() {
                if (this.combat) {
                    this.combat.startBattle();
                    
                    // 默认开启手动控制
                    setTimeout(() => {
                        this.enablePlayerControl(true); // true表示自动启用
                    }, 500); // 延迟500ms确保战斗系统完全初始化
                }
            }

            stopBattle() {
                // 停止战斗逻辑（如果需要）
                
                // 如果玩家控制处于激活状态，先禁用它
                if (this.isPlayerControlActive) {
                    this.disablePlayerControl();
                }
                
                this.updateControls(false);
                this.log('⏹️ 战斗被手动停止', 'battle');
            }

            async resetBattle() {
                this.log('🔄 重置战斗...', 'battle');
                this.clearLog();
                document.getElementById('winnerAnnouncement').style.display = 'none';
                
                // 如果玩家控制处于激活状态，先禁用它
                if (this.isPlayerControlActive) {
                    this.disablePlayerControl();
                }
                
                if (this.combat) {
                    await this.combat.resetBattle();
                }
                
                this.updateControls(false);
                this.log('✅ 战斗重置完成', 'battle');
            }

            collectAllMeat() {
                if (!this.combat) return;
                
                const meatDrops = this.combat.getMeatDrops();
                let collected = 0;
                
                for (const [meatId, meatDrop] of meatDrops) {
                    if (this.combat.collectMeat(meatId)) {
                        collected++;
                    }
                }
                
                if (collected > 0) {
                    this.log(`📦 收集了 ${collected} 个肉块`, 'meat');
                } else {
                    this.log('📦 没有肉块可收集', 'meat');
                }
            }

            updateControls(battleActive) {
                document.getElementById('startBattleBtn').disabled = battleActive;
                document.getElementById('stopBattleBtn').disabled = !battleActive;
                document.getElementById('resetBattleBtn').disabled = battleActive;
                
                // 玩家控制现在自动管理，无需手动按钮控制
            }

            startStatusUpdates() {
                this.updateInterval = setInterval(() => {
                    this.updateStatus();
                }, 200);
            }

            onKeyDown(e) {
                this.keys[e.code] = true;
                
                // 防止默认行为
                if (['Space', 'ShiftLeft', 'ShiftRight'].includes(e.code)) {
                    e.preventDefault();
                }

                this.updateKeyboardMovement();
                this.handleActionKeys(e.code);
            }

            onKeyUp(e) {
                this.keys[e.code] = false;
                this.updateKeyboardMovement();
            }

            updateKeyboardMovement() {
                if (!this.combat || !this.combat.isPlayerControlActive()) return;

                const forward = this.keys['KeyW'] ? 1 : 0;
                const backward = this.keys['KeyS'] ? 1 : 0;
                const left = this.keys['KeyA'] ? 1 : 0;
                const right = this.keys['KeyD'] ? 1 : 0;
                const isRunning = this.keys['ShiftLeft'] || this.keys['ShiftRight'];

                this.combat.playerMoveKeyboard(forward, backward, left, right, isRunning);
            }

            handleActionKeys(code) {
                if (!this.combat || !this.combat.isPlayerControlActive()) return;

                switch (code) {
                    case 'Space':
                        this.combat.playerShoot();
                        break;
                    case 'KeyC':
                        this.combat.playerAimShoot();
                        break;
                }
            }

            enablePlayerControl(isAutoEnable = false) {
                if (this.combat && this.combat.enablePlayerControl()) {
                    this.isPlayerControlActive = true;
                    if (isAutoEnable) {
                        this.log('🎮 玩家控制已自动启用', 'battle');
                    } else {
                        this.log('🎮 玩家控制已启用', 'battle');
                    }
                }
            }

            disablePlayerControl() {
                if (this.combat) {
                    this.combat.disablePlayerControl();
                    this.isPlayerControlActive = false;
                    this.log('🎮 玩家控制已禁用', 'battle');
                }
            }

            // updatePlayerControlButtons 方法已移除，因为不再有相关按钮

            updateStatus() {
                if (!this.combat) return;

                const status = this.combat.getBattleStatus();
                
                // 更新战斗计时器
                if (status.isActive && this.battleStartTime) {
                    const elapsed = (Date.now() - this.battleStartTime) / 1000;
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = Math.floor(elapsed % 60);
                    document.getElementById('battleTimer').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else if (!status.isActive) {
                    document.getElementById('battleTimer').textContent = '00:00';
                }
                
                // 更新队伍状态
                this.updateTeamStatus('bears', status.participants.bears);
                this.updateTeamStatus('hunters', status.participants.hunters);
                
                // 更新肉块信息
                document.getElementById('meatDropped').textContent = status.meatDropped;
                document.getElementById('meatAvailable').textContent = status.totalMeat;
                
                // 更新玩家控制状态
                this.updatePlayerControlStatus(status.playerControl);
            }

            updateTeamStatus(team, data) {
                const prefix = team === 'bears' ? 'bears' : 'hunters';
                
                // 更新存活数量
                document.getElementById(`${prefix}Alive`).textContent = `${data.alive}/${data.total}`;
                
                // 更新击杀数量
                const kills = team === 'bears' ? data.killed : data.killed;
                document.getElementById(`${prefix}Kills`).textContent = kills;
                
                // 更新血量条
                const healthPercent = data.total > 0 ? (data.alive / data.total) * 100 : 0;
                document.getElementById(`${prefix}HealthBar`).style.width = `${healthPercent}%`;
            }

            updatePlayerControlStatus(playerControl) {
                if (playerControl.isActive) {
                    document.getElementById('controlledHunter').textContent = playerControl.controlledHunter.id;
                    document.getElementById('hunterHealth').textContent = 
                        `${playerControl.controlledHunter.health}/${playerControl.controlledHunter.maxHealth}`;
                    
                    // 更新移动状态
                    const isMoving = playerControl.hunterStatus.manualMoveDirection.length() > 0;
                    const movementText = isMoving ? 
                        (playerControl.hunterStatus.isRunning ? '跑步中' : '行走中') : '静止';
                    document.getElementById('hunterMovement').textContent = movementText;
                } else {
                    document.getElementById('controlledHunter').textContent = '无';
                    document.getElementById('hunterHealth').textContent = '-/-';
                    document.getElementById('hunterMovement').textContent = '静止';
                }
            }

            showWinner(winner) {
                const announcement = document.getElementById('winnerAnnouncement');
                const winnerText = this.getWinnerText(winner);
                
                announcement.textContent = `🏆 ${winnerText} 获胜！`;
                announcement.style.display = 'block';
                
                // 根据获胜者改变颜色
                if (winner === 'bears') {
                    announcement.style.background = 'linear-gradient(45deg, #d32f2f, #f44336)';
                } else if (winner === 'hunters') {
                    announcement.style.background = 'linear-gradient(45deg, #1976d2, #2196f3)';
                } else {
                    announcement.style.background = 'linear-gradient(45deg, #616161, #757575)';
                }
            }

            getWinnerText(winner) {
                switch (winner) {
                    case 'bears': return '熊队';
                    case 'hunters': return '猎人队';
                    case 'draw': return '平局';
                    default: return '未知';
                }
            }

            log(message, type = '') {
                const logContent = document.getElementById('logContent');
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                entry.innerHTML = `[${timestamp}] ${message}`;
                
                logContent.appendChild(entry);
                logContent.scrollTop = logContent.scrollHeight;
                
                // 限制日志条目数量
                while (logContent.children.length > 100) {
                    logContent.removeChild(logContent.firstChild);
                }
            }

            clearLog() {
                document.getElementById('logContent').innerHTML = '';
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                
                // 更新战斗系统
                if (this.combat) {
                    this.combat.update();
                }
                
                // 固定镜头位置 - 不旋转
                this.renderer.render(this.scene, this.camera);
            }
        }

        // 启动应用
        new CombatApp();
    </script>
</body>
</html> 