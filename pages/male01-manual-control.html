<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Male01 Manual Control Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 300px;
            max-width: 350px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 15px;
            padding: 20px;
            color: white;
            min-width: 300px;
            max-width: 350px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #joystick-container {
            position: absolute;
            bottom: 40px;
            left: 50px;
            width: 150px;
            height: 150px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            border: 2px solid rgba(100, 181, 246, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
        }

        #joystick {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #64b5f6, #42a5f5);
            border-radius: 50%;
            position: relative;
            cursor: grab;
            box-shadow: 0 4px 16px rgba(100, 181, 246, 0.4);
            transition: transform 0.1s ease;
        }

        #joystick:active {
            cursor: grabbing;
            transform: scale(1.1);
        }

        #action-buttons {
            position: absolute;
            bottom: 40px;
            right: 50px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .action-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 24px;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
            user-select: none;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .shoot-btn {
            background: linear-gradient(45deg, #f44336, #ef5350);
        }

        .run-btn {
            background: linear-gradient(45deg, #ff9800, #ffb74d);
        }

        .run-btn.active {
            background: linear-gradient(45deg, #4caf50, #66bb6a);
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
        }

        h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h4 {
            margin: 0 0 12px 0;
            color: #64b5f6;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .button-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        button {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 100px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .primary-btn {
            background: linear-gradient(45deg, #2196f3, #42a5f5);
            color: white;
        }

        .success-btn {
            background: linear-gradient(45deg, #4caf50, #66bb6a);
            color: white;
        }

        .warning-btn {
            background: linear-gradient(45deg, #ff9800, #ffb74d);
            color: white;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            color: #b0bec5;
            font-weight: 500;
        }

        .status-value {
            color: #fff;
            font-weight: 600;
        }

        .keyboard-hints {
            background: rgba(255, 255, 255, 0.05);
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 12px;
            line-height: 1.4;
        }

        .key {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            margin: 0 2px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            background: rgba(0, 0, 0, 0.95);
            border-radius: 15px;
            padding: 40px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.8);
        }

        .spinner {
            border: 4px solid #444;
            border-top: 4px solid #2196f3;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .position-display {
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 11px;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading Male01 Manual Control System...</div>
            <div style="font-size: 12px; margin-top: 10px; color: #bdc3c7;">
                Initializing model, controls, and input systems...
            </div>
        </div>

        <div id="controls">
            <h3>🎮 Male01 手动控制</h3>
            
            <div class="control-group">
                <h4>控制模式</h4>
                <div class="button-row">
                    <button id="enableManualBtn" class="success-btn">启用手动控制</button>
                    <button id="disableManualBtn" class="warning-btn" disabled>禁用手动控制</button>
                </div>
                <div class="button-row">
                    <button id="startPatrolBtn" class="primary-btn" disabled>开始巡逻</button>
                    <button id="stopPatrolBtn" class="warning-btn" disabled>停止巡逻</button>
                </div>
            </div>

            <div class="control-group">
                <h4>动作控制</h4>
                <div class="button-row">
                    <button id="shootBtn" class="warning-btn" disabled>射击</button>
                    <button id="aimShootBtn" class="warning-btn" disabled>瞄准射击</button>
                </div>
                <div class="button-row">
                    <button id="reviveBtn" class="success-btn">复活</button>
                </div>
            </div>

            <div class="control-group">
                <h4>键盘控制</h4>
                <div class="keyboard-hints">
                    <div><span class="key">W/S</span> 前进/后退</div>
                    <div><span class="key">A/D</span> 左移/右移</div>
                    <div><span class="key">Shift</span> 跑步</div>
                    <div><span class="key">Space</span> 射击</div>
                    <div><span class="key">C</span> 瞄准射击</div>
                    <div style="margin-top: 8px; color: #64b5f6;">
                        💡 启用手动控制后可使用键盘操作
                    </div>
                </div>
            </div>
        </div>

        <div id="status">
            <h3>📊 状态信息</h3>
            
            <div class="control-group">
                <h4>基本状态</h4>
                <div class="status-item">
                    <span class="status-label">健康值:</span>
                    <span class="status-value" id="healthStatus">100/100</span>
                </div>
                <div class="status-item">
                    <span class="status-label">存活状态:</span>
                    <span class="status-value" id="aliveStatus">存活</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前状态:</span>
                    <span class="status-value" id="currentState">IDLE</span>
                </div>
                <div class="status-item">
                    <span class="status-label">控制模式:</span>
                    <span class="status-value" id="controlMode">自动模式</span>
                </div>
            </div>

            <div class="control-group">
                <h4>位置信息</h4>
                <div class="position-display" id="positionDisplay">
                    X: 0.00, Y: 0.00, Z: 0.00
                </div>
                <div class="status-item">
                    <span class="status-label">移动速度:</span>
                    <span class="status-value" id="currentSpeed">0.0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">是否跑步:</span>
                    <span class="status-value" id="runningStatus">否</span>
                </div>
            </div>

            <div class="control-group">
                <h4>手动控制状态</h4>
                <div class="status-item">
                    <span class="status-label">移动方向:</span>
                    <span class="status-value" id="moveDirection">静止</span>
                </div>
                <div class="position-display" id="directionDisplay">
                    Dir X: 0.00, Z: 0.00
                </div>
            </div>

            <div class="control-group">
                <h4>射击系统</h4>
                <div class="status-item">
                    <span class="status-label">武器可见:</span>
                    <span class="status-value" id="weaponVisible">否</span>
                </div>
                <div class="status-item">
                    <span class="status-label">瞄准状态:</span>
                    <span class="status-value" id="targetingStatus">否</span>
                </div>
                <div class="status-item">
                    <span class="status-label">目标数量:</span>
                    <span class="status-value" id="targetCount">0</span>
                </div>
            </div>
        </div>

        <!-- 虚拟遥感 -->
        <div id="joystick-container">
            <div id="joystick"></div>
        </div>

        <!-- 动作按钮 -->
        <div id="action-buttons">
            <button class="action-btn run-btn" id="runToggleBtn" disabled>
                🏃
            </button>
            <button class="action-btn shoot-btn" id="mobileShootBtn" disabled>
                🔫
            </button>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { Male01Model } from '../src/entities/Male01Model.js';

        class Male01ControlApp {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.male01 = null;
                
                // 控制状态
                this.keys = {};
                this.isRunToggled = false;
                
                // 遥感状态
                this.joystick = {
                    isDragging: false,
                    startX: 0,
                    startY: 0,
                    currentX: 0,
                    currentY: 0,
                    maxDistance: 45,
                    element: null,
                    container: null
                };
                
                this.init();
            }

            async init() {
                // 设置渲染器
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x87ceeb, 1); // 天空蓝背景
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.getElementById('container').appendChild(this.renderer.domElement);

                // 设置相机位置
                this.camera.position.set(0, 8, 8);
                this.camera.lookAt(0, 0, 0);

                // 添加光照
                this.setupLights();

                // 添加地面
                this.addGround();

                // 创建Male01模型
                this.male01 = new Male01Model(this.scene, {
                    name: 'ControllableWorker',
                    position: new THREE.Vector3(0, 0, 0),
                    scale: 0.025
                });

                // 设置事件回调
                this.male01.setEventCallbacks({
                    onLoadComplete: () => {
                        console.log('Male01 loaded successfully');
                        this.updateStatus();
                    },
                    onAnimationChange: (newState) => {
                        console.log('Animation changed to:', newState);
                        this.updateStatus();
                    },
                    onShootTarget: () => {
                        console.log('Male01 is shooting!');
                    }
                });

                // 初始化模型
                await this.male01.initialize();

                // 设置控制事件
                this.setupControls();
                this.setupJoystick();

                // 开始渲染循环
                this.animate();

                // 开始状态更新
                this.startStatusUpdates();

                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            }

            setupLights() {
                // 环境光
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);

                // 主方向光
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 20, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);
            }

            addGround() {
                const groundGeometry = new THREE.PlaneGeometry(30, 30);
                const groundMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0x90EE90,
                    transparent: true,
                    opacity: 0.8
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -0.5;
                ground.receiveShadow = true;
                this.scene.add(ground);

                // 添加网格
                const gridHelper = new THREE.GridHelper(30, 30, 0x000000, 0x000000);
                gridHelper.position.y = -0.49;
                this.scene.add(gridHelper);
            }

            setupControls() {
                // UI按钮事件
                document.getElementById('enableManualBtn').addEventListener('click', () => {
                    this.enableManualControl();
                });

                document.getElementById('disableManualBtn').addEventListener('click', () => {
                    this.disableManualControl();
                });

                document.getElementById('startPatrolBtn').addEventListener('click', () => {
                    this.startPatrol();
                });

                document.getElementById('stopPatrolBtn').addEventListener('click', () => {
                    this.stopPatrol();
                });

                document.getElementById('shootBtn').addEventListener('click', () => {
                    if (this.male01) this.male01.manualShoot();
                });

                document.getElementById('aimShootBtn').addEventListener('click', () => {
                    if (this.male01) this.male01.manualAimShoot();
                });

                document.getElementById('reviveBtn').addEventListener('click', () => {
                    if (this.male01) this.male01.revive();
                });

                // 移动端按钮
                document.getElementById('runToggleBtn').addEventListener('click', () => {
                    this.toggleRun();
                });

                document.getElementById('mobileShootBtn').addEventListener('click', () => {
                    if (this.male01) this.male01.manualShoot();
                });

                // 键盘事件
                document.addEventListener('keydown', (e) => this.onKeyDown(e));
                document.addEventListener('keyup', (e) => this.onKeyUp(e));

                // 窗口调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            setupJoystick() {
                this.joystick.container = document.getElementById('joystick-container');
                this.joystick.element = document.getElementById('joystick');

                // 鼠标/触摸事件
                this.joystick.element.addEventListener('mousedown', (e) => this.onJoystickStart(e));
                this.joystick.element.addEventListener('touchstart', (e) => this.onJoystickStart(e.touches[0]));

                document.addEventListener('mousemove', (e) => this.onJoystickMove(e));
                document.addEventListener('touchmove', (e) => {
                    if (this.joystick.isDragging) {
                        e.preventDefault();
                        this.onJoystickMove(e.touches[0]);
                    }
                });

                document.addEventListener('mouseup', () => this.onJoystickEnd());
                document.addEventListener('touchend', () => this.onJoystickEnd());
            }

            onJoystickStart(e) {
                this.joystick.isDragging = true;
                const rect = this.joystick.container.getBoundingClientRect();
                this.joystick.startX = rect.left + rect.width / 2;
                this.joystick.startY = rect.top + rect.height / 2;
                this.joystick.element.style.transition = 'none';
            }

            onJoystickMove(e) {
                if (!this.joystick.isDragging) return;

                const deltaX = e.clientX - this.joystick.startX;
                const deltaY = e.clientY - this.joystick.startY;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                if (distance <= this.joystick.maxDistance) {
                    this.joystick.currentX = deltaX;
                    this.joystick.currentY = deltaY;
                } else {
                    const angle = Math.atan2(deltaY, deltaX);
                    this.joystick.currentX = Math.cos(angle) * this.joystick.maxDistance;
                    this.joystick.currentY = Math.sin(angle) * this.joystick.maxDistance;
                }

                // 更新遥感位置
                this.joystick.element.style.transform = 
                    `translate(${this.joystick.currentX}px, ${this.joystick.currentY}px)`;

                // 更新Male01移动
                this.updateJoystickMovement();
            }

            onJoystickEnd() {
                this.joystick.isDragging = false;
                this.joystick.currentX = 0;
                this.joystick.currentY = 0;
                this.joystick.element.style.transition = 'transform 0.2s ease';
                this.joystick.element.style.transform = 'translate(0px, 0px)';

                // 停止移动
                if (this.male01 && this.male01.isInManualControl()) {
                    this.male01.manualMoveJoystick(0, 0, this.isRunToggled);
                }
            }

            updateJoystickMovement() {
                if (!this.male01 || !this.male01.isInManualControl()) return;

                // 转换遥感坐标为移动输入
                const normalizedX = this.joystick.currentX / this.joystick.maxDistance;
                const normalizedY = -this.joystick.currentY / this.joystick.maxDistance; // 反转Y轴

                this.male01.manualMoveJoystick(normalizedX, normalizedY, this.isRunToggled);
            }

            onKeyDown(e) {
                this.keys[e.code] = true;
                
                // 防止默认行为
                if (['Space', 'ShiftLeft', 'ShiftRight'].includes(e.code)) {
                    e.preventDefault();
                }

                this.updateKeyboardMovement();
                this.handleActionKeys(e.code);
            }

            onKeyUp(e) {
                this.keys[e.code] = false;
                this.updateKeyboardMovement();
            }

            updateKeyboardMovement() {
                if (!this.male01 || !this.male01.isInManualControl()) return;

                const forward = this.keys['KeyW'] ? 1 : 0;
                const backward = this.keys['KeyS'] ? 1 : 0;
                const left = this.keys['KeyA'] ? 1 : 0;
                const right = this.keys['KeyD'] ? 1 : 0;
                const isRunning = this.keys['ShiftLeft'] || this.keys['ShiftRight'];

                this.male01.manualMoveKeyboard(forward, backward, left, right, isRunning);
            }

            handleActionKeys(code) {
                if (!this.male01 || !this.male01.isInManualControl()) return;

                switch (code) {
                    case 'Space':
                        this.male01.manualShoot();
                        break;
                    case 'KeyC':
                        this.male01.manualAimShoot();
                        break;
                }
            }

            enableManualControl() {
                if (this.male01) {
                    this.male01.enableManualControl();
                    this.updateControlButtons(true);
                }
            }

            disableManualControl() {
                if (this.male01) {
                    this.male01.disableManualControl();
                    this.updateControlButtons(false);
                }
            }

            startPatrol() {
                if (this.male01) {
                    this.male01.startPatrol();
                    this.updatePatrolButtons(true);
                }
            }

            stopPatrol() {
                if (this.male01) {
                    this.male01.stopPatrol();
                    this.updatePatrolButtons(false);
                }
            }

            toggleRun() {
                this.isRunToggled = !this.isRunToggled;
                const btn = document.getElementById('runToggleBtn');
                
                if (this.isRunToggled) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            }

            updateControlButtons(manualEnabled) {
                document.getElementById('enableManualBtn').disabled = manualEnabled;
                document.getElementById('disableManualBtn').disabled = !manualEnabled;
                document.getElementById('startPatrolBtn').disabled = manualEnabled;
                document.getElementById('stopPatrolBtn').disabled = manualEnabled;
                document.getElementById('shootBtn').disabled = !manualEnabled;
                document.getElementById('aimShootBtn').disabled = !manualEnabled;
                document.getElementById('runToggleBtn').disabled = !manualEnabled;
                document.getElementById('mobileShootBtn').disabled = !manualEnabled;
            }

            updatePatrolButtons(patrolling) {
                document.getElementById('startPatrolBtn').disabled = patrolling;
                document.getElementById('stopPatrolBtn').disabled = !patrolling;
            }

            startStatusUpdates() {
                setInterval(() => {
                    this.updateStatus();
                }, 100);
            }

            updateStatus() {
                if (!this.male01) return;

                const status = this.male01.getStatus();
                
                // 基本状态
                document.getElementById('healthStatus').textContent = `${status.health}/${status.maxHealth}`;
                document.getElementById('aliveStatus').textContent = status.isAlive ? '存活' : '死亡';
                document.getElementById('currentState').textContent = status.currentState;
                document.getElementById('controlMode').textContent = status.isManualControl ? '手动控制' : '自动模式';

                // 位置信息
                if (status.position) {
                    document.getElementById('positionDisplay').textContent = 
                        `X: ${status.position.x.toFixed(2)}, Y: ${status.position.y.toFixed(2)}, Z: ${status.position.z.toFixed(2)}`;
                }

                // 移动状态
                const speed = status.isRunning ? status.runSpeed : status.walkSpeed;
                const currentSpeed = status.manualMoveDirection.length() > 0 ? speed : 0;
                document.getElementById('currentSpeed').textContent = currentSpeed.toFixed(1);
                document.getElementById('runningStatus').textContent = status.isRunning ? '是' : '否';

                // 手动控制状态
                if (status.manualMoveDirection && status.manualMoveDirection.length() > 0) {
                    document.getElementById('moveDirection').textContent = '移动中';
                    document.getElementById('directionDisplay').textContent = 
                        `Dir X: ${status.manualMoveDirection.x.toFixed(2)}, Z: ${status.manualMoveDirection.z.toFixed(2)}`;
                } else {
                    document.getElementById('moveDirection').textContent = '静止';
                    document.getElementById('directionDisplay').textContent = 'Dir X: 0.00, Z: 0.00';
                }

                // 射击系统
                document.getElementById('weaponVisible').textContent = status.weaponVisible ? '是' : '否';
                document.getElementById('targetingStatus').textContent = status.isTargeting ? '是' : '否';
                document.getElementById('targetCount').textContent = status.targetCount.toString();
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                
                // 更新Male01
                if (this.male01) {
                    this.male01.update();
                }
                
                // 简单的相机跟随
                if (this.male01 && this.male01.getModel()) {
                    const position = this.male01.getModel().position;
                    this.camera.position.x = position.x + 8;
                    this.camera.position.z = position.z + 8;
                    this.camera.lookAt(position.x, position.y, position.z);
                }
                
                this.renderer.render(this.scene, this.camera);
            }
        }

        // 启动应用
        new Male01ControlApp();
    </script>
</body>
</html> 