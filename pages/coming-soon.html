<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>模块开发中 - Three.js 项目</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Arial', 'Helvetica Neue', sans-serif;
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
        min-height: 100vh;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      .container {
        text-align: center;
        max-width: 600px;
        padding: 40px 20px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
      }

      .container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4ecdc4, #44a08d);
      }

      .icon {
        font-size: 5rem;
        margin-bottom: 30px;
        animation: bounce 2s infinite;
      }

      @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }

      h1 {
        font-size: 2.5rem;
        font-weight: 300;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #4ecdc4, #44a08d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .description {
        font-size: 1.2rem;
        color: #ccc;
        margin-bottom: 30px;
        line-height: 1.6;
      }

      .features {
        list-style: none;
        margin-bottom: 40px;
        text-align: left;
        display: inline-block;
      }

      .features li {
        color: #aaa;
        margin-bottom: 10px;
        padding-left: 25px;
        position: relative;
      }

      .features li::before {
        content: '⚡';
        position: absolute;
        left: 0;
        color: #4ecdc4;
      }

      .progress {
        margin-bottom: 30px;
      }

      .progress-label {
        font-size: 0.9rem;
        color: #888;
        margin-bottom: 10px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4ecdc4, #44a08d);
        border-radius: 4px;
        width: 25%;
        animation: progress 3s ease-in-out infinite;
      }

      @keyframes progress {
        0% { width: 15%; }
        50% { width: 35%; }
        100% { width: 25%; }
      }

      .back-button {
        display: inline-block;
        padding: 15px 30px;
        background: linear-gradient(45deg, #4ecdc4, #44a08d);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: 1px solid rgba(78, 205, 196, 0.3);
      }

      .back-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px rgba(78, 205, 196, 0.3);
      }

      .particles {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        top: 0;
        left: 0;
        z-index: -1;
      }

      .particle {
        position: absolute;
        background: rgba(78, 205, 196, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
      }

      .particle:nth-child(1) {
        width: 80px;
        height: 80px;
        left: 10%;
        animation-delay: 0s;
      }

      .particle:nth-child(2) {
        width: 60px;
        height: 60px;
        left: 80%;
        animation-delay: 2s;
      }

      .particle:nth-child(3) {
        width: 100px;
        height: 100px;
        left: 60%;
        animation-delay: 4s;
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(100vh) rotate(0deg);
          opacity: 0;
        }
        10%, 90% {
          opacity: 1;
        }
        50% {
          transform: translateY(-100px) rotate(180deg);
        }
      }

      @media (max-width: 768px) {
        .container {
          margin: 20px;
          padding: 30px 20px;
        }
        
        h1 {
          font-size: 2rem;
        }
        
        .description {
          font-size: 1rem;
        }
        
        .icon {
          font-size: 4rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="particles">
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
    </div>

    <div class="container">
      <div class="icon">🚧</div>
      <h1>模块开发中</h1>
      <p class="description">
        该模块正在紧张开发中，将为您带来令人惊叹的 3D 体验功能。
      </p>
      
      <ul class="features">
        <li>模块化架构设计</li>
        <li>高性能渲染优化</li>
        <li>完整的 TypeScript 支持</li>
        <li>丰富的调试工具</li>
      </ul>

      <div class="progress">
        <div class="progress-label">开发进度</div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>

      <a href="/" class="back-button">← 返回主页</a>
    </div>

    <script>
      // 添加一些交互效果
      document.addEventListener('DOMContentLoaded', () => {
        const container = document.querySelector('.container');
        
        // 鼠标移动视差效果
        document.addEventListener('mousemove', (e) => {
          const x = (e.clientX / window.innerWidth - 0.5) * 20;
          const y = (e.clientY / window.innerHeight - 0.5) * 20;
          
          container.style.transform = `translate(${x}px, ${y}px)`;
        });

        // 点击效果
        container.addEventListener('click', (e) => {
          const ripple = document.createElement('div');
          ripple.style.position = 'absolute';
          ripple.style.borderRadius = '50%';
          ripple.style.background = 'rgba(78, 205, 196, 0.3)';
          ripple.style.transform = 'scale(0)';
          ripple.style.animation = 'ripple 0.6s linear';
          ripple.style.left = (e.clientX - container.offsetLeft) + 'px';
          ripple.style.top = (e.clientY - container.offsetTop) + 'px';
          ripple.style.width = ripple.style.height = '20px';
          ripple.style.marginLeft = ripple.style.marginTop = '-10px';
          
          container.appendChild(ripple);
          
          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // 添加 ripple 动画样式
      const style = document.createElement('style');
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    </script>
  </body>
</html> 