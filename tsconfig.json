{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"three/webgpu": ["./node_modules/three/build/three.webgpu.js"], "three/tsl": ["./node_modules/three/build/three.tsl.js"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}