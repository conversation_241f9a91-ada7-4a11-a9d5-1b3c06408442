<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Three.js 大型项目 - 模块导航</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Arial', 'Helvetica Neue', sans-serif;
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
        min-height: 100vh;
        color: white;
        overflow-x: hidden;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .header {
        text-align: center;
        margin-bottom: 60px;
        padding: 40px 0;
      }

      .header h1 {
        font-size: 3.5rem;
        font-weight: 300;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #4ecdc4, #44a08d, #093637);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(78, 205, 196, 0.3);
      }

      .header p {
        font-size: 1.2rem;
        color: #888;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .modules-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        flex: 1;
        align-content: start;
      }

      .module-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 30px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
      }

      .module-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4ecdc4, #44a08d);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .module-card:hover {
        transform: translateY(-5px);
        border-color: rgba(78, 205, 196, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      .module-card:hover::before {
        opacity: 1;
      }

      .module-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        display: block;
      }

      .module-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: #4ecdc4;
      }

      .module-card p {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 25px;
      }

      .module-features {
        list-style: none;
        margin-bottom: 25px;
      }

      .module-features li {
        color: #aaa;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
      }

      .module-features li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #4ecdc4;
        font-weight: bold;
      }

      .module-status {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        margin-bottom: 20px;
      }

      .status-completed {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
        border: 1px solid rgba(76, 175, 80, 0.3);
      }

      .status-development {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
        border: 1px solid rgba(255, 152, 0, 0.3);
      }

      .status-planned {
        background: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
        border: 1px solid rgba(158, 158, 158, 0.3);
      }

      .module-button {
        display: inline-block;
        padding: 12px 30px;
        background: linear-gradient(45deg, #4ecdc4, #44a08d);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
      }

      .module-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(78, 205, 196, 0.3);
      }

      .module-button:disabled {
        background: #666;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .footer {
        margin-top: 60px;
        text-align: center;
        padding: 30px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        color: #666;
      }

      .tech-stack {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
      }

      .tech-item {
        background: rgba(255, 255, 255, 0.05);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 2.5rem;
        }
        
        .modules-grid {
          grid-template-columns: 1fr;
        }
        
        .container {
          padding: 15px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1>Three.js 大型项目</h1>
        <p>基于 TypeScript 的模块化 3D 图形开发平台。每个模块独立开发，可单独使用或组合构建复杂应用。</p>
      </header>

      <main class="modules-grid">
        <!-- 熊模型动画控制模块 -->
        <div class="module-card">
          <span class="module-icon">🐻</span>
          <span class="module-status status-completed">已完成</span>
          <h3>熊模型动画控制模块</h3>
          <p>熊模型动画控制系统，包含智能巡逻、攻击行为和调试面板。</p>
          <ul class="module-features">
            <li>熊模型AI行为</li>
            <li>智能巡逻系统</li>
            <li>动画状态控制</li>
            <li>熊主题调试面板</li>
          </ul>
          <a href="/pages/bear-animation.html" class="module-button">进入模块</a>
        </div>

        <!-- Male01 工人模型模块 -->
        <div class="module-card">
          <span class="module-icon">👷</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Male01 工人模型模块</h3>
          <p>功能丰富的工人角色模型，包含多种动作和行为状态。</p>
          <ul class="module-features">
            <li>多样化动作系统</li>
            <li>战斗和生活动作</li>
            <li>智能移动控制</li>
            <li>实时状态监控</li>
          </ul>
          <a href="/pages/male01-worker.html" class="module-button">进入模块</a>
        </div>

        <!-- Male01手动控制模块 -->
        <div class="module-card">
          <span class="module-icon">🎮</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Male01手动控制模块</h3>
          <p>Male01工人的手动控制系统，支持键盘和虚拟遥感操作，提供完整的交互体验。</p>
          <ul class="module-features">
            <li>键盘WASD控制</li>
            <li>虚拟遥感操作</li>
            <li>手动射击系统</li>
            <li>实时状态监控</li>
          </ul>
          <a href="/pages/male01-manual-control.html" class="module-button">进入模块</a>
        </div>

        <!-- 肉块模型模块 -->
        <div class="module-card">
          <span class="module-icon">🥩</span>
          <span class="module-status status-completed">已完成</span>
          <h3>肉块模型模块</h3>
          <p>静态肉块模型展示系统，包含精细的纹理应用和变换控制。</p>
          <ul class="module-features">
            <li>静态模型展示</li>
            <li>纹理系统应用</li>
            <li>变换参数控制</li>
            <li>实时状态监控</li>
          </ul>
          <a href="/pages/meat-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 草地模块 -->
        <div class="module-card">
          <span class="module-icon">🌱</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Grass 草地系统</h3>
          <p>基于SS_Grass_01模型的静态草地展示系统，支持纹理应用和材质调节。</p>
          <ul class="module-features">
            <li>静态草地模型展示</li>
            <li>纹理系统应用</li>
            <li>材质属性调节</li>
            <li>变换参数控制</li>
            <li>实时状态监控</li>
          </ul>
          <a href="/pages/grass-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 桌子模块 -->
        <div class="module-card">
          <span class="module-icon">🪑</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Table 桌子系统</h3>
          <p>基于SSD_Table_01模型的静态桌子展示系统，支持PBR材质和纹理应用。</p>
          <ul class="module-features">
            <li>静态桌子模型展示</li>
            <li>PBR材质系统</li>
            <li>材质属性调节</li>
            <li>变换参数控制</li>
            <li>实时状态监控</li>
          </ul>
          <a href="/pages/table-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 岩石模块 -->
        <div class="module-card">
          <span class="module-icon">🪨</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Rocks 岩石系统</h3>
          <p>基于SS_Rock_01模型的静态岩石展示系统，包含8个岩石网格，支持PBR材质调节。</p>
          <ul class="module-features">
            <li>多网格FBX加载</li>
            <li>PBR材质系统</li>
            <li>实例化渲染支持</li>
            <li>材质属性实时调节</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/rocks-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 地面模块 -->
        <div class="module-card">
          <span class="module-icon">🌍</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Ground 地面系统</h3>
          <p>基于SS_Ground_01模型的地面展示系统，包含9个地面网格，支持PBR材质调节和多网格控制。</p>
          <ul class="module-features">
            <li>多网格FBX加载</li>
            <li>PBR材质系统</li>
            <li>地面材质优化</li>
            <li>材质属性实时调节</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/ground-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 主地面模块 -->
        <div class="module-card">
          <span class="module-icon">🏔️</span>
          <span class="module-status status-completed">已完成</span>
          <h3>GroundMain 主地面系统</h3>
          <p>基于SS_Ground_01模型的简化主地面展示系统，专注于单一主地面网格，应用M_BaseGroundColor_05材质和SS_Ground_01_3_D贴图。</p>
          <ul class="module-features">
            <li>单网格FBX加载</li>
            <li>M_BaseGroundColor_05材质</li>
            <li>SS_Ground_01_3_D贴图</li>
            <li>简化控制界面</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/ground-main-model.html" class="module-button">进入模块</a>
        </div>

        <!-- Ground01模块 -->
        <div class="module-card">
          <span class="module-icon">🌍</span>
          <h3>Ground01 模块</h3>
          <p>专门处理Plane.018网格的地面展示系统，基于Ground_01目录资源</p>
          <ul>
            <li>专门针对Plane.018网格</li>
            <li>M_BaseGroundColor_04材质</li>
            <li>SS_Ground_01_2_D贴图</li>
            <li>完整PBR材质系统</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/ground01-model.html" class="module-button">进入模块</a>
        </div>

        <!-- Ground02模块 -->
        <div class="module-card">
          <span class="module-icon">🗺️</span>
          <h3>Ground02 模块</h3>
          <p>专门处理Plane.018网格的地面展示系统，基于Ground_02目录资源</p>
          <ul>
            <li>专门针对Plane.018网格</li>
            <li>M_BaseGroundColor_02材质</li>
            <li>SS_Ground_02_D贴图</li>
            <li>完整PBR材质系统</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/ground02-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 头盔模块 -->
        <div class="module-card">
          <span class="module-icon">🪖</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Helmet 头盔系统</h3>
          <p>基于SS_Helmet_01模型的静态头盔展示系统，支持PBR材质和法线贴图应用。</p>
          <ul class="module-features">
            <li>静态头盔模型展示</li>
            <li>PBR材质系统</li>
            <li>法线贴图支持</li>
            <li>材质属性调节</li>
            <li>变换参数控制</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/helmet-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 八字模块 -->
        <div class="module-card">
          <span class="module-icon">🍽️</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Bazi 八字系统</h3>
          <p>基于SSD_Bazi_01模型的静态八字展示系统，支持PBR材质和纹理应用。</p>
          <ul class="module-features">
            <li>静态八字模型展示</li>
            <li>PBR材质系统</li>
            <li>纹理贴图支持</li>
            <li>材质属性调节</li>
            <li>变换参数控制</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/bazi-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 矿石模块 -->
        <div class="module-card">
          <span class="module-icon">🔶</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Ore 矿石系统</h3>
          <p>基于SS_Ore_01模型的静态矿石展示系统，支持金黄色PBR材质和纹理重复应用。</p>
          <ul class="module-features">
            <li>静态矿石模型展示</li>
            <li>金黄色PBR材质</li>
            <li>纹理重复贴图(3x3)</li>
            <li>材质属性调节</li>
            <li>变换参数控制</li>
            <li>Cocos光照配置</li>
          </ul>
          <a href="/pages/ore-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 肉块堆叠模块 -->
        <div class="module-card">
          <span class="module-icon">🏗️</span>
          <span class="module-status status-completed">已完成</span>
          <h3>肉块完美堆叠模块</h3>
          <p>实现肉块完美对齐的紧密堆叠效果，每块肉完全一致且紧贴叠放。</p>
          <ul class="module-features">
            <li>完美对齐堆叠</li>
            <li>统一旋转角度</li>
            <li>紧密贴合效果</li>
            <li>精确控制系统</li>
          </ul>
          <a href="/pages/meat-stacking.html" class="module-button">进入模块</a>
        </div>

        <!-- 树模型模块 -->
        <div class="module-card">
          <span class="module-icon">🌳</span>
          <span class="module-status status-completed">已完成</span>
          <h3>树模型展示模块</h3>
          <p>基于Tree_01模型的交互式树展示系统，包含多种摆动效果和实时参数调节。</p>
          <ul class="module-features">
            <li>静态展示模式</li>
            <li>轻微摆动效果</li>
            <li>强风摆动模拟</li>
            <li>实时参数调节</li>
            <li>材质调试工具</li>
            <li>阴影和光照效果</li>
          </ul>
          <a href="/pages/tree-model.html" class="module-button">进入模块</a>
        </div>

        <!-- Tree02模型展示模块 -->
        <div class="module-card">
          <span class="module-icon">🌳</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Tree02模型展示</h3>
          <p>基于Cocos Creator材质的Tree02模型展示系统，支持多种动画状态和实时参数调节。</p>
          <ul class="module-features">
            <li>Cocos Creator材质支持</li>
            <li>三种动画状态切换</li>
            <li>实时参数调节面板</li>
            <li>材质调试工具</li>
            <li>优化的渲染效果</li>
          </ul>
          <a href="/pages/tree02-model.html" class="module-button">进入模块</a>
        </div>

        <!-- Tree03模型展示模块 -->
        <div class="module-card">
          <span class="module-icon">🌲</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Tree03模型展示</h3>
          <p>基于Tree03模型的展示系统，使用与Tree02相同的Cocos Creator材质，支持完整的动画状态控制。</p>
          <ul class="module-features">
            <li>Tree03模型支持</li>
            <li>共享Cocos Creator材质</li>
            <li>三种动画状态切换</li>
            <li>实时参数调节面板</li>
            <li>材质调试工具</li>
          </ul>
          <a href="/pages/tree03-model.html" class="module-button">进入模块</a>
        </div>

        <!-- Tree04模型展示模块 -->
        <div class="module-card">
          <span class="module-icon">🌳</span>
          <span class="module-status status-completed">已完成</span>
          <h3>Tree04模型展示</h3>
          <p>基于Tree_04模型的展示系统，使用Cocos Creator材质，支持完整的动画状态控制和色温调整。</p>
          <ul class="module-features">
            <li>Tree_04模型支持</li>
            <li>共享Cocos Creator材质</li>
            <li>三种动画状态切换</li>
            <li>色温调整功能(0-100范围)</li>
            <li>实时参数调节面板</li>
            <li>材质调试工具</li>
          </ul>
          <a href="/pages/tree04-model.html" class="module-button">进入模块</a>
        </div>

        <!-- 熊猎人战斗模块 -->
        <div class="module-card">
          <span class="module-icon">⚔️</span>
          <span class="module-status status-completed">已完成</span>
          <h3>熊猎人战斗系统</h3>
          <p>复杂的战斗交互系统，包含AI对战、伤害计算、死亡掉落等机制。</p>
          <ul class="module-features">
            <li>智能AI对战</li>
            <li>攻击射击系统</li>
            <li>死亡掉落机制</li>
            <li>实时战斗统计</li>
          </ul>
          <a href="/pages/bear-hunter-combat.html" class="module-button">进入模块</a>
        </div>

        <!-- 场景管理模块 -->
        <div class="module-card">
          <span class="module-icon">🌍</span>
          <span class="module-status status-planned">计划中</span>
          <h3>场景管理模块</h3>
          <p>大型场景的加载、优化和管理系统，支持LOD、遮挡剔除和动态加载。</p>
          <ul class="module-features">
            <li>场景分块加载</li>
            <li>LOD 层级细节</li>
            <li>遮挡剔除优化</li>
            <li>动态资源管理</li>
          </ul>
          <button class="module-button" disabled>开发中</button>
        </div>

        <!-- 粒子特效模块 -->
        <div class="module-card">
          <span class="module-icon">✨</span>
          <span class="module-status status-planned">计划中</span>
          <h3>粒子特效模块</h3>
          <p>高性能的粒子系统，支持各种视觉特效和环境效果。</p>
          <ul class="module-features">
            <li>GPU 粒子系统</li>
            <li>预设特效库</li>
            <li>实时编辑器</li>
            <li>性能优化</li>
          </ul>
          <button class="module-button" disabled>开发中</button>
        </div>

        <!-- 物理引擎模块 -->
        <div class="module-card">
          <span class="module-icon">⚡</span>
          <span class="module-status status-planned">计划中</span>
          <h3>物理引擎模块</h3>
          <p>集成物理引擎，提供碰撞检测、刚体模拟和约束系统。</p>
          <ul class="module-features">
            <li>刚体物理模拟</li>
            <li>碰撞检测系统</li>
            <li>约束和关节</li>
            <li>流体模拟</li>
          </ul>
          <button class="module-button" disabled>开发中</button>
        </div>

        <!-- UI系统模块 -->
        <div class="module-card">
          <span class="module-icon">🎮</span>
          <span class="module-status status-planned">计划中</span>
          <h3>UI 系统模块</h3>
          <p>3D 空间中的 UI 系统，支持 HUD、菜单和交互界面。</p>
          <ul class="module-features">
            <li>3D 空间 UI</li>
            <li>响应式布局</li>
            <li>动画过渡</li>
            <li>触摸和鼠标交互</li>
          </ul>
          <button class="module-button" disabled>开发中</button>
        </div>

        <!-- 音频系统模块 -->
        <div class="module-card">
          <span class="module-icon">🔊</span>
          <span class="module-status status-planned">计划中</span>
          <h3>音频系统模块</h3>
          <p>3D 位置音频系统，支持环境音效和动态音乐。</p>
          <ul class="module-features">
            <li>3D 位置音频</li>
            <li>环境音效</li>
            <li>动态音乐混合</li>
            <li>音频分析可视化</li>
          </ul>
          <button class="module-button" disabled>开发中</button>
        </div>
      </main>

      <footer class="footer">
        <p>基于现代 Web 技术构建的 3D 图形开发平台</p>
        <div class="tech-stack">
          <span class="tech-item">Three.js</span>
          <span class="tech-item">TypeScript</span>
          <span class="tech-item">Vite</span>
          <span class="tech-item">WebGL</span>
          <span class="tech-item">ES Modules</span>
        </div>
      </footer>
    </div>
  </body>
</html> 