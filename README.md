# Three.js TypeScript 项目

这是一个使用 TypeScript 和 Three.js 构建的现代 3D 图形应用程序。

## 特性

- ✨ **现代技术栈**: TypeScript + Three.js + Vite
- 🎯 **严格类型检查**: 完整的 TypeScript 支持
- 🚀 **热重载**: Vite 提供的快速开发体验
- 🎮 **交互式控制**: OrbitControls 支持鼠标控制视角
- 💡 **多种光源**: 环境光、方向光、点光源、聚光灯
- 🎨 **PBR 材质**: 物理渲染材质和阴影效果
- 📱 **响应式设计**: 自适应不同屏幕尺寸

## 项目结构

```
threejs/
├── src/
│   └── main.ts          # 主应用文件
├── index.html           # HTML 入口文件
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript 配置
├── tsconfig.node.json   # Node.js TypeScript 配置
├── vite.config.ts       # Vite 构建配置
└── README.md            # 项目说明
```

## 安装

确保您已安装 [Node.js](https://nodejs.org/) (版本 16 或更高)。

```bash
# 克隆项目后安装依赖
npm install
```

## 使用

### 开发模式

启动开发服务器，支持热重载：

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
```

构建结果将输出到 `dist` 目录。

### 预览生产版本

```bash
npm run preview
```

### 类型检查

仅运行 TypeScript 类型检查：

```bash
npm run type-check
```

## 功能说明

当前场景包含：

- **立方体** (青色) - 沿 X 和 Y 轴旋转
- **球体** (红色) - 沿 Y 轴旋转，高金属度材质
- **环面** (蓝色) - 沿 X 和 Y 轴旋转

### 交互控制

- **鼠标左键拖拽**: 旋转视角
- **鼠标滚轮**: 缩放视图
- **鼠标右键拖拽**: 平移视图

### 光照系统

- **环境光**: 提供基础照明
- **方向光**: 主要光源，产生阴影
- **点光源**: 红色光源，左后方
- **聚光灯**: 蓝色聚光，右前方

## 技术细节

### 依赖项

- **three**: Three.js 核心库
- **@types/three**: Three.js TypeScript 类型定义
- **typescript**: TypeScript 编译器
- **vite**: 构建工具和开发服务器

### TypeScript 配置

项目配置了严格的 TypeScript 检查，包括：

- 严格空值检查
- 未使用变量检查
- 未使用参数检查
- Switch 语句穿透检查

### Vite 配置

- 开发服务器端口: 3000
- 支持热模块替换 (HMR)
- 生成源码映射
- 自动处理 TypeScript 编译

## 扩展建议

您可以基于此项目继续开发：

1. **添加更多几何体**: 使用 Three.js 的其他几何体类型
2. **导入 3D 模型**: 使用 GLTFLoader 加载外部模型
3. **后处理效果**: 添加渲染后处理管道
4. **物理引擎**: 集成 Cannon.js 或 Ammo.js
5. **用户界面**: 添加 dat.GUI 或 lil-gui 控制面板
6. **动画系统**: 使用 Three.js 动画混合器
7. **音频**: 添加 3D 位置音频支持

## 许可证

MIT License 