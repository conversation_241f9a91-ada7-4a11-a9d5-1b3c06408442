# Three.js TypeScript 项目技术方案文档

## 1. 项目架构分析

### 1.1 整体架构模式
- **多页面模块化架构**: 采用独立页面模式，每个功能模块拥有独立的HTML页面和入口点
- **模块化设计**: 基于ES6模块系统，实现代码的高度模块化和可复用性
- **事件驱动架构**: 通过PageManager实现模块间通信，采用消息传递机制
- **分层架构**: 清晰的分层结构 - 实体层(entities)、控制器层(controllers)、加载器层(loaders)、工具层(utils)

### 1.2 技术栈组成
```json
{
  "核心框架": {
    "Three.js": "^0.178.0",
    "TypeScript": "^5.8.3"
  },
  "构建工具": {
    "Vite": "^7.0.6"
  },
  "UI工具": {
    "dat.gui": "^0.7.9"
  },
  "类型定义": {
    "@types/three": "^0.178.1",
    "@types/dat.gui": "^0.7.13",
    "@types/node": "^24.1.0"
  }
}
```

### 1.3 目录结构详细说明
```
threejs/
├── src/                          # 源代码根目录
│   ├── entities/                 # 实体类模块 - 核心业务对象
│   │   ├── BearModel.ts         # 熊模型实体 - 完整动画系统
│   │   ├── Male01Model.ts       # 工人模型实体 - 射击和移动系统
│   │   └── MeatModel.ts         # 肉块模型实体 - 静态展示系统
│   ├── controllers/             # 控制器模块 - 业务逻辑控制
│   │   └── AnimationController.ts # 动画状态机控制器
│   ├── loaders/                 # 加载器模块 - 资源加载管理
│   │   └── FBXLoader.ts         # FBX模型加载器
│   ├── utils/                   # 工具类模块 - 公共功能
│   │   ├── PageManager.ts       # 页面管理器 - 模块通信
│   │   └── BearDebugPanel.ts    # 调试面板工具
│   ├── examples/                # 示例系统 - 复杂功能演示
│   │   ├── bear-hunter-combat.ts # 战斗系统示例
│   │   └── meat-stacking.ts     # 堆叠系统示例
│   ├── models/                  # 3D模型资源
│   │   ├── Bear/                # 熊模型和动画文件
│   │   ├── Male_01/             # 工人模型和武器文件
│   │   └── Meat/                # 肉块模型文件
│   ├── styles/                  # 样式文件
│   │   └── common.css           # 公共样式定义
│   └── bear-main.ts             # 熊模块主入口
├── pages/                       # 模块页面目录
│   ├── boss-animation.html      # Boss动画控制模块页面
│   ├── bear-animation.html      # 熊动画控制模块页面
│   └── male01-worker.html       # 工人模块页面
├── index.html                   # 主导航页面
├── vite.config.ts              # 多页面构建配置
├── tsconfig.json               # TypeScript严格配置
└── package.json                # 项目依赖配置
```

### 1.4 构建配置分析
**vite.config.ts多页面配置**:
```typescript
rollupOptions: {
  input: {
    main: resolve('index.html'),
    'boss-animation': resolve('pages/boss-animation.html'),
    'bear-animation': resolve('pages/bear-animation.html'),
    // 支持动态添加新模块页面
  }
}
```

## 2. 功能模块清单

### 2.1 已完成模块

#### Boss动画控制模块 (生产就绪)
- **入口文件**: `/pages/boss-animation.html`
- **核心类**: `BossMonster` (推测，基于文档描述)
- **功能特性**:
  - FBX模型加载和动画播放
  - 状态机管理 (IDLE, ATTACK_1, ATTACK_2, DIE, GUI)
  - 实时调试面板控制
  - 生命值系统和伤害处理
- **依赖关系**: ModelLoader → AnimationController → BossDebugPanel

#### 熊动画控制模块 (生产就绪)
- **入口文件**: `/src/bear-main.ts`, `/pages/bear-animation.html`
- **核心类**: `BearModel`
- **功能特性**:
  - 完整的熊模型动画系统 (idle, walk, attack, death)
  - 智能巡逻和追击系统
  - 攻击目标检测和伤害计算
  - 纹理系统和材质管理
- **接口定义**:
```typescript
interface BearConfig {
  name: string;
  modelPath: string;
  animations: AnimationFile[];
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}
```

#### 工人模型模块 (生产就绪)
- **入口文件**: `/pages/male01-worker.html`
- **核心类**: `Male01Model`
- **功能特性**:
  - 工人角色动画控制
  - 武器系统集成 (火枪模型)
  - 射击系统和弹道计算
  - 手动/自动移动控制
- **武器配置**: 优化的武器位置和旋转参数

#### 肉块展示模块 (生产就绪)
- **入口文件**: 通过`MeatModel`类调用
- **核心类**: `MeatModel`
- **功能特性**:
  - 静态模型展示和纹理应用
  - 变换控制 (位置、旋转、缩放)
  - 材质调试和状态监控
  - 事件回调系统

#### 树模型展示模块 (生产就绪)
- **入口文件**: `/pages/tree-model.html`
- **核心类**: `TreeModel`
- **功能特性**:
  - Tree_01模型的静态展示和动态摆动
  - 三种状态管理 (静态、轻微摆动、强风摆动)
  - 实时摆动参数调节 (强度、速度、风力)
  - 变换控制和材质调试
  - HTML控制面板集成（无需额外调试面板文件）

#### Tree02模型展示模块 (生产就绪)
- **入口文件**: `/pages/tree02-model.html`
- **核心类**: `Tree02Model`
- **功能特性**:
  - Tree_02模型的静态展示和动态摆动
  - Cocos Creator材质文件解析和应用
  - 三种状态管理 (静态、轻微摆动、强风摆动)
  - 实时摆动参数调节和颜色调整
  - HTML控制面板集成（无需额外调试面板文件）
- **接口定义**:
```typescript
interface TreeConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

enum TreeState {
  STATIC = 'static',
  SWAYING = 'swaying',
  WINDY = 'windy'
}
```

#### Tree03模型展示模块 (生产就绪)
- **入口文件**: `/pages/tree03-model.html`
- **核心类**: `Tree03Model`
- **功能特性**:
  - Tree_03模型的静态展示和动态摆动
  - 使用与Tree02相同的Cocos Creator材质文件
  - 三种状态管理 (静态、轻微摆动、强风摆动)
  - 实时摆动参数调节和颜色调整
  - HTML控制面板集成（无需额外调试面板文件）
- **接口定义**:
```typescript
interface Tree03Config {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

enum Tree03State {
  STATIC = 'static',
  SWAYING = 'swaying',
  WINDY = 'windy'
}
```

### 2.2 开发中模块

#### 战斗系统示例 (开发中)
- **文件路径**: `/src/examples/bear-hunter-combat.ts`
- **功能描述**: 熊与猎人的战斗交互系统
- **当前状态**: 基础框架完成，需要完善战斗逻辑

#### 堆叠系统示例 (开发中)
- **文件路径**: `/src/examples/meat-stacking.ts`
- **功能描述**: 肉块自动堆叠演示系统
- **当前状态**: 基础实现完成，需要优化性能

### 2.3 计划中模块
- 场景管理模块 (LOD、遮挡剔除)
- 粒子特效模块 (GPU粒子系统)
- 物理引擎模块 (刚体碰撞)
- UI系统模块 (3D空间UI)
- 音频系统模块 (3D位置音频)

## 3. 核心组件分析

### 3.1 实体类设计模式

#### MeatModel类 - 静态模型展示
```typescript
export class MeatModel {
  private config: MeatConfig;
  private loader: ModelLoader;
  private model: THREE.Group;
  private isLoaded: boolean;
  
  // 工厂方法模式
  static create(scene: THREE.Scene, config?: Partial<MeatConfig>): MeatModel
  
  // 异步初始化模式
  async initialize(): Promise<void>
  
  // 状态查询接口
  getStatus(): ModelStatus
}
```

#### BearModel类 - 复杂动画实体
```typescript
export class BearModel {
  // 状态机模式
  private currentBearState: BearState;
  private animationController: AnimationController;
  
  // 行为系统
  private isWalking: boolean;
  private isChasing: boolean;
  private targets: THREE.Object3D[];
  
  // 生命系统
  private health: number;
  private maxHealth: number;
  private isAlive: boolean;
}
```

### 3.2 加载器系统实现机制

#### ModelLoader类 - 统一资源加载
```typescript
export class ModelLoader {
  private loader: FBXLoader;
  private loadingManager: THREE.LoadingManager;
  private loadedModels: Map<string, ModelLoadResult>;
  
  // 主模型加载
  async loadMainModel(path: string): Promise<ModelLoadResult>
  
  // 动画文件加载
  async loadAnimationFiles(files: AnimationFile[]): Promise<THREE.AnimationClip[]>
  
  // 模型预处理
  preprocessModel(model: THREE.Group): void
}
```

### 3.3 控制器和管理器职责划分

#### AnimationController - 动画状态机
- **职责**: 管理动画状态转换和播放控制
- **核心方法**: `playState()`, `addTransition()`, `update()`
- **设计模式**: 状态机模式 + 命令模式

#### PageManager - 页面通信管理器
- **职责**: 模块间消息传递和状态同步
- **核心功能**: 消息路由、状态通知、错误处理
- **设计模式**: 单例模式 + 观察者模式

### 3.4 工具类和公共组件复用策略

#### BearDebugPanel - 调试面板工具
- **复用策略**: 基于dat.gui的可扩展调试面板基类
- **参数化配置**: 支持动态添加控制参数
- **事件绑定**: 统一的事件处理机制

## 4. 开发规范和最佳实践

### 4.1 TypeScript类型定义规范
- **严格模式**: 启用所有严格类型检查选项
- **接口优先**: 使用interface定义数据结构
- **泛型约束**: 合理使用泛型提高代码复用性
- **类型守卫**: 实现运行时类型安全检查

### 4.2 文件命名和组织规范
- **PascalCase**: 类文件命名 (如`BearModel.ts`)
- **camelCase**: 实例和方法命名
- **模块分离**: 按功能职责分离到不同目录
- **依赖管理**: 明确的导入导出关系

### 4.3 错误处理和调试机制
- **统一错误处理**: PageManager提供全局错误处理
- **分层日志**: console.log分级输出
- **调试面板**: 每个模块独立的调试工具
- **状态监控**: 实时状态显示和性能监控

### 4.4 性能优化策略
- **按需加载**: 模型和纹理懒加载
- **资源复用**: 共享几何体和材质
- **动画优化**: 合理的动画更新频率
- **内存管理**: 及时清理不用的资源

## 5. 技术架构图

### 5.1 系统架构层次图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  主导航页面  │  Boss模块页面  │  熊模块页面  │  工人模块页面    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   页面管理层 (Page Layer)                     │
├─────────────────────────────────────────────────────────────┤
│              PageManager (消息路由、状态管理)                 │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
├─────────────────────────────────────────────────────────────┤
│  BearModel  │  Male01Model  │  MeatModel  │  BossMonster    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   控制器层 (Controller Layer)                │
├─────────────────────────────────────────────────────────────┤
│         AnimationController  │  DebugPanel  │  Examples      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   资源加载层 (Loader Layer)                   │
├─────────────────────────────────────────────────────────────┤
│              ModelLoader (FBX加载、纹理管理)                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   渲染引擎层 (Engine Layer)                   │
├─────────────────────────────────────────────────────────────┤
│                    Three.js + WebGL                         │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 模块依赖关系图
```
PageManager ──┐
              ├─→ BearModel ──→ AnimationController
              │                      │
              │                      ├─→ ModelLoader
              │                      └─→ BearDebugPanel
              │
              ├─→ Male01Model ──→ AnimationController
              │                      │
              │                      └─→ ModelLoader
              │
              └─→ MeatModel ──→ ModelLoader
```

## 6. 数据流和通信机制

### 6.1 模块间通信流程
1. **页面加载**: PageManager初始化并监听消息
2. **模块启动**: 各模块通过PageManager注册状态
3. **状态同步**: 实时状态通过消息系统广播
4. **错误处理**: 统一的错误收集和显示机制

### 6.2 事件驱动架构
```typescript
// 消息类型定义
interface PageMessage {
  type: string;
  data?: any;
  timestamp: number;
}

// 事件流向
User Action → Module Event → PageManager → Message Broadcast → UI Update
```

## 7. 构建和部署配置

### 7.1 开发环境配置
- **热重载**: 支持所有页面的实时更新
- **类型检查**: 实时TypeScript类型验证
- **资源代理**: 开发服务器自动处理模型文件路径

### 7.2 生产构建优化
- **代码分割**: 每个模块独立打包
- **资源压缩**: 自动压缩JS、CSS和资源文件
- **缓存策略**: 长期缓存静态资源

### 7.3 部署注意事项
- **模型文件**: 需要正确配置静态资源路径
- **CORS设置**: 确保FBX文件可以正常加载
- **性能监控**: 建议添加性能监控和错误上报

---

*本文档基于项目当前状态分析生成，版本: 1.0.0*
*最后更新: 2025-07-28*
