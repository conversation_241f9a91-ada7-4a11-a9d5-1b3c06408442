import * as THREE from 'three';
import { FBXLoader } from 'three/addons/loaders/FBXLoader.js';

export interface ModelLoadResult {
  model: THREE.Group;
  animations: THREE.AnimationClip[];
  mixer?: THREE.AnimationMixer;
}

export interface AnimationFile {
  name: string;
  path: string;
  loop: boolean;
  priority: number;
}

export class ModelLoader {
  private loader: FBXLoader;
  private loadingManager: THREE.LoadingManager;
  private loadedModels: Map<string, ModelLoadResult> = new Map();

  constructor() {
    this.loadingManager = new THREE.LoadingManager();
    this.loader = new FBXLoader(this.loadingManager);

    // 设置加载进度回调
    this.loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
      console.log(`Loading progress: ${itemsLoaded}/${itemsTotal} - ${url}`);
    };

    this.loadingManager.onError = (url) => {
      console.error(`Error loading: ${url}`);
    };
  }

  /**
   * 加载主模型文件
   */
  async loadMainModel(path: string): Promise<ModelLoadResult> {
    return new Promise((resolve, reject) => {
      this.loader.load(
        path,
        (object) => {
          const result: ModelLoadResult = {
            model: object,
            animations: object.animations || [],
            mixer: new THREE.AnimationMixer(object)
          };

          console.log(`Loaded main model: ${path}`);
          console.log(`Animations found: ${result.animations.length}`);
          
          this.loadedModels.set('main', result);
          resolve(result);
        },
        (progress) => {
          console.log(`Loading ${path}: ${(progress.loaded / progress.total * 100)}%`);
        },
        (error) => {
          console.error(`Error loading main model ${path}:`, error);
          reject(error);
        }
      );
    });
  }

  /**
   * 加载动画文件
   */
  async loadAnimationFile(name: string, path: string): Promise<THREE.AnimationClip[]> {
    return new Promise((resolve, reject) => {
      console.log(`🎬 Loading animation file: ${name} from ${path}`);
      
      this.loader.load(
        path,
        (object) => {
          const animations = object.animations || [];
          console.log(`🎬 Loaded animation ${name}: ${animations.length} clips found`);
          
          // 打印每个动画的详细信息
          animations.forEach((clip, index) => {
            console.log(`  📽️ Clip ${index}: name="${clip.name}", duration=${clip.duration}s, tracks=${clip.tracks.length}`);
            
            // 重命名动画以便识别
            clip.name = `${name}_${index}`;
            console.log(`  🏷️ Renamed to: ${clip.name}`);
          });

          if (animations.length === 0) {
            console.warn(`⚠️ No animations found in file: ${path}`);
          }

          resolve(animations);
        },
        (progress) => {
          console.log(`Loading animation ${name}: ${(progress.loaded / progress.total * 100).toFixed(1)}%`);
        },
        (error) => {
          console.error(`❌ Error loading animation ${name} from ${path}:`, error);
          reject(error);
        }
      );
    });
  }

  /**
   * 批量加载动画文件
   */
  async loadAnimations(animationFiles: AnimationFile[]): Promise<Map<string, THREE.AnimationClip[]>> {
    const animations = new Map<string, THREE.AnimationClip[]>();
    
    for (const animFile of animationFiles) {
      try {
        const clips = await this.loadAnimationFile(animFile.name, animFile.path);
        animations.set(animFile.name, clips);
      } catch (error) {
        console.error(`Failed to load animation ${animFile.name}:`, error);
      }
    }

    return animations;
  }

  /**
   * 预处理模型（设置阴影、材质等）
   */
  preprocessModel(model: THREE.Group): void {
    model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        child.castShadow = true;
        child.receiveShadow = true;

        // 如果是SkinnedMesh，确保正确设置
        if (child instanceof THREE.SkinnedMesh) {
          child.frustumCulled = false;
        }

        // 材质优化
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => this.optimizeMaterial(mat));
          } else {
            this.optimizeMaterial(child.material);
          }
        }
      }
    });
  }

  private optimizeMaterial(material: THREE.Material): void {
    if (material instanceof THREE.MeshStandardMaterial || 
        material instanceof THREE.MeshPhongMaterial) {
      material.needsUpdate = true;
    }
  }

  /**
   * 获取已加载的模型
   */
  getLoadedModel(name: string): ModelLoadResult | undefined {
    return this.loadedModels.get(name);
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.loadedModels.forEach((result) => {
      if (result.mixer) {
        result.mixer.stopAllAction();
      }
      // 清理模型几何体和材质
      result.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => mat.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    });
    this.loadedModels.clear();
  }
} 