import * as THREE from 'three';

export interface AnimationState {
  name: string;
  clip: THREE.AnimationClip;
  action?: THREE.AnimationAction;
  loop: boolean;
  priority: number;
  fadeInTime: number;
  fadeOutTime: number;
}

export interface AnimationTransition {
  from: string;
  to: string;
  condition?: () => boolean;
  duration: number;
}

export class AnimationController {
  private mixer: THREE.AnimationMixer;
  private states: Map<string, AnimationState> = new Map();
  private transitions: AnimationTransition[] = [];
  private currentState: string | null = null;
  private isTransitioning = false;
  private transitionPromise: Promise<void> | null = null;

  constructor(mixer: THREE.AnimationMixer) {
    this.mixer = mixer;
    
    // 监听动画事件
    this.mixer.addEventListener('finished', (event) => {
      this.onAnimationFinished(event);
    });
  }

  /**
   * 添加动画状态
   */
  addState(name: string, clip: THREE.AnimationClip, options: Partial<AnimationState> = {}): void {
    const action = this.mixer.clipAction(clip);
    
    const state: AnimationState = {
      name,
      clip,
      action,
      loop: options.loop ?? true,
      priority: options.priority ?? 0,
      fadeInTime: options.fadeInTime ?? 0.3,
      fadeOutTime: options.fadeOutTime ?? 0.3,
      ...options
    };

    // 设置动画属性
    if (state.action) {
      state.action.setLoop(state.loop ? THREE.LoopRepeat : THREE.LoopOnce, Infinity);
      state.action.clampWhenFinished = !state.loop;
      state.action.enabled = false;
    }

    this.states.set(name, state);
    console.log(`Added animation state: ${name}`);
  }

  /**
   * 添加状态转换规则
   */
  addTransition(from: string, to: string, duration = 0.3, condition?: () => boolean): void {
    this.transitions.push({
      from,
      to,
      duration,
      condition
    });
  }

  /**
   * 播放指定动画状态
   */
  async playState(stateName: string, force = false): Promise<void> {
    console.log(`🎬 AnimationController.playState called: ${stateName}, current: ${this.currentState}, force: ${force}`);
    
    // 特殊检查：如果当前是死亡状态，不允许切换到其他状态
    if (this.currentState === 'death' && stateName !== 'death') {
      console.warn(`🎬 Blocking state change from death to ${stateName} to preserve death animation`);
      return;
    }
    
    // 更强的保护：如果死亡动画正在播放，不允许切换到其他状态
    const deathState = this.states.get('death');
    if (deathState?.action?.isRunning() && stateName !== 'death') {
      console.warn(`🎬 Death animation is running, blocking switch to ${stateName}`);
      return;
    }
    
    if (!force && this.currentState === stateName) {
      console.log(`🎬 Skipping ${stateName} - already current state`);
      return;
    }

    const state = this.states.get(stateName);
    if (!state || !state.action) {
      console.error(`Animation state not found: ${stateName}`);
      return;
    }

    // 如果正在转换，等待完成
    if (this.isTransitioning && this.transitionPromise) {
      await this.transitionPromise;
    }

    this.transitionPromise = this.executeTransition(stateName);
    return this.transitionPromise;
  }

  private async executeTransition(newStateName: string): Promise<void> {
    return new Promise((resolve) => {
      this.isTransitioning = true;
      const newState = this.states.get(newStateName)!;
      const currentState = this.currentState ? this.states.get(this.currentState) : null;

      // 查找转换规则
      const transition = this.transitions.find(t => 
        t.from === (this.currentState || '') && t.to === newStateName
      );

      const fadeTime = transition?.duration ?? newState.fadeInTime;

      // 特殊处理死亡动画：停止所有其他动画
      if (newStateName === 'death') {
        console.log('🎬 Playing death animation - stopping ALL other animations immediately');
        
        // 立即停止并禁用所有非死亡动画
        for (const [stateName, state] of this.states) {
          if (stateName !== 'death' && state.action) {
            state.action.stop();
            state.action.enabled = false;
            state.action.setEffectiveWeight(0);
            console.log(`🎬 Stopped animation: ${stateName}`);
          }
        }
        
        // 重置并播放死亡动画，确保最高权重
        const deathAction = newState.action!;
        deathAction.reset();
        deathAction.enabled = true;
        deathAction.setEffectiveWeight(1.0);
        deathAction.setEffectiveTimeScale(1.0);
        deathAction.play();
        
        console.log(`🎬 Death animation started with weight: ${deathAction.getEffectiveWeight()}`);
        console.log(`🎬 Death animation enabled: ${deathAction.enabled}`);
        console.log(`🎬 Death animation running: ${deathAction.isRunning()}`);
        
        // 死亡动画无需渐变，立即生效
        this.currentState = newStateName;
        this.isTransitioning = false;
        this.transitionPromise = null;
        resolve();
        
      } else {
        // 正常动画切换
        console.log(`🎬 Normal animation transition from ${this.currentState} to ${newStateName}`);
        
        // 停止当前动画
        if (currentState?.action) {
          currentState.action.fadeOut(fadeTime);
          console.log(`🎬 Fading out: ${this.currentState}`);
        }

        // 开始新动画
        newState.action!.reset();
        newState.action!.fadeIn(fadeTime);
        newState.action!.enabled = true;
        newState.action!.play();
        
        console.log(`🎬 Fading in: ${newStateName}`);

        this.currentState = newStateName;

        // 等待过渡完成
        setTimeout(() => {
          this.isTransitioning = false;
          this.transitionPromise = null;
          console.log(`🎬 Transition to ${newStateName} completed`);
          resolve();
        }, fadeTime * 1000);
      }
    });
  }

  /**
   * 停止当前动画
   */
  stop(): void {
    if (this.currentState) {
      const state = this.states.get(this.currentState);
      if (state?.action) {
        state.action.stop();
      }
      this.currentState = null;
    }
  }

  /**
   * 暂停当前动画
   */
  pause(): void {
    if (this.currentState) {
      const state = this.states.get(this.currentState);
      if (state?.action) {
        state.action.paused = true;
      }
    }
  }

  /**
   * 恢复当前动画
   */
  resume(): void {
    if (this.currentState) {
      const state = this.states.get(this.currentState);
      if (state?.action) {
        state.action.paused = false;
      }
    }
  }

  /**
   * 设置动画速度
   */
  setTimeScale(timeScale: number): void {
    if (this.currentState) {
      const state = this.states.get(this.currentState);
      if (state?.action) {
        state.action.timeScale = timeScale;
      }
    }
  }

  /**
   * 设置所有动画的全局速度
   */
  setGlobalTimeScale(timeScale: number): void {
    this.mixer.timeScale = timeScale;
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): string | null {
    return this.currentState;
  }

  /**
   * 获取动画状态信息
   */
  getStateInfo(stateName: string): AnimationState | undefined {
    return this.states.get(stateName);
  }

  /**
   * 获取所有状态名称
   */
  getStateNames(): string[] {
    return Array.from(this.states.keys());
  }

  /**
   * 检查是否可以转换到指定状态
   */
  canTransitionTo(stateName: string): boolean {
    if (!this.currentState) return true;
    
    const transition = this.transitions.find(t => 
      t.from === this.currentState && t.to === stateName
    );

    if (!transition) return false;
    if (transition.condition) return transition.condition();
    
    return true;
  }

  /**
   * 更新动画混合器
   */
  update(deltaTime: number): void {
    this.mixer.update(deltaTime);
  }

  /**
   * 动画完成事件处理
   */
  private onAnimationFinished(event: any): void {
    const action = event.action;
    const stateName = this.getStateNameByAction(action);
    
    if (stateName) {
      console.log(`Animation finished: ${stateName}`);
      
      // 查找自动转换
      const autoTransition = this.transitions.find(t => 
        t.from === stateName && (!t.condition || t.condition())
      );

      if (autoTransition) {
        // 特殊处理：如果当前状态是死亡状态，不执行自动转换
        if (stateName === 'death') {
          console.log(`Animation finished: ${stateName} - blocking auto transition to prevent death animation interruption`);
          return;
        }
        this.playState(autoTransition.to);
      }
    }
  }

  /**
   * 根据Action查找状态名称
   */
  private getStateNameByAction(action: THREE.AnimationAction): string | null {
    for (const [name, state] of this.states) {
      if (state.action === action) {
        return name;
      }
    }
    return null;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.mixer.stopAllAction();
    this.states.clear();
    this.transitions = [];
    this.currentState = null;
    this.isTransitioning = false;
    this.transitionPromise = null;
  }
} 