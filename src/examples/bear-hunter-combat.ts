import * as THREE from 'three';
import { BearModel, BearState } from '../entities/BearModel.js';
import { Male01Model, Male01State } from '../entities/Male01Model.js';
import { MeatModel } from '../entities/MeatModel.js';

export interface CombatConfig {
  bearCount: number;        // 熊的数量
  hunterCount: number;      // 猎人数量
  bearHealth: number;       // 熊的血量
  hunterHealth: number;     // 猎人血量
  bearDamage: number;       // 熊的攻击伤害
  hunterDamage: number;     // 猎人射击伤害
  meatDropChance: number;   // 肉块掉落概率 (0-1)
  battleAreaSize: number;   // 战斗区域大小
}

export class CombatParticipant {
  public readonly model: BearModel | Male01Model;
  public readonly type: 'bear' | 'hunter';
  public readonly id: string;
  public readonly team: 'bears' | 'hunters';

  constructor(
    model: BearModel | Male01Model,
    type: 'bear' | 'hunter',
    id: string,
    team: 'bears' | 'hunters'
  ) {
    this.model = model;
    this.type = type;
    this.id = id;
    this.team = team;
  }

  // 通过模型获取状态，消除重复状态管理
  get health(): number {
    return this.model.getHealth();
  }

  get maxHealth(): number {
    return this.model.getMaxHealth();
  }

  get isAlive(): boolean {
    return this.model.getIsAlive();
  }

  get position(): THREE.Vector3 {
    return this.model.getModel()?.position.clone() || new THREE.Vector3();
  }

  // 提供设置方法，委托给模型
  takeDamage(damage: number): void {
    this.model.takeDamage(damage);
  }

  setHealth(health: number): void {
    this.model.setHealth(health);
  }
}

export interface MeatDrop {
  model: MeatModel;
  id: string;
  position: THREE.Vector3;
  dropTime: number;
}

export class BearHunterCombat {
  private scene: THREE.Scene;
  private config: CombatConfig;
  private participants: Map<string, CombatParticipant> = new Map();
  private meatDrops: Map<string, MeatDrop> = new Map();
  private combatStats = {
    bearsKilled: 0,
    huntersKilled: 0,
    meatDropped: 0,
    battleStartTime: 0,
    battleEndTime: 0
  };
  
  // 战斗状态
  private isBattleActive = false;
  private battleTimer = 0;
  private clock = new THREE.Clock();
  
  // 手动控制
  private playerControlledHunter: CombatParticipant | null = null;
  private isPlayerControlEnabled = false;
  
  // 事件回调
  private onBattleStart?: () => void;
  private onBattleEnd?: (winner: 'bears' | 'hunters' | 'draw', stats: any) => void;
  private onParticipantDeath?: (participant: CombatParticipant, killer?: CombatParticipant) => void;
  private onMeatDrop?: (meat: MeatDrop, deadBear: CombatParticipant) => void;
  private onDamageDealt?: (attacker: CombatParticipant, target: CombatParticipant, damage: number) => void;
  private onMeatCollected?: (hunter: CombatParticipant, meat: MeatDrop) => void;
  
  // 肉块收集系统
  private collectionRadius = 1.5; // 收集范围
  private backpackMeat: Map<string, MeatDrop[]> = new Map(); // 每个猎人的背包肉块

  constructor(scene: THREE.Scene, config?: Partial<CombatConfig>) {
    this.scene = scene;
    
    // 默认配置
    this.config = {
      bearCount: 3,
      hunterCount: 1,  // 默认只有一个猎人
      bearHealth: 100,
      hunterHealth: 100,
      bearDamage: 30,
      hunterDamage: 215,
      meatDropChance: 0.8,
      battleAreaSize: 15,
      ...config
    };
  }

  /**
   * 初始化战斗系统
   */
  async initializeCombat(): Promise<void> {
    console.log('初始化熊猎人战斗系统...');
    
    // 清理现有参与者
    this.clearCombat();
    
    // 创建熊群
    await this.createBears();
    
    // 创建猎人队伍
    await this.createHunters();
    
    // 设置战斗关系
    this.setupCombatRelations();
    
    console.log(`战斗系统初始化完成 - 熊: ${this.config.bearCount}, 猎人: ${this.config.hunterCount}`);
  }

  /**
   * 创建熊群
   */
  private async createBears(): Promise<void> {
    const loadPromises: Promise<void>[] = [];
    
    for (let i = 0; i < this.config.bearCount; i++) {
      const bearId = `bear_${i}`;
      
      // 完全随机位置分布
      const angle = Math.random() * Math.PI * 2;  // 随机角度
      const radius = (Math.random() * 0.4 + 0.2) * this.config.battleAreaSize;  // 随机半径 (0.2-0.6倍战场大小)
      const position = new THREE.Vector3(
        Math.cos(angle) * radius + (Math.random() - 0.5) * 4,  // 添加额外随机偏移
        0,
        Math.sin(angle) * radius + (Math.random() - 0.5) * 4   // 添加额外随机偏移
      );
      
      // 创建熊模型
      const bear = new BearModel(this.scene, {
        name: `Bear_${i}`,
        position: position,
        scale: 0.010  // 调整熊的大小，使其相对于猎人更协调
      });
      
      // 设置熊的属性
      bear.setAttackRange(3.5);
      bear.setAttackCooldown(2000);
      bear.setChaseSpeed(4.0);
      bear.setWalkBounds(-this.config.battleAreaSize, this.config.battleAreaSize);
      
      // 设置熊的血量
      bear.setHealth(this.config.bearHealth);
      
      // 创建参与者数据
      const participant = new CombatParticipant(
        bear,
        'bear',
        bearId,
        'bears'
      );
      
      this.participants.set(bearId, participant);
      
      // 设置事件回调
      bear.setEventCallbacks({
        onLoadComplete: () => {
          console.log(`熊 ${bearId} 加载完成`);
          // 开始巡逻
          bear.startPatrol();
        },
        onAttackTarget: (target) => {
          this.handleAttack(participant, target);
        },
        onDeathAnimationComplete: () => {
          // 死亡动画完成后处理肉块掉落和熊的消失
          this.handleBearDeathComplete(participant);
        }
      });
      
      // 异步加载
      loadPromises.push(bear.initialize());
    }
    
    await Promise.all(loadPromises);
    console.log('所有熊创建完成');
  }

  /**
   * 创建猎人队伍
   */
  private async createHunters(): Promise<void> {
    const loadPromises: Promise<void>[] = [];
    
    for (let i = 0; i < this.config.hunterCount; i++) {
      const hunterId = `hunter_${i}`;
      
      // 猎人位置（战场中心附近的随机位置）
      const position = new THREE.Vector3(
        (Math.random() - 0.5) * 6,  // 在中心附近±3的范围内随机
        0,
        (Math.random() - 0.5) * 6   // 在中心附近±3的范围内随机
      );
      
      // 创建猎人模型
      const hunter = new Male01Model(this.scene, {
        name: `Hunter_${i}`,
        position: position,
        scale: 0.025
      });
      
      // 设置猎人的属性
      hunter.setShootingRange(8.0);
      hunter.setShootingCooldown(1500);
      hunter.setWalkSpeed(2.0);
      hunter.setRunSpeed(3.5);
      hunter.setMoveBounds(-this.config.battleAreaSize, this.config.battleAreaSize);
      
      // 设置猎人的血量
      hunter.setHealth(this.config.hunterHealth);
      
      // 创建参与者数据
      const participant = new CombatParticipant(
        hunter,
        'hunter',
        hunterId,
        'hunters'
      );
      
      this.participants.set(hunterId, participant);
      
      // 设置事件回调
      hunter.setEventCallbacks({
        onLoadComplete: () => {
          console.log(`猎人 ${hunterId} 加载完成`);
          // 测试子弹系统是否正确初始化
          console.log(`🔍 测试 ${hunterId} 的子弹系统状态`);
          hunter.testBulletSystem();
          
          // 额外测试：尝试发射一颗测试子弹
          setTimeout(() => {
            console.log(`🧪 为 ${hunterId} 发射初始化测试子弹`);
            hunter.shoot(false);
          }, 1000);
          
          // 开始巡逻
          hunter.startPatrol();
        },
        onShootTarget: (target) => {
          console.log(`🎯 ${hunterId} 正在射击目标: ${target.name}`);
          this.handleShoot(participant, target);
        },
        onDeath: () => {
          this.handleParticipantDeath(participant);
        }
      });
      
      // 异步加载
      loadPromises.push(hunter.initialize());
    }
    
    await Promise.all(loadPromises);
    console.log('所有猎人创建完成');
  }

  /**
   * 设置战斗关系
   */
  private setupCombatRelations(): void {
    // 为熊添加猎人目标
    for (const [bearId, bearParticipant] of this.participants) {
      if (bearParticipant.type === 'bear') {
        const bear = bearParticipant.model as BearModel;
        
        // 添加所有猎人作为攻击目标
        for (const [hunterId, hunterParticipant] of this.participants) {
          if (hunterParticipant.type === 'hunter') {
            const hunterModel = hunterParticipant.model.getModel();
            if (hunterModel) {
              hunterModel.name = hunterId; // 设置名称用于识别
              bear.addTarget(hunterModel);
            }
          }
        }
      }
    }
    
    // 为猎人添加熊目标
    for (const [hunterId, hunterParticipant] of this.participants) {
      if (hunterParticipant.type === 'hunter') {
        const hunter = hunterParticipant.model as Male01Model;
        
        // 添加所有熊作为射击目标
        for (const [bearId, bearParticipant] of this.participants) {
          if (bearParticipant.type === 'bear') {
            const bearModel = bearParticipant.model.getModel();
            if (bearModel) {
              bearModel.name = bearId; // 设置名称用于识别
              hunter.addTarget(bearModel);
            }
          }
        }
      }
    }
    
    console.log('战斗关系设置完成');
  }

  /**
   * 处理攻击事件
   */
  private handleAttack(attacker: CombatParticipant, targetModel: THREE.Object3D): void {
    // 检查目标模型是否有效
    if (!targetModel || !targetModel.name) {
      console.warn(`⚠️ ${attacker.id} 尝试攻击无效目标`);
      return;
    }
    
    // 根据模型名称找到目标参与者
    const target = this.findParticipantByModelName(targetModel.name);
    if (!target || !target.isAlive) {
      console.warn(`⚠️ ${attacker.id} 尝试攻击不存在或已死亡的目标: ${targetModel.name}`);
      return;
    }
    
    // 计算伤害
    const damage = this.config.bearDamage;
    
    // 应用伤害
    this.dealDamage(target, damage, attacker);
    
    console.log(`🐻 ${attacker.id} 攻击了 👨 ${target.id}, 造成 ${damage} 伤害`);
  }

  /**
   * 处理射击事件
   */
  private handleShoot(attacker: CombatParticipant, targetModel: THREE.Object3D): void {
    // 检查目标模型是否有效
    if (!targetModel || !targetModel.name) {
      console.warn(`⚠️ ${attacker.id} 尝试射击无效目标`);
      return;
    }
    
    // 根据模型名称找到目标参与者
    const target = this.findParticipantByModelName(targetModel.name);
    if (!target || !target.isAlive) {
      console.warn(`⚠️ ${attacker.id} 尝试射击不存在或已死亡的目标: ${targetModel.name}`);
      return;
    }
    
    // 计算伤害
    const damage = this.config.hunterDamage;
    
    // 应用伤害
    this.dealDamage(target, damage, attacker);
    
    console.log(`🔫 ${attacker.id} 射击了 🐻 ${target.id}, 造成 ${damage} 伤害`);
  }

  /**
   * 应用伤害
   */
  private dealDamage(target: CombatParticipant, damage: number, attacker?: CombatParticipant): void {
    if (!target.isAlive) return;
    
    console.log(`💥 Dealing ${damage} damage to ${target.id}`);
    
    // 直接通过 CombatParticipant 委托给模型处理伤害
    target.takeDamage(damage);
    
    console.log(`${target.id} health: ${target.health}/${target.maxHealth}`);
    
    // 触发伤害事件
    if (this.onDamageDealt && attacker) {
      this.onDamageDealt(attacker, target, damage);
    }
    
    // 检查是否死亡（状态现在直接从模型获取）
    if (target.health <= 0) {
      // 状态已经由模型内部管理，不需要手动设置
      
      // 从战斗关系中移除
      this.removeFromCombat(target);
      
      if (target.type === 'bear') {
        // 熊死亡：等待死亡动画完成后再掉落肉块和移除
        this.combatStats.bearsKilled++;
        console.log(`🐻 ${target.id} 已死亡，等待死亡动画完成...`);
        // 死亡动画完成后会调用 handleBearDeathComplete
      } else {
        // 猎人死亡：立即处理
        this.combatStats.huntersKilled++;
        // 触发死亡事件
        this.handleParticipantDeath(target, attacker);
      }
      
      // 检查战斗是否结束
      this.checkBattleEnd();
    }
  }

  // killParticipant 方法已移除，死亡逻辑现在在 dealDamage 中处理

  /**
   * 处理熊死亡动画完成
   */
  private async handleBearDeathComplete(deadBear: CombatParticipant): Promise<void> {
    console.log(`🐻 ${deadBear.id} 死亡动画完成，开始掉落肉块...`);
    
    // 掉落肉块
    await this.dropMeat(deadBear);
    
    // 移除熊模型
    if (deadBear.model && deadBear.model.getModel()) {
      this.scene.remove(deadBear.model.getModel()!);
      deadBear.model.dispose();
    }
    
    // 触发参与者死亡事件
    this.handleParticipantDeath(deadBear);
  }

  /**
   * 处理参与者死亡
   */
  private handleParticipantDeath(deadParticipant: CombatParticipant, killer?: CombatParticipant): void {
    console.log(`💀 ${deadParticipant.id} 死亡${killer ? ` (被 ${killer.id} 杀死)` : ''}`);
    
    // 如果死亡的是玩家控制的猎人，自动切换控制或禁用控制
    if (this.playerControlledHunter === deadParticipant) {
      console.log('玩家控制的猎人已死亡，尝试切换到其他猎人...');
      
      // 尝试找到另一个存活的猎人
      let foundAnotherHunter = false;
      for (const [id, participant] of this.participants) {
        if (participant.type === 'hunter' && participant.isAlive && participant !== deadParticipant) {
          this.playerControlledHunter = null; // 先清空当前控制
          this.enablePlayerControl(id);
          foundAnotherHunter = true;
          console.log(`自动切换到猎人: ${id}`);
          break;
        }
      }
      
      if (!foundAnotherHunter) {
        console.log('没有其他存活的猎人，禁用玩家控制');
        this.disablePlayerControl();
      }
    }
    
    if (this.onParticipantDeath) {
      this.onParticipantDeath(deadParticipant, killer);
    }
  }

  /**
   * 从战斗中移除参与者
   */
  private removeFromCombat(participant: CombatParticipant): void {
    // 从所有敌对目标列表中移除
    for (const [id, p] of this.participants) {
      if (p.isAlive && p.team !== participant.team) {
        const model = p.model.getModel();
        if (model && participant.model.getModel()) {
          if (p.type === 'bear') {
            (p.model as BearModel).removeTarget(participant.model.getModel()!);
          } else {
            (p.model as Male01Model).removeTarget(participant.model.getModel()!);
          }
        }
      }
    }
  }

  /**
   * 掉落肉块
   */
  private async dropMeat(deadBear: CombatParticipant): Promise<void> {
    const bearPosition = deadBear.model.getModel()?.position || deadBear.position;
    
    // 掉落3块肉，每块都有抛物线动画
    const meatCount = 3;
    const dropPromises: Promise<void>[] = [];
    
    for (let i = 0; i < meatCount; i++) {
      // 检查每块肉的掉落概率
      if (Math.random() > this.config.meatDropChance) {
        continue;
      }
      
      dropPromises.push(this.createAndDropMeat(deadBear, bearPosition, i));
    }
    
    if (dropPromises.length === 0) {
      console.log(`🐻 ${deadBear.id} 没有掉落肉块`);
      return;
    }
    
    try {
      await Promise.all(dropPromises);
      console.log(`🥩 ${deadBear.id} 总共掉落了 ${dropPromises.length} 块肉`);
    } catch (error) {
      console.error('创建肉块失败:', error);
    }
  }

  /**
   * 创建单块肉并执行抛物线掉落动画
   */
  private async createAndDropMeat(deadBear: CombatParticipant, bearPosition: THREE.Vector3, index: number): Promise<void> {
    const meatId = `meat_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 计算随机的目标位置（相对于熊的位置）
    const angle = (Math.PI * 2 / 3) * index + (Math.random() - 0.5) * 0.5; // 每块肉间隔120度，加一些随机偏移
    const distance = 1.5 + Math.random() * 1.5; // 掉落距离 1.5-3 单位
    const targetX = bearPosition.x + Math.cos(angle) * distance;
    const targetZ = bearPosition.z + Math.sin(angle) * distance;
    
    // 创建肉块，初始位置在熊的上方
    const meat = new MeatModel(this.scene, {
      name: `Meat_from_${deadBear.id}_${index}`,
      position: new THREE.Vector3(
        bearPosition.x + (Math.random() - 0.5) * 0.5, // 初始位置稍微随机
        2.0 + Math.random() * 1.0, // 从熊上方2-3单位高度开始
        bearPosition.z + (Math.random() - 0.5) * 0.5
      ),
      scale: 0.4 + Math.random() * 0.2  // 随机大小 0.4-0.6
    });
    
    await meat.initialize();
    
    // 执行抛物线掉落动画
    await this.animateMeatDrop(meat, targetX, targetZ);
    
    // 创建肉块掉落数据
    const meatDrop: MeatDrop = {
      model: meat,
      id: meatId,
      position: meat.getPosition(),
      dropTime: Date.now()
    };
    
    this.meatDrops.set(meatId, meatDrop);
    this.combatStats.meatDropped++;
    
    console.log(`🥩 肉块 ${meatId} 掉落完成`);
    
    if (this.onMeatDrop) {
      this.onMeatDrop(meatDrop, deadBear);
    }
  }

  /**
   * 执行肉块抛物线掉落动画
   */
  private animateMeatDrop(meat: MeatModel, targetX: number, targetZ: number): Promise<void> {
    return new Promise((resolve) => {
      const model = meat.getModel();
      if (!model) {
        resolve();
        return;
      }

      const startPos = model.position.clone();
      const endPos = new THREE.Vector3(targetX, 0.1, targetZ); // 最终落地高度
      const duration = 0.8 + Math.random() * 0.4; // 动画持续时间 0.8-1.2 秒
      
      let startTime: number | null = null;
      
      const animate = (currentTime: number) => {
        if (startTime === null) {
          startTime = currentTime;
        }
        
        const elapsed = (currentTime - startTime) / 1000; // 转换为秒
        const progress = Math.min(elapsed / duration, 1); // 0-1 进度
        
        if (progress < 1) {
          // 水平位置：线性插值
          const currentX = startPos.x + (endPos.x - startPos.x) * progress;
          const currentZ = startPos.z + (endPos.z - startPos.z) * progress;
          
          // 垂直位置：抛物线（重力效果）
          const peakHeight = startPos.y + 1.0; // 抛物线顶点高度
          const currentY = startPos.y + 
            (peakHeight - startPos.y) * (4 * progress * (1 - progress)) + // 抛物线部分
            (endPos.y - startPos.y) * progress; // 线性下降部分
          
          model.position.set(currentX, Math.max(currentY, endPos.y), currentZ);
          
          // 添加旋转效果，让肉块在空中翻滚
          model.rotation.x += 0.1;
          model.rotation.z += 0.05;
          
          requestAnimationFrame(animate);
        } else {
          // 动画完成，确保肉块在最终位置
          model.position.copy(endPos);
          
          // 重置旋转，让肉块平躺在地面上
          model.rotation.set(0, Math.random() * Math.PI * 2, 0); // 只保留Y轴随机旋转
          
          // 添加一个小的弹跳效果
          this.addBounceEffect(meat);
          
          resolve();
        }
      };
      
      requestAnimationFrame(animate);
    });
  }

  /**
   * 添加肉块落地弹跳效果
   */
  private addBounceEffect(meat: MeatModel): void {
    const model = meat.getModel();
    if (!model) return;

    const originalY = model.position.y;
    const bounceHeight = 0.3;
    const bounceDuration = 0.3;
    
    let startTime: number | null = null;
    
    const bounce = (currentTime: number) => {
      if (startTime === null) {
        startTime = currentTime;
      }
      
      const elapsed = (currentTime - startTime) / 1000;
      const progress = Math.min(elapsed / bounceDuration, 1);
      
      if (progress < 1) {
        // 使用正弦函数创建弹跳效果
        const bounceProgress = Math.sin(progress * Math.PI);
        model.position.y = originalY + bounceHeight * bounceProgress;
        
        requestAnimationFrame(bounce);
      } else {
        // 弹跳完成，确保回到原位
        model.position.y = originalY;
      }
    };
    
    requestAnimationFrame(bounce);
  }

  /**
   * 根据模型名称查找参与者
   */
  private findParticipantByModelName(modelName: string): CombatParticipant | null {
    return this.participants.get(modelName) || null;
  }

  /**
   * 开始战斗
   */
  startBattle(): void {
    if (this.isBattleActive) return;
    
    this.isBattleActive = true;
    this.combatStats.battleStartTime = Date.now();
    this.battleTimer = 0;
    this.clock.start();
    
    console.log('🎺 战斗开始！');
    
    if (this.onBattleStart) {
      this.onBattleStart();
    }
  }

  /**
   * 检查战斗是否结束
   */
  private checkBattleEnd(): void {
    if (!this.isBattleActive) return;
    
    const aliveBears = Array.from(this.participants.values()).filter(p => p.type === 'bear' && p.isAlive);
    const aliveHunters = Array.from(this.participants.values()).filter(p => p.type === 'hunter' && p.isAlive);
    
    let winner: 'bears' | 'hunters' | 'draw' | null = null;
    
    if (aliveBears.length === 0 && aliveHunters.length === 0) {
      winner = 'draw';
    } else if (aliveBears.length === 0) {
      winner = 'hunters';
    } else if (aliveHunters.length === 0) {
      winner = 'bears';
    }
    
    if (winner) {
      this.endBattle(winner);
    }
  }

  /**
   * 结束战斗
   */
  private endBattle(winner: 'bears' | 'hunters' | 'draw'): void {
    if (!this.isBattleActive) return;
    
    this.isBattleActive = false;
    this.combatStats.battleEndTime = Date.now();
    
    const battleDuration = (this.combatStats.battleEndTime - this.combatStats.battleStartTime) / 1000;
    
    const finalStats = {
      ...this.combatStats,
      battleDuration,
      winner,
      survivingBears: Array.from(this.participants.values()).filter(p => p.type === 'bear' && p.isAlive).length,
      survivingHunters: Array.from(this.participants.values()).filter(p => p.type === 'hunter' && p.isAlive).length
    };
    
    console.log(`🏁 战斗结束！获胜者: ${winner}`);
    console.log('战斗统计:', finalStats);
    
    if (this.onBattleEnd) {
      this.onBattleEnd(winner, finalStats);
    }
  }

  /**
   * 更新战斗系统（每帧调用）
   */
  update(): void {
    if (!this.isBattleActive) return;
    
    const deltaTime = this.clock.getDelta();
    this.battleTimer += deltaTime;
    
    // 更新所有参与者
    for (const [id, participant] of this.participants) {
      participant.model.update();
      
      // 检查猎人是否需要收集肉块
      if (participant.type === 'hunter' && participant.isAlive) {
        this.checkMeatCollection(participant);
        // 更新背包肉块位置（跟随猎人移动）
        this.updateBackpackMeatPositions(participant);
      }
    }
  }

  /**
   * 获取战斗状态
   */
  getBattleStatus() {
    const aliveBears = Array.from(this.participants.values()).filter(p => p.type === 'bear' && p.isAlive);
    const aliveHunters = Array.from(this.participants.values()).filter(p => p.type === 'hunter' && p.isAlive);
    const totalMeat = this.meatDrops.size;
    
    return {
      isActive: this.isBattleActive,
      battleTime: this.battleTimer,
      participants: {
        bears: {
          total: this.config.bearCount,
          alive: aliveBears.length,
          killed: this.combatStats.bearsKilled
        },
        hunters: {
          total: this.config.hunterCount,
          alive: aliveHunters.length,
          killed: this.combatStats.huntersKilled
        }
      },
      meatDropped: this.combatStats.meatDropped,
      totalMeat: totalMeat,
      stats: this.combatStats,
      // 玩家控制状态
      playerControl: this.getPlayerControlStatus()
    };
  }

  /**
   * 获取所有参与者
   */
  getParticipants(): Map<string, CombatParticipant> {
    return new Map(this.participants);
  }

  /**
   * 获取所有肉块掉落
   */
  getMeatDrops(): Map<string, MeatDrop> {
    return new Map(this.meatDrops);
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CombatConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 启用玩家控制模式
   * @param hunterId 要控制的猎人ID，如果不指定则控制第一个存活的猎人
   */
  enablePlayerControl(hunterId?: string): boolean {
    let targetHunter: CombatParticipant | null = null;

    if (hunterId) {
      // 指定特定猎人
      const participant = this.participants.get(hunterId);
      if (participant && participant.type === 'hunter' && participant.isAlive) {
        targetHunter = participant;
      }
    } else {
      // 找到第一个存活的猎人
      for (const [id, participant] of this.participants) {
        if (participant.type === 'hunter' && participant.isAlive) {
          targetHunter = participant;
          break;
        }
      }
    }

    if (!targetHunter) {
      console.warn('没有找到可控制的猎人');
      return false;
    }

    // 如果之前有控制的猎人，先禁用其手动控制
    if (this.playerControlledHunter && this.playerControlledHunter !== targetHunter) {
      const prevHunter = this.playerControlledHunter.model as Male01Model;
      prevHunter.disableManualControl();
    }

    this.playerControlledHunter = targetHunter;
    this.isPlayerControlEnabled = true;

    // 启用目标猎人的手动控制
    const hunter = targetHunter.model as Male01Model;
    hunter.enableManualControl();

    console.log(`玩家现在控制猎人: ${targetHunter.id}`);
    return true;
  }

  /**
   * 禁用玩家控制模式
   */
  disablePlayerControl(): void {
    if (!this.playerControlledHunter) return;

    // 禁用当前控制的猎人的手动控制
    const hunter = this.playerControlledHunter.model as Male01Model;
    hunter.disableManualControl();
    
    // 恢复自动巡逻
    hunter.startPatrol();

    console.log(`停止控制猎人: ${this.playerControlledHunter.id}`);
    
    this.playerControlledHunter = null;
    this.isPlayerControlEnabled = false;
  }

  /**
   * 检查是否启用了玩家控制
   */
  isPlayerControlActive(): boolean {
    return this.isPlayerControlEnabled && this.playerControlledHunter !== null;
  }

  /**
   * 获取当前玩家控制的猎人
   */
  getPlayerControlledHunter(): CombatParticipant | null {
    return this.playerControlledHunter;
  }

  /**
   * 玩家控制移动 - 键盘方式
   */
  playerMoveKeyboard(forward: number, backward: number, left: number, right: number, isRunning = false): void {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) return;

    const hunter = this.playerControlledHunter.model as Male01Model;
    hunter.manualMoveKeyboard(forward, backward, left, right, isRunning);
  }

  /**
   * 玩家控制移动 - 遥感方式
   */
  playerMoveJoystick(joystickX: number, joystickY: number, isRunning = false): void {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) return;

    const hunter = this.playerControlledHunter.model as Male01Model;
    hunter.manualMoveJoystick(joystickX, joystickY, isRunning);
  }

  /**
   * 玩家控制移动 - 直接方向
   */
  playerMove(direction: THREE.Vector3, isRunning = false): void {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) return;

    const hunter = this.playerControlledHunter.model as Male01Model;
    hunter.manualMove(direction, isRunning);
  }

  /**
   * 玩家射击
   */
  playerShoot(): void {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) return;

    const hunter = this.playerControlledHunter.model as Male01Model;
    console.log('🔫 玩家手动射击');
    hunter.manualShoot();
  }

  /**
   * 玩家瞄准射击
   */
  playerAimShoot(): void {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) return;

    const hunter = this.playerControlledHunter.model as Male01Model;
    console.log('🎯 玩家手动瞄准射击');
    hunter.manualAimShoot();
  }

  /**
   * 测试所有猎人的子弹系统
   */
  testAllHunterBullets(): void {
    console.log('🧪 测试所有猎人的子弹系统');
    for (const [id, participant] of this.participants) {
      if (participant.type === 'hunter') {
        const hunter = participant.model as Male01Model;
        console.log(`🔍 测试猎人 ${id} 的子弹系统:`);
        hunter.testBulletSystem();
      }
    }
  }

  /**
   * 强制所有猎人射击测试子弹
   */
  forceHunterShoot(): void {
    console.log('💥 强制所有猎人射击');
    console.log(`📊 当前参与者数量: ${this.participants.size}`);
    
    for (const [id, participant] of this.participants) {
      console.log(`🔍 检查参与者: ${id}, 类型: ${participant.type}, 存活: ${participant.isAlive}`);
      
      if (participant.type === 'hunter' && participant.isAlive) {
        const hunter = participant.model as Male01Model;
        console.log(`🔫 强制 ${id} 射击 (使用shoot方法)`);
        console.log(`📊 猎人状态 - 已加载: ${hunter.getStatus().isLoaded}, 存活: ${hunter.getStatus().isAlive}`);
        
        // 同时测试子弹系统
        hunter.testBulletSystem();
        
        // 创建一个显眼的测试立方体作为可视化验证
        const testCube = new THREE.Mesh(
          new THREE.BoxGeometry(1, 1, 1),
          new THREE.MeshBasicMaterial({ color: 0xff0000 })
        );
        const hunterPos = hunter.getModel()?.position;
        if (hunterPos) {
          testCube.position.set(hunterPos.x + 2, hunterPos.y + 2, hunterPos.z);
          this.scene.add(testCube);
          
          // 3秒后移除测试立方体
          setTimeout(() => {
            this.scene.remove(testCube);
          }, 3000);
          
          console.log(`🟥 为 ${id} 创建了红色测试立方体 at (${testCube.position.x}, ${testCube.position.y}, ${testCube.position.z})`);
        }
        
        // 然后射击
        hunter.shoot(false); // 使用shoot而不是manualShoot，避免手动控制检查
      }
    }
  }

  /**
   * 获取玩家控制状态
   */
  getPlayerControlStatus() {
    if (!this.isPlayerControlEnabled || !this.playerControlledHunter) {
      return {
        isActive: false,
        controlledHunter: null,
        hunterStatus: null
      };
    }

    const hunter = this.playerControlledHunter.model as Male01Model;
    const hunterStatus = hunter.getStatus();
    const manualControlStatus = hunter.getManualControlStatus();

    return {
      isActive: true,
      controlledHunter: {
        id: this.playerControlledHunter.id,
        health: this.playerControlledHunter.health,
        maxHealth: this.playerControlledHunter.maxHealth,
        isAlive: this.playerControlledHunter.isAlive,
        position: hunterStatus.position
      },
      hunterStatus: {
        currentState: hunterStatus.currentState,
        isManualControl: hunterStatus.isManualControl,
        manualMoveDirection: manualControlStatus.manualMoveDirection,
        isRunning: manualControlStatus.isRunning,
        currentSpeed: manualControlStatus.currentSpeed,
        weaponVisible: hunterStatus.weaponVisible,
        isTargeting: hunterStatus.isTargeting
      }
    };
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onBattleStart?: () => void;
    onBattleEnd?: (winner: 'bears' | 'hunters' | 'draw', stats: any) => void;
    onParticipantDeath?: (participant: CombatParticipant, killer?: CombatParticipant) => void;
    onMeatDrop?: (meat: MeatDrop, deadBear: CombatParticipant) => void;
    onDamageDealt?: (attacker: CombatParticipant, target: CombatParticipant, damage: number) => void;
    onMeatCollected?: (hunter: CombatParticipant, meat: MeatDrop) => void;
  }): void {
    this.onBattleStart = callbacks.onBattleStart;
    this.onBattleEnd = callbacks.onBattleEnd;
    this.onParticipantDeath = callbacks.onParticipantDeath;
    this.onMeatDrop = callbacks.onMeatDrop;
    this.onDamageDealt = callbacks.onDamageDealt;
    this.onMeatCollected = callbacks.onMeatCollected;
  }

  /**
   * 检查猎人周围的肉块收集
   */
  private checkMeatCollection(hunter: CombatParticipant): void {
    const hunterPosition = hunter.position;
    const meatsToCollect: string[] = [];
    
    // 检查所有肉块是否在收集范围内
    for (const [meatId, meatDrop] of this.meatDrops) {
      const meatPosition = meatDrop.position;
      const distance = hunterPosition.distanceTo(meatPosition);
      
      if (distance <= this.collectionRadius) {
        meatsToCollect.push(meatId);
      }
    }
    
    // 收集范围内的肉块
    for (const meatId of meatsToCollect) {
      this.collectMeatToBackpack(hunter, meatId);
    }
  }

  /**
   * 收集肉块到猎人背包并堆叠到后背
   */
  private async collectMeatToBackpack(hunter: CombatParticipant, meatId: string): Promise<void> {
    const meatDrop = this.meatDrops.get(meatId);
    if (!meatDrop) return;
    
    // 从地面移除肉块
    this.scene.remove(meatDrop.model.getModel()!);
    this.meatDrops.delete(meatId);
    
    // 获取或创建猎人的背包肉块数组
    if (!this.backpackMeat.has(hunter.id)) {
      this.backpackMeat.set(hunter.id, []);
    }
    const hunterMeat = this.backpackMeat.get(hunter.id)!;
    
    // 创建新的肉块实例用于背包堆叠
    const backpackMeat = new MeatModel(this.scene, {
      name: `Backpack_${meatDrop.id}`,
      position: hunter.position.clone(),
      scale: 0.6 // 调大背包肉块，让它更明显
    });
    
    try {
      await backpackMeat.initialize();
      
      // 计算堆叠位置（在猎人后背）
      const stackPosition = this.calculateBackpackPosition(hunter, hunterMeat.length);
      
      // 执行肉块飞向后背的动画
      await this.animateMeatToBackpack(backpackMeat, stackPosition);
      
      // 将肉块添加到背包
      const backpackMeatDrop: MeatDrop = {
        model: backpackMeat,
        id: `backpack_${meatDrop.id}`,
        position: stackPosition,
        dropTime: Date.now()
      };
      
      hunterMeat.push(backpackMeatDrop);
      
      console.log(`🎒 ${hunter.id} 收集了肉块，背包中现有 ${hunterMeat.length} 块肉`);
      
      if (this.onMeatCollected) {
        this.onMeatCollected(hunter, backpackMeatDrop);
      }
      
    } catch (error) {
      console.error('收集肉块到背包失败:', error);
    }
  }

  /**
   * 计算肉块在猎人后背的堆叠位置
   */
  private calculateBackpackPosition(hunter: CombatParticipant, stackIndex: number): THREE.Vector3 {
    const hunterModel = hunter.model.getModel();
    if (!hunterModel) return hunter.position.clone();
    
    // 计算猎人后方向量
    const hunterRotation = hunterModel.rotation.y;
    const backwardX = -Math.sin(hunterRotation) * 1.2; // 后方1.2单位（增加距离）
    const backwardZ = -Math.cos(hunterRotation) * 1.2;
    
    // 基础后背位置
    const basePosition = new THREE.Vector3(
      hunterModel.position.x + backwardX,
      hunterModel.position.y + 1.2, // 后背高度
      hunterModel.position.z + backwardZ
    );
    
    // 垂直堆叠，每层高度0.25（适应更大的肉块）
    const stackHeight = stackIndex * 0.25;
    basePosition.y += stackHeight;
    
    return basePosition;
  }

  /**
   * 肉块飞向后背的动画
   */
  private animateMeatToBackpack(meat: MeatModel, targetPosition: THREE.Vector3): Promise<void> {
    return new Promise((resolve) => {
      const model = meat.getModel();
      if (!model) {
        resolve();
        return;
      }

      const startPos = model.position.clone();
      const duration = 0.6; // 动画持续时间
      
      let startTime: number | null = null;
      
      const animate = (currentTime: number) => {
        if (startTime === null) {
          startTime = currentTime;
        }
        
        const elapsed = (currentTime - startTime) / 1000;
        const progress = Math.min(elapsed / duration, 1);
        
        if (progress < 1) {
          // 水平位置：线性插值
          const currentX = startPos.x + (targetPosition.x - startPos.x) * progress;
          const currentZ = startPos.z + (targetPosition.z - startPos.z) * progress;
          
          // 垂直位置：抛物线轨迹
          const peakHeight = Math.max(startPos.y, targetPosition.y) + 1.0;
          const baseY = startPos.y + (targetPosition.y - startPos.y) * progress;
          const parabolicY = baseY + 4 * 1.0 * progress * (1 - progress);
          
          model.position.set(currentX, parabolicY, currentZ);
          
          // 旋转效果
          model.rotation.y += 0.15;
          
          requestAnimationFrame(animate);
        } else {
          // 动画完成
          model.position.copy(targetPosition);
          model.rotation.set(0, 0, 0); // 重置旋转
          resolve();
        }
      };
      
      requestAnimationFrame(animate);
    });
  }

  /**
   * 更新猎人背包中肉块的位置（跟随猎人移动）
   */
  private updateBackpackMeatPositions(hunter: CombatParticipant): void {
    const hunterMeat = this.backpackMeat.get(hunter.id);
    if (!hunterMeat || hunterMeat.length === 0) return;
    
    // 更新每块肉的位置
    hunterMeat.forEach((meat, index) => {
      const newPosition = this.calculateBackpackPosition(hunter, index);
      meat.model.setPosition(newPosition);
      meat.position.copy(newPosition);
    });
  }

  /**
   * 收集肉块（保留原有的手动收集方法）
   */
  collectMeat(meatId: string): boolean {
    const meatDrop = this.meatDrops.get(meatId);
    if (!meatDrop) return false;
    
    // 移除肉块
    meatDrop.model.dispose();
    this.meatDrops.delete(meatId);
    
    console.log(`🥩 肉块 ${meatId} 被收集`);
    return true;
  }

  /**
   * 获取猎人背包中的肉块数量
   */
  getHunterMeatCount(hunterId: string): number {
    const hunterMeat = this.backpackMeat.get(hunterId);
    return hunterMeat ? hunterMeat.length : 0;
  }

  /**
   * 获取猎人背包中的所有肉块
   */
  getHunterMeat(hunterId: string): MeatDrop[] {
    return this.backpackMeat.get(hunterId) || [];
  }

  /**
   * 重置战斗
   */
  async resetBattle(): Promise<void> {
    console.log('重置战斗...');
    this.clearCombat();
    await this.initializeCombat();
  }

  /**
   * 清理战斗
   */
  private clearCombat(): void {
    this.isBattleActive = false;
    
    // 清理参与者
    for (const [id, participant] of this.participants) {
      participant.model.dispose();
    }
    this.participants.clear();
    
    // 清理肉块
    for (const [id, meatDrop] of this.meatDrops) {
      meatDrop.model.dispose();
    }
    this.meatDrops.clear();
    
    // 清理背包肉块
    for (const [hunterId, hunterMeat] of this.backpackMeat) {
      for (const meat of hunterMeat) {
        meat.model.dispose();
      }
    }
    this.backpackMeat.clear();
    
    // 重置统计
    this.combatStats = {
      bearsKilled: 0,
      huntersKilled: 0,
      meatDropped: 0,
      battleStartTime: 0,
      battleEndTime: 0
    };
    
    this.battleTimer = 0;
  }

  /**
   * 清理所有资源
   */
  dispose(): void {
    this.clearCombat();
    console.log('熊猎人战斗系统已清理');
  }
} 