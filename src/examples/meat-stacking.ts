import * as THREE from 'three';
import { MeatModel } from '../entities/MeatModel.js';

export interface StackingConfig {
  layers: number;           // 堆叠层数
  itemsPerLayer: number;   // 每层肉块数量
  layerHeight: number;     // 层间距
  spreadRadius: number;    // 散布半径
  randomOffset: boolean;   // 是否添加随机偏移
  animateStacking: boolean; // 是否播放堆叠动画
  stackingSpeed: number;   // 堆叠速度
}

export interface MeatInstance {
  model: MeatModel;
  targetPosition: THREE.Vector3;
  currentPosition: THREE.Vector3;
  targetRotation: THREE.Euler;
  isAnimating: boolean;
  animationDelay: number;
  layer: number;
  index: number;
}

export class MeatStackingDemo {
  private scene: THREE.Scene;
  private config: StackingConfig;
  private meatInstances: MeatInstance[] = [];
  private isStacking = false;
  private animationClock = new THREE.Clock();
  
  // 事件回调
  private onStackingComplete?: () => void;
  private onLayerComplete?: (layer: number) => void;
  private onMeatPlaced?: (instance: MeatInstance) => void;

  constructor(scene: THREE.Scene, config?: Partial<StackingConfig>) {
    this.scene = scene;
    
    // 默认配置 - 单列完全紧密堆叠效果
    this.config = {
      layers: 12,
      itemsPerLayer: 1,
      layerHeight: 0.08,  // 极小的间距，让肉块完全紧贴
      spreadRadius: 1.0,
      randomOffset: false, // 默认关闭随机偏移
      animateStacking: true,
      stackingSpeed: 1.2, // 更慢的速度，让抛物线轨迹更明显
      ...config
    };
  }

  /**
   * 初始化肉块堆叠
   */
  async initializeStacking(): Promise<void> {
    console.log('开始初始化肉块堆叠...');
    
    // 清理现有实例
    this.clearInstances();
    
    // 创建所有肉块实例
    await this.createMeatInstances();
    
    // 计算堆叠位置
    this.calculateStackingPositions();
    
    // 开始堆叠动画
    if (this.config.animateStacking) {
      this.startStackingAnimation();
    } else {
      this.placeMeatInstantly();
    }
    
    console.log(`肉块堆叠初始化完成，总共 ${this.meatInstances.length} 个肉块`);
  }

  /**
   * 创建肉块实例
   */
  private async createMeatInstances(): Promise<void> {
    const totalInstances = this.config.layers * this.config.itemsPerLayer;
    const loadPromises: Promise<void>[] = [];
    
    for (let layer = 0; layer < this.config.layers; layer++) {
      for (let index = 0; index < this.config.itemsPerLayer; index++) {
        const instanceIndex = layer * this.config.itemsPerLayer + index;
        
                 // 创建肉块模型
         const meatModel = new MeatModel(this.scene, {
           name: `Meat_L${layer}_I${index}`,
           scale: 0.1, // 固定缩放，保持所有肉块大小一致
           position: new THREE.Vector3(3, 0, 0) // 初始位置在地面旁边，距离更远
         });
        
        // 设置事件回调
        meatModel.setEventCallbacks({
          onLoadComplete: () => {
            console.log(`肉块 ${instanceIndex + 1}/${totalInstances} 加载完成`);
          }
        });
        
                 // 创建实例数据
         const instance: MeatInstance = {
           model: meatModel,
           targetPosition: new THREE.Vector3(),
           currentPosition: new THREE.Vector3(3, 0, 0), // 初始在地面旁边，距离更远
           targetRotation: new THREE.Euler(),
           isAnimating: false,
           animationDelay: instanceIndex * 0.5, // 更大的延迟，让抛物线动画更明显
           layer: layer,
           index: index
         };
        
        this.meatInstances.push(instance);
        
        // 异步加载模型
        loadPromises.push(meatModel.initialize());
      }
    }
    
    // 等待所有模型加载完成
    await Promise.all(loadPromises);
    console.log('所有肉块模型加载完成');
  }

  /**
   * 计算堆叠位置
   */
  private calculateStackingPositions(): void {
    for (const instance of this.meatInstances) {
      const { layer, index } = instance;
      
      // 紧密垂直堆叠：每个肉块紧贴着下面一个
      const y = layer * this.config.layerHeight;
      
      // X轴位置：根据索引在一排中排列
      const spacing = this.config.spreadRadius * 0.5; // 肉块之间的间距
      const totalWidth = (this.config.itemsPerLayer - 1) * spacing;
      const startX = -totalWidth / 2; // 居中排列
      let x = startX + index * spacing;
      
      // Z轴固定在中心
      let z = 0;
      
      // 不添加任何随机偏移，保持完全对齐
      // if (this.config.randomOffset) 条件被移除，确保完全一致
      
      // 设置目标位置
      instance.targetPosition.set(x, y, z);
      
      // 设置完全一致的旋转角度 - 所有肉块使用相同的旋转
      instance.targetRotation.set(
        0,    // X轴旋转固定为0
        0,    // Y轴旋转固定为0  
        0     // Z轴旋转固定为0
      );
    }
  }

  /**
   * 开始堆叠动画
   */
  private startStackingAnimation(): void {
    this.isStacking = true;
    this.animationClock.start();
    
    console.log('开始肉块堆叠动画...');
    
    // 启动动画循环
    this.animateStacking();
  }

  /**
   * 动画循环
   */
  private animateStacking(): void {
    if (!this.isStacking) return;
    
    const elapsedTime = this.animationClock.getElapsedTime();
    let allAnimationsComplete = true;
    
    for (const instance of this.meatInstances) {
      // 检查是否到了开始动画的时间
      if (elapsedTime < instance.animationDelay) {
        allAnimationsComplete = false;
        continue;
      }
      
      // 如果还在动画中
      if (instance.isAnimating || !this.isInstanceAtTarget(instance)) {
        instance.isAnimating = true;
        allAnimationsComplete = false;
        
        // 计算动画进度
        const animationTime = elapsedTime - instance.animationDelay;
        const animationDuration = 1.0 / this.config.stackingSpeed;
        const progress = Math.min(animationTime / animationDuration, 1.0);
        
        // 使用缓动函数
        const easedProgress = this.easeOutQuad(progress);
        
        // 计算起始位置：在地面旁边
        const startPosition = new THREE.Vector3(
          instance.targetPosition.x + 3, // 在目标位置旁边3单位，距离更远
          0, // 地面高度
          instance.targetPosition.z
        );
        
        // 计算抛物线轨迹
        const currentPosition = this.calculateParabolicPath(
          startPosition,
          instance.targetPosition,
          easedProgress
        );
        
        instance.currentPosition.copy(currentPosition);
        
        // 设置模型位置
        instance.model.setPosition(instance.currentPosition);
        
        // 设置模型旋转 (保持固定旋转)
        instance.model.setRotation(instance.targetRotation);
        
        // 检查动画是否完成
        if (progress >= 1.0) {
          instance.isAnimating = false;
          
          // 触发单个肉块放置完成事件
          if (this.onMeatPlaced) {
            this.onMeatPlaced(instance);
          }
          
          // 检查层是否完成
          this.checkLayerCompletion(instance.layer);
        }
      }
    }
    
    // 如果所有动画完成
    if (allAnimationsComplete) {
      this.isStacking = false;
      console.log('肉块堆叠动画完成！');
      
      if (this.onStackingComplete) {
        this.onStackingComplete();
      }
    } else {
      // 继续动画
      requestAnimationFrame(() => this.animateStacking());
    }
  }

  /**
   * 检查实例是否到达目标位置
   */
  private isInstanceAtTarget(instance: MeatInstance): boolean {
    const distance = instance.currentPosition.distanceTo(instance.targetPosition);
    return distance < 0.01;
  }

  /**
   * 检查层完成情况
   */
  private checkLayerCompletion(layer: number): void {
    const layerInstances = this.meatInstances.filter(inst => inst.layer === layer);
    const completedInstances = layerInstances.filter(inst => !inst.isAnimating);
    
    if (completedInstances.length === layerInstances.length) {
      console.log(`第 ${layer + 1} 层堆叠完成`);
      
      if (this.onLayerComplete) {
        this.onLayerComplete(layer);
      }
    }
  }

  /**
   * 缓动函数 - 弹跳效果
   */
  private easeOutBounce(t: number): number {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  }

  /**
   * 缓动函数 - 二次方缓出
   */
  private easeOutQuad(t: number): number {
    return 1 - (1 - t) * (1 - t);
  }

  /**
   * 计算抛物线路径
   * @param start 起始位置
   * @param end 结束位置
   * @param progress 进度 (0-1)
   * @returns 当前抛物线位置
   */
  private calculateParabolicPath(start: THREE.Vector3, end: THREE.Vector3, progress: number): THREE.Vector3 {
    // 水平插值
    const x = start.x + (end.x - start.x) * progress;
    const z = start.z + (end.z - start.z) * progress;
    
    // 抛物线高度计算
    // 在中间点达到最高，创造自然的抛物线轨迹
    const distance = Math.sqrt((end.x - start.x) ** 2 + (end.z - start.z) ** 2); // 水平距离
    const heightDifference = Math.abs(end.y - start.y);
    const maxHeight = Math.max(1.0, distance * 0.4 + heightDifference * 0.3); // 根据距离和高度差调整
    
    // 抛物线公式：h = 4 * maxHeight * progress * (1 - progress)
    const parabolicHeight = 4 * maxHeight * progress * (1 - progress);
    
    // 基础Y位置插值
    const baseY = start.y + (end.y - start.y) * progress;
    
    // 最终Y位置 = 基础位置 + 抛物线高度
    const y = baseY + parabolicHeight;
    
    return new THREE.Vector3(x, y, z);
  }

  /**
   * 立即放置所有肉块（无动画）
   */
  private placeMeatInstantly(): void {
    for (const instance of this.meatInstances) {
      instance.model.setPosition(instance.targetPosition);
      instance.model.setRotation(instance.targetRotation);
      instance.currentPosition.copy(instance.targetPosition);
      instance.isAnimating = false;
    }
    
    console.log('肉块堆叠立即完成！');
    
    if (this.onStackingComplete) {
      this.onStackingComplete();
    }
  }

  /**
   * 重新开始堆叠
   */
  async restartStacking(): Promise<void> {
    console.log('重新开始堆叠...');
    
    // 重置所有肉块到初始位置（地面旁边）
    for (const instance of this.meatInstances) {
      instance.currentPosition.set(3, 0, 0);
      instance.model.setPosition(instance.currentPosition);
      instance.isAnimating = false;
    }
    
    // 重新计算位置
    this.calculateStackingPositions();
    
    // 重新开始动画
    if (this.config.animateStacking) {
      this.animationClock.start();
      this.startStackingAnimation();
    } else {
      this.placeMeatInstantly();
    }
  }

  /**
   * 清理所有实例
   */
  private clearInstances(): void {
    for (const instance of this.meatInstances) {
      instance.model.dispose();
    }
    this.meatInstances = [];
  }

  /**
   * 获取堆叠状态
   */
  getStackingStatus(): {
    total: number;
    completed: number;
    isStacking: boolean;
    progress: number;
  } {
    const total = this.meatInstances.length;
    const completed = this.meatInstances.filter(inst => !inst.isAnimating).length;
    const progress = total > 0 ? completed / total : 0;
    
    return {
      total,
      completed,
      isStacking: this.isStacking,
      progress
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<StackingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onStackingComplete?: () => void;
    onLayerComplete?: (layer: number) => void;
    onMeatPlaced?: (instance: MeatInstance) => void;
  }): void {
    this.onStackingComplete = callbacks.onStackingComplete;
    this.onLayerComplete = callbacks.onLayerComplete;
    this.onMeatPlaced = callbacks.onMeatPlaced;
  }

  /**
   * 获取所有肉块实例
   */
  getMeatInstances(): MeatInstance[] {
    return this.meatInstances;
  }

  /**
   * 获取配置
   */
  getConfig(): StackingConfig {
    return { ...this.config };
  }

  /**
   * 暂停/恢复堆叠动画
   */
  toggleStackingAnimation(): void {
    this.isStacking = !this.isStacking;
    if (this.isStacking) {
      this.animateStacking();
    }
  }

  /**
   * 添加单个肉块到指定位置
   */
  async addMeatAtPosition(position: THREE.Vector3, rotation?: THREE.Euler): Promise<MeatInstance> {
    const meatModel = new MeatModel(this.scene, {
      name: `Meat_Custom_${Date.now()}`,
      scale: 0.1, // 固定缩放，保持一致
      position: new THREE.Vector3(3, 0, 0) // 在地面旁边，距离更远
    });
    
    await meatModel.initialize();
    
    const instance: MeatInstance = {
      model: meatModel,
      targetPosition: position.clone(),
      currentPosition: new THREE.Vector3(3, 0, 0), // 在地面旁边，距离更远
      targetRotation: rotation || new THREE.Euler(0, 0, 0), // 固定旋转角度
      isAnimating: false,
      animationDelay: 0,
      layer: -1, // 自定义肉块
      index: -1
    };
    
    this.meatInstances.push(instance);
    
    // 立即移动到目标位置
    instance.model.setPosition(instance.targetPosition);
    instance.model.setRotation(instance.targetRotation);
    instance.currentPosition.copy(instance.targetPosition);
    
    return instance;
  }

  /**
   * 移除指定的肉块实例
   */
  removeMeatInstance(instance: MeatInstance): boolean {
    const index = this.meatInstances.indexOf(instance);
    if (index !== -1) {
      instance.model.dispose();
      this.meatInstances.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 清理所有资源
   */
  dispose(): void {
    this.isStacking = false;
    this.clearInstances();
    console.log('肉块堆叠系统已清理');
  }
} 