import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface TreeConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export enum TreeState {
  STATIC = 'static',
  SWAYING = 'swaying',
  WINDY = 'windy'
}

export class TreeModel {
  private config: TreeConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;
  private clock: THREE.Clock;

  // 树的状态
  private currentTreeState: TreeState = TreeState.STATIC;
  private swayIntensity = 0.5;
  private windStrength = 1.0;
  private swaySpeed = 1.0;

  // 默认树纹理配置 (Tree_01只有FBX文件，没有单独的纹理文件)
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    // 如果后续添加纹理，可以在这里配置
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onStateChange?: (state: TreeState) => void;

  constructor(scene: THREE.Scene, config?: Partial<TreeConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'Tree',
      modelPath: '/src/models/Tree_01/SS_Tree_01.fbx',
      textures: TreeModel.DEFAULT_TEXTURES,
      scale: 0.02,  // 适合的树木尺寸
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.clock = new THREE.Clock();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 创建标准树实例的快捷方法
   */
  static create(scene: THREE.Scene, name = 'Tree'): TreeModel {
    return new TreeModel(scene, { name });
  }

  /**
   * 初始化并加载树模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('开始加载树模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();
      
      // 预处理模型
      this.loader.preprocessModel(this.model);
      
      // 应用材质优化
      this.applyTreeMaterials();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('树模型加载完成');
    } catch (error) {
      console.error('树模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型的基本属性
   */
  private setupModelProperties(): void {
    this.model.name = this.config.name;
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
  }

  /**
   * 应用树的材质优化
   */
  private applyTreeMaterials(): void {
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        
        materials.forEach((material) => {
          this.optimizeTreeMaterial(material);
        });
      }
    });
  }

  /**
   * 优化树的材质属性
   */
  private optimizeTreeMaterial(material: THREE.Material): void {
    if (material instanceof THREE.MeshStandardMaterial) {
      // 树木的自然材质属性
      material.color = new THREE.Color(0x4a5d23);  // 自然的绿褐色
      material.roughness = 0.8;                    // 较高的粗糙度
      material.metalness = 0.0;                    // 非金属
      material.transparent = false;
      material.opacity = 1.0;
      
      // 启用阴影
      material.shadowSide = THREE.DoubleSide;
      
      console.log('Applied tree material properties to MeshStandardMaterial');
    } else if (material instanceof THREE.MeshPhongMaterial) {
      material.color = new THREE.Color(0x4a5d23);
      material.shininess = 10; // 低光泽度
      material.transparent = false;
      material.opacity = 1.0;
      
      console.log('Applied tree material properties to MeshPhongMaterial');
    } else if (material instanceof THREE.MeshLambertMaterial || material instanceof THREE.MeshBasicMaterial) {
      material.color = new THREE.Color(0x4a5d23);
      material.transparent = false;
      material.opacity = 1.0;
      
      console.log('Applied basic tree material properties');
    }
    
    // 标记材质需要更新
    material.needsUpdate = true;
  }

  /**
   * 设置树的状态
   */
  setState(state: TreeState): void {
    if (this.currentTreeState === state) return;
    
    this.currentTreeState = state;
    console.log(`Tree state changed to: ${state}`);
    
    if (this.onStateChange) {
      this.onStateChange(state);
    }
  }

  /**
   * 更新树模型（每帧调用）
   */
  update(): void {
    if (!this.isLoaded || !this.model) return;

    const deltaTime = this.clock.getDelta();
    const time = this.clock.getElapsedTime();

    // 根据状态更新树的行为
    switch (this.currentTreeState) {
      case TreeState.SWAYING:
        this.updateSwaying(time, deltaTime);
        break;
      case TreeState.WINDY:
        this.updateWindyMotion(time, deltaTime);
        break;
      case TreeState.STATIC:
      default:
        // 静态状态，重置旋转
        this.model.rotation.x = this.config.rotation.x;
        this.model.rotation.z = this.config.rotation.z;
        break;
    }
  }

  /**
   * 更新轻微摆动
   */
  private updateSwaying(time: number, deltaTime: number): void {
    if (!this.model) return;

    // 轻微的摆动效果
    const swayX = Math.sin(time * this.swaySpeed) * this.swayIntensity * 0.01;
    const swayZ = Math.cos(time * this.swaySpeed * 0.7) * this.swayIntensity * 0.008;

    this.model.rotation.x = this.config.rotation.x + swayX;
    this.model.rotation.z = this.config.rotation.z + swayZ;
  }

  /**
   * 更新强风摆动
   */
  private updateWindyMotion(time: number, deltaTime: number): void {
    if (!this.model) return;

    // 强风摆动效果
    const windX = Math.sin(time * this.swaySpeed * 1.5) * this.windStrength * 0.03;
    const windZ = Math.cos(time * this.swaySpeed * 1.2) * this.windStrength * 0.025;
    
    // 添加一些随机性
    const randomFactor = Math.sin(time * 3.7) * 0.5 + 0.5;
    
    this.model.rotation.x = this.config.rotation.x + windX * randomFactor;
    this.model.rotation.z = this.config.rotation.z + windZ * randomFactor;
  }

  /**
   * 设置摆动参数
   */
  setSwayParameters(intensity: number, speed: number, windStrength: number): void {
    this.swayIntensity = Math.max(0, Math.min(2, intensity));
    this.swaySpeed = Math.max(0.1, Math.min(5, speed));
    this.windStrength = Math.max(0, Math.min(3, windStrength));
  }

  /**
   * 设置模型位置
   */
  setPosition(position: THREE.Vector3): void {
    this.config.position.copy(position);
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置模型旋转
   */
  setRotation(rotation: THREE.Euler): void {
    this.config.rotation.copy(rotation);
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 设置模型缩放
   */
  setScale(scale: number): void {
    this.config.scale = scale;
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 获取模型位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : this.config.position.clone();
  }

  /**
   * 获取模型旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : this.config.rotation.clone();
  }

  /**
   * 获取模型缩放
   */
  getScale(): number {
    return this.model ? this.model.scale.x : this.config.scale;
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): TreeState {
    return this.currentTreeState;
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onStateChange?: (state: TreeState) => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onStateChange = callbacks.onStateChange;
  }

  /**
   * 获取模型状态
   */
  getStatus(): {
    name: string;
    loaded: boolean;
    position: THREE.Vector3;
    rotation: THREE.Euler;
    scale: number;
    state: TreeState;
    swayIntensity: number;
    swaySpeed: number;
    windStrength: number;
  } {
    return {
      name: this.config.name,
      loaded: this.isLoaded,
      position: this.getPosition(),
      rotation: this.getRotation(),
      scale: this.getScale(),
      state: this.currentTreeState,
      swayIntensity: this.swayIntensity,
      swaySpeed: this.swaySpeed,
      windStrength: this.windStrength
    };
  }

  /**
   * 获取底层模型对象
   */
  getModel(): THREE.Group | null {
    return this.model || null;
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('Tree model not loaded yet');
      return;
    }

    console.log('=== Tree Model Materials Debug Info ===');
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        console.log(`Mesh: "${child.name}"`);
        const materials = Array.isArray(child.material) ? child.material : [child.material];

        materials.forEach((material, index) => {
          console.log(`  Material ${index}: {`);
          console.log(`    type: "${material.type}",`);
          console.log(`    name: "${material.name}",`);

          if ('color' in material) {
            console.log(`    color: "${material.color.getHexString()}"`);
          }
          if ('map' in material) {
            console.log(`    map: "${material.map ? 'Has texture' : 'No texture'}"`);
          }

          console.log(`  }`);
        });
      }
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      this.scene.remove(this.model);

      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    }

    console.log('Tree model disposed');
  }
}
