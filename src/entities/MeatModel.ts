import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface MeatConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export class MeatModel {
  private config: MeatConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 默认肉块纹理配置
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'meatDiffuse',
      path: '/src/models/Meat/SS_Meat_01.png',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onTextureLoaded?: () => void;

  constructor(scene: THREE.Scene, config?: Partial<MeatConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'MeatBlock',
      modelPath: '/src/models/Meat/SS_Meat_01.fbx',
      textures: MeatModel.DEFAULT_TEXTURES,
      scale: 0.4,  // 增加默认肉块大小
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('开始加载肉块模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();
      
      // 预处理模型
      this.loader.preprocessModel(this.model);
      
      // 加载并应用纹理
      await this.loadAndApplyTextures();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('肉块模型加载完成');
    } catch (error) {
      console.error('肉块模型加载失败:', error);
    }
  }

  /**
   * 设置模型的基本属性
   */
  private setupModelProperties(): void {
    this.model.name = this.config.name;
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
  }

  /**
   * 异步加载并应用纹理
   */
  private async loadAndApplyTextures(): Promise<void> {
    if (!this.config.textures || this.config.textures.length === 0) {
      console.log('No textures configured for Meat model');
      return;
    }

    // 创建所有纹理加载的Promise
    const texturePromises = this.config.textures.map((textureConfig) => {
      return this.loadTextureAsync(textureConfig);
    });

    try {
      // 等待所有纹理加载完成
      const loadedTextures = await Promise.all(texturePromises);
      
      // 应用所有纹理到模型
      loadedTextures.forEach((textureResult) => {
        if (textureResult.texture) {
          this.applyTextureToModel(textureResult.texture, textureResult.config);
          console.log(`Successfully applied texture: ${textureResult.config.name}`);
        }
      });

      if (this.onTextureLoaded) {
        this.onTextureLoaded();
      }
      
      console.log('All textures loaded and applied successfully');
    } catch (error) {
      console.error('Failed to load textures:', error);
    }
  }

  /**
   * 异步加载单个纹理
   */
  private loadTextureAsync(textureConfig: TextureFile): Promise<{texture: THREE.Texture | null, config: TextureFile}> {
    return new Promise((resolve) => {
      this.textureLoader.load(
        textureConfig.path,
        (loadedTexture) => {
          // 配置纹理属性
          this.configureTexture(loadedTexture, textureConfig);
          console.log(`Texture "${textureConfig.name}" loaded successfully`);
          resolve({ texture: loadedTexture, config: textureConfig });
        },
        undefined,
        (error) => {
          console.error(`Failed to load texture "${textureConfig.name}":`, error);
          resolve({ texture: null, config: textureConfig });
        }
      );
    });
  }

  /**
   * 配置纹理属性
   */
  private configureTexture(texture: THREE.Texture, config: TextureFile): void {
    // 设置颜色空间
    if (config.colorSpace !== undefined) {
      texture.colorSpace = config.colorSpace;
    }

    // 设置翻转
    if (config.flipY !== undefined) {
      texture.flipY = config.flipY;
    }

    // 设置包装模式
    if (config.wrapS !== undefined) {
      texture.wrapS = config.wrapS;
    }
    if (config.wrapT !== undefined) {
      texture.wrapT = config.wrapT;
    }

    // 设置重复
    if (config.repeat) {
      texture.repeat.set(config.repeat.x, config.repeat.y);
    }

    texture.needsUpdate = true;
  }

  /**
   * 将纹理应用到模型
   */
  private applyTextureToModel(texture: THREE.Texture, config: TextureFile): void {
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            this.applyTextureToMaterial(material, texture, config);
          });
        } else {
          this.applyTextureToMaterial(child.material, texture, config);
        }
      }
    });
  }

  /**
   * 将纹理应用到材质
   */
  private applyTextureToMaterial(material: THREE.Material, texture: THREE.Texture, config: TextureFile): void {
    // 检查材质类型并应用纹理
    if (material instanceof THREE.MeshStandardMaterial || 
        material instanceof THREE.MeshPhongMaterial ||
        material instanceof THREE.MeshLambertMaterial ||
        material instanceof THREE.MeshBasicMaterial) {
      
      // 根据配置的材质属性应用纹理
      switch (config.materialProperty) {
        case 'map':
          material.map = texture;
          // 根据Cocos配置设置材质属性
          this.applyCocosMaterialProperties(material);
          break;
        case 'normalMap':
          if ('normalMap' in material) {
            (material as any).normalMap = texture;
          }
          break;
        case 'roughnessMap':
          if ('roughnessMap' in material) {
            (material as any).roughnessMap = texture;
          }
          break;
        case 'metalnessMap':
          if ('metalnessMap' in material) {
            (material as any).metalnessMap = texture;
          }
          break;
        case 'aoMap':
          if ('aoMap' in material) {
            (material as any).aoMap = texture;
          }
          break;
        case 'emissiveMap':
          if ('emissiveMap' in material) {
            (material as any).emissiveMap = texture;
          }
          break;
      }
      
      // 标记材质需要更新
      material.needsUpdate = true;
    } else {
      console.warn(`Material type ${material.type} not supported for texture application`);
    }
  }

  /**
   * 应用基于Cocos配置的材质属性
   */
  private applyCocosMaterialProperties(material: THREE.Material): void {
    // 只对 MeshStandardMaterial 应用 PBR 属性
    if (material instanceof THREE.MeshStandardMaterial) {
      // 基于Cocos截图中的配置
      material.color = new THREE.Color(0xFFDDDD);  // Albedo color: FFDDDD (淡粉色)
      material.roughness = 0.5;                    // Roughness: 0.5
      material.metalness = 0.0;                    // Metallic: 0
      material.transparent = false;
      material.opacity = 1.0;
      
      // 其他 PBR 属性
      material.emissive = new THREE.Color(0x000000);
      material.emissiveIntensity = 0.0;
      
      console.log('Applied Cocos-based PBR material properties:', {
        color: material.color.getHexString(),
        roughness: material.roughness,
        metalness: material.metalness,
        transparent: material.transparent,
        opacity: material.opacity
      });
    } else if (material instanceof THREE.MeshPhongMaterial) {
      // 对于 Phong 材质，应用相应的属性
      material.color = new THREE.Color(0xFFDDDD);
      material.shininess = 50; // 相当于低粗糙度
      material.transparent = false;
      material.opacity = 1.0;
      
      console.log('Applied Cocos-based Phong material properties');
    } else if (material instanceof THREE.MeshLambertMaterial || material instanceof THREE.MeshBasicMaterial) {
      // 其他有 color 属性的材质类型
      material.color = new THREE.Color(0xFFDDDD);
      material.transparent = false;
      material.opacity = 1.0;
      
      console.log('Applied basic material properties');
    } else {
      console.log('Material type does not support color property:', material.type);
    }
  }

  /**
   * 设置模型位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置模型旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 设置模型缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 获取模型位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : new THREE.Vector3();
  }

  /**
   * 获取模型旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : new THREE.Euler();
  }

  /**
   * 获取模型缩放
   */
  getScale(): number {
    return this.model ? this.model.scale.x : 1;
  }

  /**
   * 重新应用纹理（用于实时更新）
   */
  async reapplyTexture(): Promise<void> {
    if (this.model) {
      await this.loadAndApplyTextures();
    }
  }

  /**
   * 更新纹理配置
   */
  updateTextureConfig(textures: TextureFile[]): void {
    this.config.textures = textures;
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onTextureLoaded?: () => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onTextureLoaded = callbacks.onTextureLoaded;
  }

  /**
   * 获取模型状态
   */
  getStatus(): {
    name: string;
    loaded: boolean;
    position: THREE.Vector3;
    rotation: THREE.Euler;
    scale: number;
    roughness: number;
    metalness: number;
  } {
    const materialProps = this.getMaterialProperties();
    return {
      name: this.config.name,
      loaded: this.isLoaded,
      position: this.getPosition(),
      rotation: this.getRotation(),
      scale: this.getScale(),
      roughness: materialProps.roughness,
      metalness: materialProps.metalness
    };
  }

  /**
   * 获取材质属性
   */
  private getMaterialProperties(): { roughness: number; metalness: number } {
    if (!this.model) return { roughness: 0.5, metalness: 0 };

    // 查找第一个 MeshStandardMaterial
    let roughness = 0.5;
    let metalness = 0;
    
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        for (const material of materials) {
          if (material instanceof THREE.MeshStandardMaterial) {
            roughness = material.roughness;
            metalness = material.metalness;
            return; // 找到第一个就退出
          }
        }
      }
    });

    return { roughness, metalness };
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('Model not loaded yet');
      return;
    }

    console.log('=== Meat Model Materials Debug Info ===');
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        console.log(`Mesh: "${child.name}"`);
        if (Array.isArray(child.material)) {
          child.material.forEach((material, index) => {
            console.log(`  Material[${index}]:`, {
              type: material.type,
              name: material.name,
              map: material.map ? 'Has texture' : 'No texture',
              color: material.color ? material.color.getHexString() : 'No color'
            });
          });
        } else {
          console.log(`  Material:`, {
            type: child.material.type,
            name: child.material.name,
            map: child.material.map ? 'Has texture' : 'No texture',
            color: child.material.color ? child.material.color.getHexString() : 'No color'
          });
        }
      }
    });
  }

  /**
   * 获取Three.js模型对象
   */
  getModel(): THREE.Group | null {
    return this.model || null;
  }

  /**
   * 检查模型是否已加载
   */
  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 设置材质粗糙度 (仅限 MeshStandardMaterial)
   */
  setRoughness(roughness: number): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            material.roughness = Math.max(0, Math.min(1, roughness));
            material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 设置材质金属度 (仅限 MeshStandardMaterial)
   */
  setMetalness(metalness: number): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            material.metalness = Math.max(0, Math.min(1, metalness));
            material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 重置材质属性为Cocos配置的默认值
   */
  resetMaterialProperties(): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          this.applyCocosMaterialProperties(material);
        });
      }
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat.map) mat.map.dispose();
                mat.dispose();
              });
            } else {
              if (child.material.map) child.material.map.dispose();
              child.material.dispose();
            }
          }
        }
      });

      // 从场景中移除
      this.scene.remove(this.model);
    }

    this.isLoaded = false;
    console.log('Meat model resources disposed');
  }
} 