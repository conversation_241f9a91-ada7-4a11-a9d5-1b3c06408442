import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: string;
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface GroundMainConfig {
  name: string;
  modelPath: string;
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
  materialProperties: {
    mainColor: THREE.Color;
    roughness: number;
    metalness: number;
  };
}

export interface GroundMainEventCallbacks {
  onLoadComplete?: () => void;
  onMaterialApplied?: () => void;
}

/**
 * GroundMainModel - 主地面模型系统
 * 基于SS_Ground_01.fbx中的单一主地面网格，应用M_BaseGroundColor_05材质配置
 * 使用SS_Ground_01_3_D贴图，简化的单网格处理系统
 */
export class GroundMainModel {
  private config: GroundMainConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private mainMesh!: THREE.Mesh;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 事件回调
  private onLoadComplete?: () => void;
  private onMaterialApplied?: () => void;

  // 基于M_BaseGroundColor_05材质文件和SS_Ground_01_3_D贴图的配置
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'groundMainDiffuse',
      path: '/src/models/Ground_Main/SS_Ground_01_3_D.png',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,  // 根据meta文件配置
      wrapS: THREE.RepeatWrapping,  // 根据meta文件的repeat设置
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  constructor(scene: THREE.Scene, config?: Partial<GroundMainConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置 - 基于M_BaseGroundColor_05材质配置
    this.config = {
      name: 'GroundMain',
      modelPath: '/src/models/Ground_Main/SS_Ground_01.fbx',
      scale: 1.0,
      position: new THREE.Vector3(20, 0.01, -20),  // 稍微高一点，贴在默认地面上
      rotation: new THREE.Euler(0, 0, 0),
      materialProperties: {
        // 主地面材质：基于M_BaseGroundColor_05的默认配置
        mainColor: new THREE.Color(0xFFFFFF),  // 地面棕色
        roughness: 1.0,  // 直接来自M_BaseGroundColor_05.mtl文件
        metalness: 0.0   // 地面通常无金属度
      },
      ...config
    };

    this.loader = new ModelLoader();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: GroundMainEventCallbacks): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onMaterialApplied = callbacks.onMaterialApplied;
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 开始加载主地面模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();

      // 预处理模型
      this.loader.preprocessModel(this.model);

      // 找到主地面网格（使用第一个网格作为主地面）
      this.findMainGroundMesh();

      // 应用主地面材质属性和贴图
      await this.applyGroundMainMaterialProperties();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('✅ 主地面模型加载完成');
    } catch (error) {
      console.error('❌ 主地面模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型基础属性
   */
  private setupModelProperties(): void {
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
    this.model.scale.setScalar(this.config.scale);
    this.model.name = this.config.name;
  }

  /**
   * 找到主地面网格
   */
  private findMainGroundMesh(): void {
    let meshFound = false;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && !meshFound) {
        // 使用第一个找到的网格作为主地面网格
        this.mainMesh = child;
        meshFound = true;

        // 确保主网格可见
        child.visible = true;
        child.castShadow = true;
        child.receiveShadow = true;

        console.log(`🌍 找到主地面网格:`, {
          name: child.name || 'Unnamed',
          position: child.position,
          scale: child.scale,
          triangles: child.geometry.attributes.position?.count / 3 || 0
        });
      } else if (child instanceof THREE.Mesh && meshFound) {
        // 隐藏其他网格，只显示主地面
        child.visible = false;
      }
    });

    if (!meshFound) {
      throw new Error('❌ 未找到主地面网格');
    }

    console.log('✅ 主地面网格设置完成');
  }

  /**
   * 应用主地面材质属性和贴图
   */
  private async applyGroundMainMaterialProperties(): Promise<void> {
    if (!this.mainMesh) {
      console.warn('⚠️ 主地面网格未找到，跳过材质应用');
      return;
    }

    try {
      console.log('🎨 开始应用主地面材质...');

      // 加载贴图
      const textures = await this.loadTextures();

      // 创建PBR材质 - 基于M_BaseGroundColor_05配置
      const material = new THREE.MeshStandardMaterial({
        color: this.config.materialProperties.mainColor,
        roughness: this.config.materialProperties.roughness,
        metalness: this.config.materialProperties.metalness,
        side: THREE.FrontSide,
        transparent: false
      });

      // 应用贴图
      if (textures.groundMainDiffuse) {
        material.map = textures.groundMainDiffuse;
        console.log('✅ 主地面漫反射贴图已应用');
      }

      // 应用材质到主网格
      this.mainMesh.material = material;

      console.log('✅ 主地面材质应用完成:', {
        color: `#${this.config.materialProperties.mainColor.getHexString()}`,
        roughness: this.config.materialProperties.roughness,
        metalness: this.config.materialProperties.metalness,
        hasTexture: !!textures.groundMainDiffuse
      });

      if (this.onMaterialApplied) {
        this.onMaterialApplied();
      }

    } catch (error) {
      console.error('❌ 主地面材质应用失败:', error);
      throw error;
    }
  }

  /**
   * 加载贴图文件
   */
  private async loadTextures(): Promise<Record<string, THREE.Texture>> {
    const textures: Record<string, THREE.Texture> = {};

    for (const textureFile of GroundMainModel.DEFAULT_TEXTURES) {
      try {
        console.log(`🔄 加载贴图: ${textureFile.name} from ${textureFile.path}`);
        
        const texture = await new Promise<THREE.Texture>((resolve, reject) => {
          this.textureLoader.load(
            textureFile.path,
            (loadedTexture) => resolve(loadedTexture),
            undefined,
            (error) => reject(error)
          );
        });

        // 配置贴图属性
        if (textureFile.colorSpace) {
          texture.colorSpace = textureFile.colorSpace;
        }
        if (textureFile.flipY !== undefined) {
          texture.flipY = textureFile.flipY;
        }
        if (textureFile.wrapS) {
          texture.wrapS = textureFile.wrapS;
        }
        if (textureFile.wrapT) {
          texture.wrapT = textureFile.wrapT;
        }
        if (textureFile.repeat) {
          texture.repeat.set(textureFile.repeat.x, textureFile.repeat.y);
        }

        texture.needsUpdate = true;
        textures[textureFile.name] = texture;

        console.log(`✅ 贴图加载成功: ${textureFile.name}`);
      } catch (error) {
        console.error(`❌ 贴图加载失败: ${textureFile.name}`, error);
        // 继续加载其他贴图，不中断整个过程
      }
    }

    return textures;
  }

  // ==================== 公共控制方法 ====================

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
      this.config.position.copy(position);
    }
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
      this.config.rotation.copy(rotation);
    }
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
      this.config.scale = scale;
    }
  }

  /**
   * 更新材质颜色
   */
  updateColor(color: THREE.Color): void {
    this.config.materialProperties.mainColor = color.clone();
    
    if (this.mainMesh && this.mainMesh.material instanceof THREE.MeshStandardMaterial) {
      this.mainMesh.material.color = color.clone();
    }
  }

  /**
   * 更新材质粗糙度
   */
  updateRoughness(roughness: number): void {
    this.config.materialProperties.roughness = roughness;
    
    if (this.mainMesh && this.mainMesh.material instanceof THREE.MeshStandardMaterial) {
      this.mainMesh.material.roughness = roughness;
    }
  }

  /**
   * 更新材质金属度
   */
  updateMetalness(metalness: number): void {
    this.config.materialProperties.metalness = metalness;

    if (this.mainMesh && this.mainMesh.material instanceof THREE.MeshStandardMaterial) {
      this.mainMesh.material.metalness = metalness;
    }
  }

  /**
   * 获取模型对象
   */
  getModel(): THREE.Group | null {
    return this.isLoaded ? this.model : null;
  }

  /**
   * 获取主地面网格
   */
  getMainMesh(): THREE.Mesh | null {
    return this.isLoaded ? this.mainMesh : null;
  }

  /**
   * 获取配置信息
   */
  getConfig(): GroundMainConfig {
    return { ...this.config };
  }

  /**
   * 检查是否已加载
   */
  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 设置主地面可见性
   */
  setVisibility(visible: boolean): void {
    if (this.mainMesh) {
      this.mainMesh.visible = visible;
      console.log(`👁️ 主地面可见性设置为: ${visible}`);
    }
  }

  /**
   * 重置到默认状态
   */
  reset(): void {
    this.setPosition(new THREE.Vector3(0, 0.01, 0));  // 与默认配置保持一致
    this.setRotation(new THREE.Euler(0, 0, 0));
    this.setScale(1.0);
    this.setVisibility(true);

    // 重置材质属性
    const defaultColor = new THREE.Color(0x8B7355);
    this.updateColor(defaultColor);
    this.updateRoughness(1.0);
    this.updateMetalness(0.0);

    console.log('🔄 主地面已重置到默认状态');
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.mainMesh) {
      console.log('❌ 主地面网格未加载');
      return;
    }

    console.log('=== 🌍 GroundMain Material Debug ===');
    console.log(`主网格: ${this.mainMesh.name || 'Unnamed'}`);

    if (this.mainMesh.material instanceof THREE.MeshStandardMaterial) {
      const material = this.mainMesh.material;
      console.log('材质属性:', {
        color: `#${material.color.getHexString()}`,
        roughness: material.roughness,
        metalness: material.metalness,
        transparent: material.transparent,
        side: material.side === THREE.DoubleSide ? 'DoubleSide' : 'FrontSide',
        hasTexture: !!material.map,
        textureSize: material.map ? `${material.map.image?.width}x${material.map.image?.height}` : 'N/A'
      });

      if (material.map) {
        console.log('贴图信息:', {
          wrapS: material.map.wrapS === THREE.RepeatWrapping ? 'Repeat' : 'Clamp',
          wrapT: material.map.wrapT === THREE.RepeatWrapping ? 'Repeat' : 'Clamp',
          repeat: `${material.map.repeat.x}x${material.map.repeat.y}`,
          flipY: material.map.flipY,
          colorSpace: material.map.colorSpace
        });
      }
    }

    console.log('几何体信息:', {
      vertices: this.mainMesh.geometry.attributes.position?.count || 0,
      triangles: (this.mainMesh.geometry.attributes.position?.count || 0) / 3,
      hasUV: !!this.mainMesh.geometry.attributes.uv,
      hasNormals: !!this.mainMesh.geometry.attributes.normal
    });
  }

  /**
   * 获取模型统计信息
   */
  getModelStats(): {
    isLoaded: boolean;
    meshName: string;
    vertices: number;
    triangles: number;
    hasTexture: boolean;
    materialProperties: {
      color: string;
      roughness: number;
      metalness: number;
    };
  } {
    if (!this.isLoaded || !this.mainMesh) {
      return {
        isLoaded: false,
        meshName: '',
        vertices: 0,
        triangles: 0,
        hasTexture: false,
        materialProperties: {
          color: '#000000',
          roughness: 0,
          metalness: 0
        }
      };
    }

    const material = this.mainMesh.material as THREE.MeshStandardMaterial;
    const vertices = this.mainMesh.geometry.attributes.position?.count || 0;

    return {
      isLoaded: true,
      meshName: this.mainMesh.name || 'Unnamed',
      vertices,
      triangles: Math.floor(vertices / 3),
      hasTexture: !!material.map,
      materialProperties: {
        color: `#${material.color.getHexString()}`,
        roughness: material.roughness,
        metalness: material.metalness
      }
    };
  }

  /**
   * 销毁资源
   */
  dispose(): void {
    if (this.mainMesh) {
      // 清理几何体
      if (this.mainMesh.geometry) {
        this.mainMesh.geometry.dispose();
      }

      // 清理材质和贴图
      if (this.mainMesh.material instanceof THREE.MeshStandardMaterial) {
        const material = this.mainMesh.material;

        // 清理贴图
        if (material.map) {
          material.map.dispose();
        }

        // 清理材质
        material.dispose();
      }
    }

    if (this.model) {
      // 从场景中移除
      this.scene.remove(this.model);
      console.log('🗑️ GroundMainModel 资源已清理');
    }

    // 重置状态
    this.isLoaded = false;
  }
}
