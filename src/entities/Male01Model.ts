import * as THREE from 'three';
import { ModelLoader, AnimationFile } from '../loaders/FBXLoader.js';
import { AnimationController } from '../controllers/AnimationController.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface Male01Config {
  name: string;
  modelPath: string;
  weaponPath?: string;
  weaponTextures?: TextureFile[];
  animations: AnimationFile[];
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export enum Male01State {
  IDLE = 'idle',
  WALK = 'walk',
  RUN = 'run',
  SHOOTING = 'shooting',
  STANDING_SHOOT = 'standing_shoot',
  READING = 'reading',
  COLD = 'cold',
  DEATH = 'death'
}

export class Male01Model {
  private config: Male01Config;
  private loader: ModelLoader;
  private animationController!: AnimationController;
  private model!: THREE.Group;
  private weaponModel?: THREE.Group;
  private mixer!: THREE.AnimationMixer;
  private clock: THREE.Clock;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 默认Male01动画配置
  private static readonly DEFAULT_ANIMATIONS: AnimationFile[] = [
    {
      name: Male01State.IDLE,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 0
    },
    {
      name: Male01State.WALK,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 1
    },
    {
      name: Male01State.RUN,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 2
    },
    {
      name: Male01State.SHOOTING,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: false,
      priority: 3
    },
    {
      name: Male01State.STANDING_SHOOT,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 3
    },
    {
      name: Male01State.READING,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 1
    },
    {
      name: Male01State.COLD,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: true,
      priority: 1
    },
    {
      name: Male01State.DEATH,
      path: '/src/models/Male_01/action/<EMAIL>',
      loop: false,
      priority: 4
    }
  ];

  // 默认Male01纹理配置
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'workerDiffuse',
      path: '/src/models/Male_01/Tex/SS_MaleWorker_02.jpg',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 默认武器纹理配置
  private static readonly DEFAULT_WEAPON_TEXTURES: TextureFile[] = [
    {
      name: 'rifleBaseColor',
      path: '/src/models/Male_01/Tex/Rifle_Base_Color.jpg',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 状态管理
  private health = 100;
  private maxHealth = 100;
  private isAlive = true;
  private currentState: Male01State = Male01State.IDLE;
  private isMoving = false;
  private moveDirection = new THREE.Vector3(1, 0, 0);
  private walkSpeed = 1.5;
  private runSpeed = 3.0;
  private moveBounds = { min: -8, max: 8 };
  
  // 手动控制系统
  private isManualControl = false;  // 是否启用手动控制
  private manualMoveDirection = new THREE.Vector3(0, 0, 0);  // 手动移动方向
  private isRunning = false;  // 是否正在跑步
  
  // 射击系统
  private shootingRange = 8.0; // 射击检测范围
  private shootingCooldown = 1500; // 射击冷却时间(ms)
  private lastShootTime = 0;
  private currentTarget: THREE.Object3D | null = null;
  private targets: THREE.Object3D[] = []; // 可射击的目标列表
  private isTargeting = false;
  private originalWalkSpeed = 1.5;
  private originalRunSpeed = 3.0;
  
  // 子弹系统
  private bulletPool: THREE.Mesh[] = []; // 子弹对象池
  private activeBullets: Set<THREE.Mesh> = new Set(); // 活动中的子弹
  private bulletMaterial!: THREE.MeshBasicMaterial; // 子弹材质
  private bulletGeometry!: THREE.PlaneGeometry; // 子弹几何体
  private bulletSpeed = 8; // 降低子弹速度便于观察
  private bulletLifetime = 5000; // 增加子弹生存时间便于观察(5秒)
  private bulletRotation: { x: number; y: number; z: number } = { x: 0, y: -90, z: 90 }; // 子弹旋转配置
  
  // 事件回调
  private onLoadComplete?: () => void;
  private onAnimationChange?: (newState: Male01State) => void;
  private onDeath?: () => void;
  private onMoveDirectionChange?: (direction: THREE.Vector3) => void;
  private onTextureLoaded?: () => void;
  private onTargetDetected?: (target: THREE.Object3D) => void;
  private onTargetLost?: () => void;
  private onShootTarget?: (target: THREE.Object3D) => void;

  constructor(scene: THREE.Scene, config?: Partial<Male01Config>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'Male01Worker',
      modelPath: '/src/models/Male_01/action/<EMAIL>',
      weaponPath: '/src/models/Male_01/action/huoqiang.fbx',
      weaponTextures: Male01Model.DEFAULT_WEAPON_TEXTURES,
      animations: Male01Model.DEFAULT_ANIMATIONS,
      textures: Male01Model.DEFAULT_TEXTURES,
      scale: 0.02,
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.clock = new THREE.Clock();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 创建标准Male01实例的快捷方法
   */
  static create(scene: THREE.Scene, name = 'Male01Worker'): Male01Model {
    return new Male01Model(scene, { name });
  }

  /**
   * 创建自定义位置的Male01实例
   */
  static createAt(scene: THREE.Scene, position: THREE.Vector3, name = 'Male01Worker'): Male01Model {
    return new Male01Model(scene, { name, position });
  }

  /**
   * 创建自定义缩放的Male01实例
   */
  static createWithScale(scene: THREE.Scene, scale: number, name = 'Male01Worker'): Male01Model {
    return new Male01Model(scene, { name, scale });
  }

  /**
   * 创建自定义纹理的Male01实例
   */
  static createWithTextures(scene: THREE.Scene, textures: TextureFile[], name = 'Male01Worker'): Male01Model {
    return new Male01Model(scene, { name, textures });
  }

  /**
   * 创建简单纹理配置的工厂方法
   */
  static createTextureConfig(name: string, path: string, materialProperty: TextureFile['materialProperty'] = 'map'): TextureFile {
    return {
      name,
      path,
      materialProperty,
      colorSpace: materialProperty === 'map' ? THREE.SRGBColorSpace : THREE.NoColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    };
  }

  /**
   * 创建完整纹理配置的工厂方法
   */
  static createFullTextureConfig(config: Partial<TextureFile> & { name: string; path: string }): TextureFile {
    return {
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 },
      ...config
    };
  }

  /**
   * 初始化并加载Male01模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('Loading Male01 worker model...');
      
      // 加载主模型 (使用rig文件作为主模型)
      const modelResult = await this.loader.loadMainModel(this.config.modelPath);
      this.model = modelResult.model;
      this.mixer = modelResult.mixer!;
      
      // 预处理模型
      this.loader.preprocessModel(this.model);
      
      // 设置基本属性（不包含纹理）
      this.setupModelProperties();

      // 创建动画控制器
      this.animationController = new AnimationController(this.mixer);

      // 加载所有动画文件
      const animations = await this.loader.loadAnimations(this.config.animations);
      this.setupAnimations(animations);

      // 加载武器模型（如果配置了）
      if (this.config.weaponPath) {
        await this.loadWeapon();
      }

      // 添加到场景
      this.scene.add(this.model);
      
      // 异步加载并应用纹理
      await this.loadAndApplyTextures();
      
      // 初始化子弹系统
      this.initializeBulletSystem();
      
      this.isLoaded = true;
      console.log('Male01 worker model loaded successfully');
      
      // 开始默认动画
      this.setState(Male01State.IDLE);
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
    } catch (error) {
      console.error('Failed to load Male01 worker model:', error);
      throw error;
    }
  }

  /**
   * 设置模型基本属性（不包含纹理）
   */
  private setupModelProperties(): void {
    // 设置缩放
    this.model.scale.setScalar(this.config.scale);
    
    // 设置位置
    this.model.position.copy(this.config.position);
    
    // 设置旋转
    this.model.rotation.copy(this.config.rotation);

    // 设置初始朝向匹配移动方向
    this.updateWorkerRotation();
  }

  /**
   * 加载武器模型
   */
  private async loadWeapon(): Promise<void> {
    if (!this.config.weaponPath) {
      return;
    }

    try {
      console.log('Loading weapon model:', this.config.weaponPath);
      
      // 加载武器模型
      const weaponResult = await this.loader.loadMainModel(this.config.weaponPath);
      this.weaponModel = weaponResult.model;
      
      // 找到角色模型的右手骨骼来附加武器
      this.attachWeaponToHand();
      
      // 预处理武器模型（在附加后进行，避免影响贴图）
      this.loader.preprocessModel(this.weaponModel);
      
      // 应用武器贴图（在预处理之后，确保材质状态正确）
      if (this.config.weaponTextures && this.config.weaponTextures.length > 0) {
        await this.loadAndApplyWeaponTextures();
        
        // 延迟确保贴图正确应用（解决预处理可能重置材质的问题）
        setTimeout(async () => {
          await this.loadAndApplyWeaponTextures();
        }, 100);
      }
      
      console.log('Weapon model loaded and attached successfully');
    } catch (error) {
      console.error('Failed to load weapon model:', error);
    }
  }

    /**
   * 将武器附加到角色的右手
   */
  private attachWeaponToHand(): void {
    if (!this.weaponModel || !this.model) {
      return;
    }

    console.log('=== 开始查找 Wrist_R 骨骼 ===');
    
    // 查找精确的 Wrist_R 骨骼
    let wristRBone: THREE.Bone | null = null;
    
    // 先列出所有骨骼以便调试
    console.log('=== 所有骨骼列表 ===');
    const allBones: THREE.Bone[] = [];
    this.model.traverse((child) => {
      if (child instanceof THREE.Bone) {
        allBones.push(child);
        console.log(`骨骼: "${child.name}", 父节点: "${child.parent?.name || 'none'}"`);
      }
    });
    console.log(`总计找到 ${allBones.length} 个骨骼`);

    // 方法1: 直接查找名为 Wrist_R 的骨骼
    wristRBone = allBones.find(bone => bone.name === 'Wrist_R') || null;

    if (wristRBone && this.weaponModel) {
      console.log(`🔧 将武器附加到骨骼: "${wristRBone.name}"`);
      
      // 将武器添加到 Wrist_R 骨骼
      (wristRBone as THREE.Object3D).add(this.weaponModel);
      
      // 调整武器的位置和旋转以适合手部
      this.adjustWeaponTransform();
      
      // 初始状态隐藏武器（因为默认是IDLE状态）
      this.weaponModel.visible = false;
      
      console.log('✅ 武器成功附加到 Wrist_R 骨骼（初始隐藏）');
    }
    
    console.log('=== 武器附加过程完成 ===');
  }


  /**
   * 调整武器的变换以适合手部持握
   */
  private adjustWeaponTransform(): void {
    if (!this.weaponModel) return;

    // 设置位置（相对于手部骨骼）
    this.weaponModel.position.set(-0.11000, -1.77000, -1.68000);
    
    // 设置旋转（将度数转换为弧度）
    const rotationX = THREE.MathUtils.degToRad(30);
    const rotationY = THREE.MathUtils.degToRad(90);
    const rotationZ = THREE.MathUtils.degToRad(-47);
    this.weaponModel.rotation.set(rotationX, rotationY, rotationZ);
    
    // 保持原始缩放
    this.weaponModel.scale.setScalar(1);
    
    console.log('Weapon transform adjusted with optimized values:', {
      position: this.weaponModel.position,
      rotation: {
        x: THREE.MathUtils.radToDeg(this.weaponModel.rotation.x),
        y: THREE.MathUtils.radToDeg(this.weaponModel.rotation.y),
        z: THREE.MathUtils.radToDeg(this.weaponModel.rotation.z)
      },
      scale: this.weaponModel.scale
    });
  }

  /**
   * 异步加载并应用所有纹理
   */
  private async loadAndApplyTextures(): Promise<void> {
    if (!this.config.textures || this.config.textures.length === 0) {
      console.log('No textures configured for Male01 worker model');
      return;
    }

    // 创建所有纹理加载的Promise
    const texturePromises = this.config.textures.map((textureConfig, index) => {
      return this.loadTextureAsync(textureConfig);
    });

    try {
      // 等待所有纹理加载完成
      const loadedTextures = await Promise.all(texturePromises);
      
      // 应用所有纹理到模型
      loadedTextures.forEach((textureResult, index) => {
        if (textureResult.texture) {
          this.applyTextureToModel(textureResult.texture, textureResult.config);
          console.log(`Successfully applied texture: ${textureResult.config.name}`);
        }
      });

      if (this.onTextureLoaded) {
        this.onTextureLoaded();
      }
      
      console.log('All textures loaded and applied successfully');
    } catch (error) {
      console.error('Failed to load textures:', error);
    }
  }

  /**
   * 异步加载单个纹理
   */
  private loadTextureAsync(textureConfig: TextureFile): Promise<{texture: THREE.Texture | null, config: TextureFile}> {
    return new Promise((resolve) => {
      this.textureLoader.load(
        textureConfig.path,
        (loadedTexture) => {
          // 配置纹理属性
          this.configureTexture(loadedTexture, textureConfig);
          console.log(`Texture "${textureConfig.name}" loaded successfully`);
          resolve({ texture: loadedTexture, config: textureConfig });
        },
        undefined,
        (error) => {
          console.error(`Failed to load texture "${textureConfig.name}":`, error);
          resolve({ texture: null, config: textureConfig });
        }
      );
    });
  }



  /**
   * 配置纹理属性
   */
  private configureTexture(texture: THREE.Texture, config: TextureFile): void {
    // 设置颜色空间
    if (config.colorSpace !== undefined) {
      texture.colorSpace = config.colorSpace;
    }

    // 设置翻转
    if (config.flipY !== undefined) {
      texture.flipY = config.flipY;
    }

    // 设置包装模式
    if (config.wrapS !== undefined) {
      texture.wrapS = config.wrapS;
    }
    if (config.wrapT !== undefined) {
      texture.wrapT = config.wrapT;
    }

    // 设置重复
    if (config.repeat) {
      texture.repeat.set(config.repeat.x, config.repeat.y);
    }

    texture.needsUpdate = true;
  }

  /**
   * 将纹理应用到模型
   */
  private applyTextureToModel(texture: THREE.Texture, config: TextureFile): void {
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            this.applyTextureToMaterial(material, texture, config);
          });
        } else {
          this.applyTextureToMaterial(child.material, texture, config);
        }
      }
    });
  }

  /**
   * 将纹理应用到材质
   */
  private applyTextureToMaterial(material: THREE.Material, texture: THREE.Texture, config: TextureFile): void {
    // 检查材质类型并应用纹理
    if (material instanceof THREE.MeshStandardMaterial || 
        material instanceof THREE.MeshPhongMaterial ||
        material instanceof THREE.MeshLambertMaterial ||
        material instanceof THREE.MeshBasicMaterial) {
      
      // 根据配置的材质属性应用纹理
      switch (config.materialProperty) {
        case 'map':
          material.map = texture;
          // 设置材质颜色为白色以显示纹理的真实颜色
          material.color = new THREE.Color(0xffffff);
          // 确保材质是不透明的
          material.transparent = false;
          material.opacity = 1.0;
          break;
        case 'normalMap':
          if ('normalMap' in material) {
            (material as any).normalMap = texture;
          }
          break;
        case 'roughnessMap':
          if ('roughnessMap' in material) {
            (material as any).roughnessMap = texture;
          }
          break;
        case 'metalnessMap':
          if ('metalnessMap' in material) {
            (material as any).metalnessMap = texture;
          }
          break;
        case 'aoMap':
          if ('aoMap' in material) {
            (material as any).aoMap = texture;
          }
          break;
        case 'emissiveMap':
          if ('emissiveMap' in material) {
            (material as any).emissiveMap = texture;
          }
          break;
      }
      
      // 标记材质需要更新
      material.needsUpdate = true;
    } else {
      console.warn(`Material type ${material.type} not supported for texture application`);
    }
  }

  /**
   * 异步加载并应用武器贴图
   */
  private async loadAndApplyWeaponTextures(): Promise<void> {
    if (!this.config.weaponTextures || this.config.weaponTextures.length === 0 || !this.weaponModel) {
      console.log('No weapon textures configured or weapon model not loaded');
      return;
    }

    // 创建所有武器纹理加载的Promise
    const texturePromises = this.config.weaponTextures.map((textureConfig, index) => {
      return this.loadTextureAsync(textureConfig);
    });

    try {
      // 等待所有纹理加载完成
      const loadedTextures = await Promise.all(texturePromises);
      
      // 应用所有纹理到武器模型
      loadedTextures.forEach((textureResult, index) => {
        if (textureResult.texture) {
          this.applyTextureToWeapon(textureResult.texture, textureResult.config);
          console.log(`Successfully applied weapon texture: ${textureResult.config.name}`);
        }
      });
      
      console.log('All weapon textures loaded and applied successfully');
    } catch (error) {
      console.error('Failed to load weapon textures:', error);
    }
  }

  /**
   * 将纹理应用到武器模型
   */
  private applyTextureToWeapon(texture: THREE.Texture, config: TextureFile): void {
    if (!this.weaponModel) return;

    this.weaponModel.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            this.applyTextureToMaterial(material, texture, config);
          });
        } else {
          this.applyTextureToMaterial(child.material, texture, config);
        }
      }
    });
  }

  /**
   * 初始化子弹系统
   */
  private initializeBulletSystem(): void {
    // 创建子弹几何体 - 使用更大的平面几何体便于观察
    this.bulletGeometry = new THREE.PlaneGeometry(0.5, 2.0); // 大幅增大子弹大小便于观察
    
    // 加载子弹纹理
    const bulletTexture = this.textureLoader.load(
      '/src/models/Male_01/Tex/Bullet_01.png',
      () => {
        console.log('子弹纹理加载成功');
      },
      undefined,
      (error) => {
        console.error('子弹纹理加载失败:', error);
        // 如果纹理加载失败，使用纯色材质作为备用
        this.bulletMaterial.map = null;
        this.bulletMaterial.color.setHex(0xffff00); // 黄色子弹
        this.bulletMaterial.needsUpdate = true;
      }
    );
    bulletTexture.colorSpace = THREE.SRGBColorSpace;
    bulletTexture.flipY = false; // 尝试不翻转
    
    // 创建子弹材质 - 先用纯色材质确保可见性
    this.bulletMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00, // 亮黄色，易于观察
      map: bulletTexture,
      transparent: true,
      alphaTest: 0.1,
      side: THREE.DoubleSide,
      depthTest: false // 禁用深度测试，确保始终可见
    });
    
    // 预创建子弹对象池
    for (let i = 0; i < 20; i++) {
      const bullet = this.createBullet();
      bullet.visible = false;
      this.bulletPool.push(bullet);
      this.scene.add(bullet);
    }
    
    console.log('子弹系统初始化完成，预创建20发子弹');
    console.log(`📊 初始化验证 - 子弹池: ${this.bulletPool.length}, 材质: ${!!this.bulletMaterial}, 几何体: ${!!this.bulletGeometry}`);
  }

  /**
   * 创建单个子弹对象
   */
  private createBullet(): THREE.Mesh {
    const bullet = new THREE.Mesh(this.bulletGeometry, this.bulletMaterial);
    bullet.userData = {
      velocity: new THREE.Vector3(),
      startTime: 0,
      isActive: false
    };
    return bullet;
  }

  /**
   * 获取可用的子弹对象
   */
  private getBullet(): THREE.Mesh | null {
    // 从对象池中获取可用的子弹
    for (const bullet of this.bulletPool) {
      if (!bullet.userData.isActive) {
        return bullet;
      }
    }
    
    // 如果没有可用的子弹，创建新的
    const newBullet = this.createBullet();
    this.bulletPool.push(newBullet);
    this.scene.add(newBullet);
    return newBullet;
  }

  /**
   * 发射子弹
   */
  private fireBullet(startPosition: THREE.Vector3, direction: THREE.Vector3): void {
    const bullet = this.getBullet();
    if (!bullet) {
      console.warn('无法获取子弹对象');
      return;
    }
    
    // 设置子弹初始位置
    bullet.position.copy(startPosition);
    bullet.visible = true;
    
    // 计算子弹的旋转，让子弹头朝向飞行方向
    const angle = Math.atan2(direction.x, direction.z);
    bullet.rotation.set(
      THREE.MathUtils.degToRad(this.bulletRotation.x),
      angle + THREE.MathUtils.degToRad(this.bulletRotation.y),
      THREE.MathUtils.degToRad(this.bulletRotation.z)
    );
    
    // 设置子弹数据
    const targetDirection = direction.clone().normalize();
    bullet.userData.velocity = targetDirection.multiplyScalar(this.bulletSpeed);
    bullet.userData.startTime = Date.now();
    bullet.userData.isActive = true;
    
    this.activeBullets.add(bullet);
    
    console.log(`🔫 发射子弹！位置: (${startPosition.x.toFixed(2)}, ${startPosition.y.toFixed(2)}, ${startPosition.z.toFixed(2)})`);
    console.log(`🎯 方向: (${targetDirection.x.toFixed(2)}, ${targetDirection.y.toFixed(2)}, ${targetDirection.z.toFixed(2)})`);
    console.log(`📊 当前活动子弹数: ${this.activeBullets.size}`);
  }

  /**
   * 更新子弹系统
   */
  private updateBullets(deltaTime: number): void {
    if (this.activeBullets.size === 0) return;
    
    const currentTime = Date.now();
    const bulletsToRemove: THREE.Mesh[] = [];
    
    // 每秒输出一次子弹状态（调试用）
    if (Math.floor(currentTime / 1000) !== Math.floor((currentTime - deltaTime * 1000) / 1000)) {
      console.log(`🔄 更新子弹系统: ${this.activeBullets.size} 个活动子弹`);
    }
    
    for (const bullet of this.activeBullets) {
      // 检查子弹生存时间
      if (currentTime - bullet.userData.startTime > this.bulletLifetime) {
        bulletsToRemove.push(bullet);
        console.log(`⏰ 子弹生存时间到期`);
        continue;
      }
      
      // 更新子弹位置
      const velocity = bullet.userData.velocity as THREE.Vector3;
      const oldPosition = bullet.position.clone();
      bullet.position.add(velocity.clone().multiplyScalar(deltaTime));
      
      // 调试：偶尔输出子弹位置
      if (Math.random() < 0.01) { // 1% 概率输出
        console.log(`🚀 子弹位置: (${bullet.position.x.toFixed(2)}, ${bullet.position.y.toFixed(2)}, ${bullet.position.z.toFixed(2)})`);
      }
      
      // 检查子弹是否超出范围（简单的边界检查）
      if (Math.abs(bullet.position.x) > 50 || Math.abs(bullet.position.z) > 50 || bullet.position.y < -5) {
        bulletsToRemove.push(bullet);
        console.log(`🌍 子弹超出范围: (${bullet.position.x.toFixed(2)}, ${bullet.position.y.toFixed(2)}, ${bullet.position.z.toFixed(2)})`);
      }
    }
    
    // 移除过期的子弹
    for (const bullet of bulletsToRemove) {
      this.recycleBullet(bullet);
    }
  }

  /**
   * 回收子弹到对象池
   */
  private recycleBullet(bullet: THREE.Mesh): void {
    bullet.visible = false;
    bullet.userData.isActive = false;
    bullet.userData.velocity.set(0, 0, 0);
    this.activeBullets.delete(bullet);
    console.log(`♻️ 子弹回收，剩余活动子弹: ${this.activeBullets.size}`);
  }

  /**
   * 获取武器长度
   */
  private getWeaponLength(): number {
    if (!this.weaponModel) {
      console.warn('武器模型不存在，使用默认长度');
      return 1.0; // 默认长度
    }

    try {
      // 计算武器的包围盒
      const box = new THREE.Box3().setFromObject(this.weaponModel);
      const size = new THREE.Vector3();
      box.getSize(size);
      
      // 武器通常沿着Z轴方向最长，但也可能是X轴，取最大值
      const weaponLength = Math.max(size.x, size.y, size.z);
      
      console.log(`🔫 武器尺寸: X:${size.x.toFixed(2)}, Y:${size.y.toFixed(2)}, Z:${size.z.toFixed(2)}`);
      console.log(`🔫 计算得出武器长度: ${weaponLength.toFixed(2)}`);
      
      return weaponLength;
    } catch (error) {
      console.error('计算武器长度时出错:', error);
      return 1.0; // 出错时返回默认长度
    }
  }

  /**
   * 计算枪口位置
   */
  private getMuzzlePosition(): THREE.Vector3 {
    if (!this.model) {
      console.warn('模型不存在，使用默认位置');
      return new THREE.Vector3();
    }
    
    // 获取武器长度，如果有武器则使用动态长度，否则使用默认偏移
    const weaponLength = this.weaponModel ? this.getWeaponLength() : 1.0;
    
    // 基于角色位置和朝向计算枪口位置
    const hunterPosition = this.model.position.clone();
    const hunterRotation = this.model.rotation.y;
    
    // 计算前方位置（枪口位置） - 使用武器长度的70%作为枪口位置
    const muzzleOffset = weaponLength * 0.7; // 枪口通常不在武器最前端
    const forwardX = Math.sin(hunterRotation) * muzzleOffset;
    const forwardZ = Math.cos(hunterRotation) * muzzleOffset;
    
    const muzzlePosition = new THREE.Vector3(
      hunterPosition.x + forwardX,
      hunterPosition.y + 1.0, // 枪口高度
      hunterPosition.z + forwardZ
    );
    
    console.log(`🔫 武器长度: ${weaponLength.toFixed(2)}, 枪口偏移: ${muzzleOffset.toFixed(2)}`);
    console.log(`🔫 枪口位置: (${muzzlePosition.x.toFixed(2)}, ${muzzlePosition.y.toFixed(2)}, ${muzzlePosition.z.toFixed(2)})`);
    return muzzlePosition;
  }



  /**
   * 设置动画状态
   */
  private setupAnimations(animationsMap: Map<string, THREE.AnimationClip[]>): void {
    // 为每个动画文件添加状态
    this.config.animations.forEach((animConfig) => {
      const clips = animationsMap.get(animConfig.name);
      if (clips && clips.length > 0) {
        // 使用第一个动画片段
        const clip = clips[0];
        
        this.animationController.addState(animConfig.name, clip, {
          loop: animConfig.loop,
          priority: animConfig.priority,
          fadeInTime: 0.5,
          fadeOutTime: 0.5
        });
      }
    });

    // 设置状态转换规则
    this.setupAnimationTransitions();
  }

  /**
   * 设置动画状态转换规则
   */
  private setupAnimationTransitions(): void {
    // IDLE -> 其他状态 transitions
    this.animationController.addTransition(Male01State.IDLE, Male01State.WALK, 0.3);
    this.animationController.addTransition(Male01State.IDLE, Male01State.RUN, 0.3);
    this.animationController.addTransition(Male01State.IDLE, Male01State.SHOOTING, 0.2);
    this.animationController.addTransition(Male01State.IDLE, Male01State.STANDING_SHOOT, 0.2);
    this.animationController.addTransition(Male01State.IDLE, Male01State.READING, 0.5);
    this.animationController.addTransition(Male01State.IDLE, Male01State.COLD, 0.5);
    this.animationController.addTransition(Male01State.IDLE, Male01State.DEATH, 0.5);
    
    // WALK -> 其他状态 transitions
    this.animationController.addTransition(Male01State.WALK, Male01State.IDLE, 0.3);
    this.animationController.addTransition(Male01State.WALK, Male01State.RUN, 0.2);
    this.animationController.addTransition(Male01State.WALK, Male01State.SHOOTING, 0.2);
    this.animationController.addTransition(Male01State.WALK, Male01State.DEATH, 0.5);
    
    // RUN -> 其他状态 transitions
    this.animationController.addTransition(Male01State.RUN, Male01State.IDLE, 0.3);
    this.animationController.addTransition(Male01State.RUN, Male01State.WALK, 0.2);
    this.animationController.addTransition(Male01State.RUN, Male01State.SHOOTING, 0.2);
    this.animationController.addTransition(Male01State.RUN, Male01State.DEATH, 0.5);
    
    // SHOOTING -> 其他状态 transitions (移除自动转换到IDLE，改为手动控制)
    // this.animationController.addTransition(Male01State.SHOOTING, Male01State.IDLE, 0.3);
    this.animationController.addTransition(Male01State.SHOOTING, Male01State.WALK, 0.3);
    this.animationController.addTransition(Male01State.SHOOTING, Male01State.RUN, 0.3);
    this.animationController.addTransition(Male01State.SHOOTING, Male01State.STANDING_SHOOT, 0.2);
    this.animationController.addTransition(Male01State.SHOOTING, Male01State.DEATH, 0.5);
    
    // STANDING_SHOOT -> 其他状态 transitions
    this.animationController.addTransition(Male01State.STANDING_SHOOT, Male01State.IDLE, 0.3);
    this.animationController.addTransition(Male01State.STANDING_SHOOT, Male01State.SHOOTING, 0.2);
    this.animationController.addTransition(Male01State.STANDING_SHOOT, Male01State.DEATH, 0.5);
    
    // READING -> 其他状态 transitions
    this.animationController.addTransition(Male01State.READING, Male01State.IDLE, 0.5);
    this.animationController.addTransition(Male01State.READING, Male01State.DEATH, 0.5);
    
    // COLD -> 其他状态 transitions
    this.animationController.addTransition(Male01State.COLD, Male01State.IDLE, 0.5);
    this.animationController.addTransition(Male01State.COLD, Male01State.DEATH, 0.5);
  }

  /**
   * 设置Male01状态
   */
  async setState(state: Male01State): Promise<void> {
    if (!this.isLoaded || !this.isAlive && state !== Male01State.DEATH) {
      return;
    }

    this.currentState = state;
    await this.animationController.playState(state);
    
    // 特殊状态处理
    if (state === Male01State.WALK || state === Male01State.RUN) {
      this.isMoving = true;
    } else {
      this.isMoving = false;
    }
    
    // 根据状态控制武器显示/隐藏
    this.updateWeaponVisibility(state);
    
    if (this.onAnimationChange) {
      this.onAnimationChange(state);
    }
  }

  /**
   * 开始巡逻行走
   */
  async startPatrol(isRunning = false): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    await this.setState(isRunning ? Male01State.RUN : Male01State.WALK);
    this.isMoving = true;
    
    // 设置初始朝向以匹配移动方向
    this.updateWorkerRotation();
  }

  /**
   * 停止巡逻
   */
  async stopPatrol(): Promise<void> {
    if (!this.isLoaded) return;

    this.isMoving = false;
    await this.setState(Male01State.IDLE);
  }

  /**
   * 射击动作
   */
  async shoot(isStanding = false): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    console.log(`🔫 执行射击动作 (isStanding: ${isStanding})`);

    // const wasMoving = this.isMoving;
    this.isMoving = false;
    
    // 如果是单次射击（非站立射击），发射子弹
    if (!isStanding) {
      this.fireManualBullet();
    }
    
    await this.setState(isStanding ? Male01State.STANDING_SHOOT : Male01State.SHOOTING);
    
    // 如果是单次射击，完成后返回之前的状态
    // if (!isStanding) {
    //   setTimeout(() => {
    //     if (this.isAlive) {
    //       if (wasMoving) {
    //         this.setState(Male01State.WALK);
    //         this.isMoving = true;
    //       } else {
    //         this.setState(Male01State.IDLE);
    //       }
    //     }
    //   }, 2000);
    // }
  }

  /**
   * 阅读动作
   */
  async startReading(): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    this.isMoving = false;
    await this.setState(Male01State.READING);
  }

  /**
   * 寒冷动作
   */
  async startShivering(): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    this.isMoving = false;
    await this.setState(Male01State.COLD);
  }

  /**
   * 受到伤害
   */
  takeDamage(damage: number): void {
    if (!this.isAlive) return;

    this.health = Math.max(0, this.health - damage);
    console.log(`Male01 worker took ${damage} damage. Health: ${this.health}/${this.maxHealth}`);

    if (this.health <= 0) {
      this.die();
    }
  }

  /**
   * 获取当前血量
   */
  getHealth(): number {
    return this.health;
  }

  /**
   * 获取最大血量
   */
  getMaxHealth(): number {
    return this.maxHealth;
  }

  /**
   * 设置血量
   */
  setHealth(health: number): void {
    this.health = Math.max(0, Math.min(health, this.maxHealth));
  }

  /**
   * 设置最大血量
   */
  setMaxHealth(maxHealth: number): void {
    this.maxHealth = Math.max(1, maxHealth);
    this.health = Math.min(this.health, this.maxHealth);
  }

  /**
   * 获取存活状态
   */
  getIsAlive(): boolean {
    return this.isAlive;
  }

  /**
   * 设置存活状态（仅供内部使用，外部应通过takeDamage等方法改变状态）
   * @param alive 存活状态
   * @internal 此方法主要用于内部状态管理，不建议外部直接调用
   */
  setIsAlive(alive: boolean): void {
    this.isAlive = alive;
  }

  /**
   * 工人死亡
   */
  private async die(): Promise<void> {
    if (!this.isAlive) return;

    this.isAlive = false;
    this.isMoving = false;
    await this.setState(Male01State.DEATH);
    
    console.log('Male01 worker has died');
    
    if (this.onDeath) {
      this.onDeath();
    }
  }

  /**
   * 复活工人
   */
  revive(): void {
    this.health = this.maxHealth;
    this.isAlive = true;
    this.setState(Male01State.IDLE);
    console.log('Male01 worker revived');
  }

  /**
   * 更新Male01模型（每帧调用）
   */
  update(): void {
    if (!this.isLoaded) return;

    const deltaTime = this.clock.getDelta();
    this.animationController.update(deltaTime);

    // 射击系统更新
    if (this.isAlive) {
      this.updateShootingSystem();
    }

    // 子弹系统更新
    this.updateBullets(deltaTime);

    // 处理移动逻辑
    if (this.isAlive) {
      if (this.isManualControl) {
        // 手动控制模式：根据手动移动方向决定是否移动
        if (this.manualMoveDirection.length() > 0) {
          this.updateMovement(deltaTime);
        }
      } else {
        // 自动模式：根据isMoving标志决定是否移动
        if (this.isMoving) {
          this.updateMovement(deltaTime);
        }
      }
    }
  }

  /**
   * 更新移动
   */
  private updateMovement(deltaTime: number): void {
    if (!this.model) return;

    if (this.isManualControl) {
      // 手动控制模式
      this.updateManualMovement(deltaTime);
    } else {
      // 自动巡逻模式
      this.updateAutoMovement(deltaTime);
    }
  }

  /**
   * 更新手动控制移动
   */
  private updateManualMovement(deltaTime: number): void {
    if (!this.model || this.manualMoveDirection.length() === 0) return;

    // 根据当前状态选择速度
    const speed = this.isRunning ? this.runSpeed : this.walkSpeed;
    
    // 沿着手动方向移动
    const movement = this.manualMoveDirection.clone().multiplyScalar(speed * deltaTime);
    this.model.position.add(movement);

    // 检查边界限制
    if (this.model.position.x > this.moveBounds.max) {
      this.model.position.x = this.moveBounds.max;
    } else if (this.model.position.x < this.moveBounds.min) {
      this.model.position.x = this.moveBounds.min;
    }

    if (this.model.position.z > this.moveBounds.max) {
      this.model.position.z = this.moveBounds.max;
    } else if (this.model.position.z < this.moveBounds.min) {
      this.model.position.z = this.moveBounds.min;
    }
  }

  /**
   * 更新自动移动（原有的巡逻逻辑）
   */
  private updateAutoMovement(deltaTime: number): void {
    if (!this.model) return;

    // 根据当前状态选择速度
    const speed = this.currentState === Male01State.RUN ? this.runSpeed : this.walkSpeed;
    
    // 沿着当前方向移动
    const movement = this.moveDirection.clone().multiplyScalar(speed * deltaTime);
    this.model.position.add(movement);

    // 检查边界并转向
    let directionChanged = false;
    
    if (this.model.position.x > this.moveBounds.max || this.model.position.x < this.moveBounds.min) {
      this.moveDirection.x *= -1;
      directionChanged = true;
    }

    if (this.model.position.z > this.moveBounds.max || this.model.position.z < this.moveBounds.min) {
      this.moveDirection.z *= -1;
      directionChanged = true;
    }

    // 如果方向改变了，更新工人的朝向
    if (directionChanged) {
      this.updateWorkerRotation();
      
      if (this.onMoveDirectionChange) {
        this.onMoveDirectionChange(this.moveDirection);
      }
    }
  }

  /**
   * 更新工人的朝向以匹配移动方向
   */
  private updateWorkerRotation(): void {
    if (!this.model) return;

    // 计算目标旋转角度
    const targetAngle = Math.atan2(this.moveDirection.x, this.moveDirection.z);
    
    // 设置工人的Y轴旋转，使其面向移动方向
    this.model.rotation.y = targetAngle;
  }

  /**
   * 更新射击系统
   */
  private updateShootingSystem(): void {
    if (!this.model || this.targets.length === 0) return;

    const currentTime = Date.now();

    // 如果没有当前目标，扫描范围内的目标
    if (!this.currentTarget || !this.isTargetInRange(this.currentTarget)) {
      this.findNearestTarget();
    }

    // 如果有目标
    if (this.currentTarget) {
      const distanceToTarget = this.getDistanceToTarget(this.currentTarget);
      
      // 检查是否在射击范围内
      if (distanceToTarget <= this.shootingRange) {
        // 在射击范围内
        if (!this.isTargeting) {
          this.startTargeting();
        }
        
        // 面向目标
        this.faceTarget(this.currentTarget);
        
        // 检查射击冷却
        if (currentTime - this.lastShootTime >= this.shootingCooldown) {
          this.shootAtTarget();
        }
      } else {
        // 超出射击范围，停止瞄准
        if (this.isTargeting) {
          this.stopTargeting();
        }
      }
    } else {
      // 没有目标，停止瞄准
      if (this.isTargeting) {
        this.stopTargeting();
      }
    }
  }

  /**
   * 查找最近的目标
   */
  private findNearestTarget(): void {
    if (!this.model) return;

    let nearestTarget: THREE.Object3D | null = null;
    let nearestDistance = this.shootingRange;

    for (const target of this.targets) {
      const distance = this.getDistanceToTarget(target);
      if (distance <= this.shootingRange && distance < nearestDistance) {
        nearestTarget = target;
        nearestDistance = distance;
      }
    }

    if (nearestTarget !== this.currentTarget) {
      this.currentTarget = nearestTarget;
      
      if (nearestTarget && this.onTargetDetected) {
        this.onTargetDetected(nearestTarget);
      } else if (!nearestTarget && this.onTargetLost) {
        this.onTargetLost();
      }
    }
  }

  /**
   * 检查目标是否在范围内
   */
  private isTargetInRange(target: THREE.Object3D): boolean {
    return this.getDistanceToTarget(target) <= this.shootingRange;
  }

  /**
   * 获取到目标的距离
   */
  private getDistanceToTarget(target: THREE.Object3D): number {
    if (!this.model) return Infinity;
    return this.model.position.distanceTo(target.position);
  }

  /**
   * 面向目标
   */
  private faceTarget(target: THREE.Object3D): void {
    if (!this.model) return;

    const direction = new THREE.Vector3();
    direction.subVectors(target.position, this.model.position);
    direction.y = 0; // 只考虑水平方向
    direction.normalize();

    if (direction.length() > 0) {
      const targetAngle = Math.atan2(direction.x, direction.z);
      this.model.rotation.y = targetAngle;
    }
  }

  /**
   * 开始瞄准目标
   */
  private async startTargeting(): Promise<void> {
    if (this.isTargeting) return;
    
    this.isTargeting = true;
    
    // 如果当前不在射击状态，切换到站立射击状态
    if (this.currentState !== Male01State.STANDING_SHOOT && this.currentState !== Male01State.SHOOTING) {
      // 暂停移动
      const wasMoving = this.isMoving;
      this.isMoving = false;
      
      // 切换到站立射击状态
      await this.setState(Male01State.STANDING_SHOOT);
    }
    
    console.log('Worker started targeting enemy');
  }

  /**
   * 停止瞄准
   */
  private async stopTargeting(): Promise<void> {
    if (!this.isTargeting) return;
    
    this.isTargeting = false;
    this.currentTarget = null;
    
    // 如果当前在射击状态，返回待机状态
    if (this.currentState === Male01State.STANDING_SHOOT || this.currentState === Male01State.SHOOTING) {
      await this.setState(Male01State.IDLE);
    }
    
    console.log('Worker stopped targeting');
  }

  /**
   * 射击目标
   */
  private async shootAtTarget(): Promise<void> {
    if (!this.currentTarget) {
      console.log('🎯 没有目标，取消射击');
      return;
    }
    
    // 保存当前目标，防止在异步操作期间被清空
    const targetToShoot = this.currentTarget;
    
    this.lastShootTime = Date.now();
    
    console.log('🔫 开始射击目标:', targetToShoot.name);
    
    // 暂停移动进行射击
    const wasMoving = this.isMoving;
    this.isMoving = false;
    
    // 先发射子弹效果
    this.fireAtTarget(targetToShoot);
    
    // 执行射击动画
    await this.setState(Male01State.SHOOTING);
    
    console.log('Worker shooting at target!');
    
    // 检查目标是否仍然有效
    if (this.onShootTarget && targetToShoot) {
      this.onShootTarget(targetToShoot);
    }
    
    // 射击完成后恢复状态
    // setTimeout(() => {
    //   if (this.isAlive && this.currentTarget) {
    //     // 如果还有目标，回到站立射击状态
    //     this.setState(Male01State.STANDING_SHOOT);
    //   } else {
    //     // 没有目标，返回待机状态
    //     this.setState(Male01State.IDLE);
    //   }
    // }, 1000); // 射击动画持续时间
  }

  /**
   * 向目标发射子弹
   */
  private fireAtTarget(target: THREE.Object3D): void {
    // 计算枪口位置
    const muzzlePosition = this.getMuzzlePosition();
    
    // 计算射击方向
    const direction = new THREE.Vector3();
    direction.subVectors(target.position, muzzlePosition);
    direction.y += 0.5; // 稍微向上瞄准，模拟真实射击
    direction.normalize();
    
    // 发射子弹
    this.fireBullet(muzzlePosition, direction);
  }

  /**
   * 设置移动边界
   */
  setMoveBounds(min: number, max: number): void {
    this.moveBounds = { min, max };
  }

  /**
   * 设置行走速度
   */
  setWalkSpeed(speed: number): void {
    this.walkSpeed = speed;
  }

  /**
   * 设置跑步速度
   */
  setRunSpeed(speed: number): void {
    this.runSpeed = speed;
  }

  /**
   * 设置移动方向
   */
  setMoveDirection(direction: THREE.Vector3): void {
    this.moveDirection.copy(direction).normalize();
    // 更新朝向以匹配新的移动方向
    this.updateWorkerRotation();
  }

  /**
   * 启用手动控制模式
   */
  enableManualControl(): void {
    this.isManualControl = true;
    this.isMoving = false; // 停止自动移动
    console.log('Manual control enabled for', this.config.name);
  }

  /**
   * 禁用手动控制模式
   */
  disableManualControl(): void {
    this.isManualControl = false;
    this.manualMoveDirection.set(0, 0, 0);
    this.isRunning = false;
    this.setState(Male01State.IDLE);
    console.log('Manual control disabled for', this.config.name);
  }

  /**
   * 检查是否为手动控制模式
   */
  isInManualControl(): boolean {
    return this.isManualControl;
  }

  /**
   * 手动移动控制 - 设置移动方向
   * @param direction 移动方向向量 (x, z)，y轴忽略
   * @param isRunning 是否跑步
   */
  manualMove(direction: THREE.Vector3, isRunning = false): void {
    if (!this.isManualControl || !this.isAlive) return;

    this.manualMoveDirection.copy(direction);
    this.manualMoveDirection.y = 0; // 忽略Y轴
    this.isRunning = isRunning;

    // 根据移动方向更新状态
    if (this.manualMoveDirection.length() > 0) {
      // 有移动输入
      this.manualMoveDirection.normalize();
      
      // 更新朝向
      this.updateManualRotation();
      
      // 更新动画状态
      const targetState = isRunning ? Male01State.RUN : Male01State.WALK;
      if (this.currentState !== targetState) {
        this.setState(targetState);
      }
    } else {
      // 没有移动输入，切换到待机状态
      if (this.currentState !== Male01State.IDLE) {
        this.setState(Male01State.IDLE);
      }
    }
  }

  /**
   * 手动移动控制 - 键盘输入方式
   * @param forward 前进 (0-1)
   * @param backward 后退 (0-1)
   * @param left 左移 (0-1)
   * @param right 右移 (0-1)
   * @param isRunning 是否跑步
   */
  manualMoveKeyboard(forward: number, backward: number, left: number, right: number, isRunning = false): void {
    if (!this.isManualControl || !this.isAlive) return;

    // 计算移动方向
    const moveVector = new THREE.Vector3(
      right - left,    // X轴：右为正，左为负
      0,               // Y轴：忽略
      backward - forward // Z轴：后为正，前为负
    );

    this.manualMove(moveVector, isRunning);
  }

  /**
   * 手动移动控制 - 遥感输入方式
   * @param joystickX 遥感X轴 (-1 到 1)
   * @param joystickY 遥感Y轴 (-1 到 1)
   * @param isRunning 是否跑步
   */
  manualMoveJoystick(joystickX: number, joystickY: number, isRunning = false): void {
    if (!this.isManualControl || !this.isAlive) return;

    // 遥感坐标转换为移动方向
    const moveVector = new THREE.Vector3(
      joystickX,   // X轴
      0,           // Y轴：忽略
      joystickY    // Z轴：注意遥感Y轴对应3D世界的Z轴
    );

    this.manualMove(moveVector, isRunning);
  }

  /**
   * 手动射击控制
   */
  manualShoot(): void {
    if (!this.isManualControl || !this.isAlive) return;

    // 停止移动并射击
    const previousDirection = this.manualMoveDirection.clone();
    this.manualMoveDirection.set(0, 0, 0);
    
    // 发射子弹效果（手动射击时朝前方射击）
    this.fireManualBullet();
    
    this.shoot(false); // 单次射击
    
    // 延迟后恢复移动
    setTimeout(() => {
      if (this.isManualControl) {
        this.manualMoveDirection.copy(previousDirection);
      }
    }, 1000);
  }

  /**
   * 手动射击时发射子弹
   */
  private fireManualBullet(): void {
    if (!this.model) {
      console.warn('🔫 无法发射子弹：模型不存在');
      return;
    }
    
    console.log('🔫 开始手动射击');
    
    // 计算枪口位置
    const muzzlePosition = this.getMuzzlePosition();
    
    // 计算朝前方的射击方向
    const hunterRotation = this.model.rotation.y;
    const direction = new THREE.Vector3(
      Math.sin(hunterRotation),
      0.1, // 稍微向上
      Math.cos(hunterRotation)
    );
    direction.normalize();
    
    console.log(`🎯 射击方向: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}, ${direction.z.toFixed(2)})`);
    
    // 发射子弹
    this.fireBullet(muzzlePosition, direction);
  }

  /**
   * 测试子弹系统（调试用）
   */
  testBulletSystem(): void {
    console.log('🧪 测试子弹系统');
    console.log(`📊 子弹池大小: ${this.bulletPool.length}`);
    console.log(`📊 活动子弹数: ${this.activeBullets.size}`);
    console.log(`📊 子弹材质: `, this.bulletMaterial);
    console.log(`📊 子弹几何体: `, this.bulletGeometry);
    
    if (this.model) {
      // 发射一颗测试子弹
      const testPosition = this.model.position.clone();
      testPosition.y += 2;
      const testDirection = new THREE.Vector3(1, 0, 0);
      
      console.log('🚀 发射测试子弹');
      this.fireBullet(testPosition, testDirection);
    }
  }

  /**
   * 动态配置子弹参数
   */
  configureBullet(config: {
    size?: { width: number; height: number };
    speed?: number;
    lifetime?: number;
    rotation?: { x: number; y: number; z: number };
  }): void {
    if (config.size) {
      // 更新子弹几何体大小
      this.bulletGeometry.dispose();
      this.bulletGeometry = new THREE.PlaneGeometry(config.size.width, config.size.height);
      
      // 更新现有子弹的几何体
      this.bulletPool.forEach(bullet => {
        bullet.geometry = this.bulletGeometry;
      });
      this.activeBullets.forEach((data, bullet) => {
        bullet.geometry = this.bulletGeometry;
      });
      
      console.log(`🔧 子弹大小已更新: ${config.size.width} x ${config.size.height}`);
    }
    
    if (config.speed !== undefined) {
      this.bulletSpeed = config.speed;
      console.log(`🔧 子弹速度已更新: ${config.speed}`);
    }
    
    if (config.lifetime !== undefined) {
      this.bulletLifetime = config.lifetime;
      console.log(`🔧 子弹生命周期已更新: ${config.lifetime}ms`);
    }
    
    // 旋转配置将在 fireBullet 方法中应用
    if (config.rotation !== undefined) {
      this.bulletRotation = config.rotation;
      console.log(`🔧 子弹旋转已更新: X:${config.rotation.x}° Y:${config.rotation.y}° Z:${config.rotation.z}°`);
    }
  }

  /**
   * 获取当前子弹配置
   */
  getBulletConfig(): {
    size: { width: number; height: number };
    speed: number;
    lifetime: number;
    rotation: { x: number; y: number; z: number };
  } {
    const geometry = this.bulletGeometry as THREE.PlaneGeometry;
    return {
      size: {
        width: geometry.parameters.width,
        height: geometry.parameters.height
      },
      speed: this.bulletSpeed,
      lifetime: this.bulletLifetime,
      rotation: this.bulletRotation || { x: 0, y: 0, z: 0 }
    };
  }

  /**
   * 手动瞄准射击控制
   */
  manualAimShoot(): void {
    if (!this.isManualControl || !this.isAlive) return;

    // 停止移动并进入瞄准状态
    this.manualMoveDirection.set(0, 0, 0);
    this.shoot(true); // 站立瞄准射击
  }

  /**
   * 更新手动控制的朝向
   */
  private updateManualRotation(): void {
    if (!this.model || this.manualMoveDirection.length() === 0) return;

    // 计算目标旋转角度
    const targetAngle = Math.atan2(this.manualMoveDirection.x, this.manualMoveDirection.z);
    
    // 设置工人的Y轴旋转，使其面向移动方向
    this.model.rotation.y = targetAngle;
  }

  /**
   * 获取手动控制状态
   */
  getManualControlStatus() {
    return {
      isManualControl: this.isManualControl,
      manualMoveDirection: this.manualMoveDirection.clone(),
      isRunning: this.isRunning,
      currentSpeed: this.isRunning ? this.runSpeed : this.walkSpeed
    };
  }

  /**
   * 添加射击目标
   */
  addTarget(target: THREE.Object3D): void {
    if (!this.targets.includes(target)) {
      this.targets.push(target);
      console.log('Added target to worker:', target.name || 'Unnamed');
    }
  }

  /**
   * 移除射击目标
   */
  removeTarget(target: THREE.Object3D): void {
    const index = this.targets.indexOf(target);
    if (index !== -1) {
      this.targets.splice(index, 1);
      
      // 如果移除的是当前目标，清除当前目标
      if (this.currentTarget === target) {
        this.currentTarget = null;
        if (this.isTargeting) {
          this.stopTargeting();
        }
      }
      
      console.log('Removed target from worker:', target.name || 'Unnamed');
    }
  }

  /**
   * 清除所有目标
   */
  clearTargets(): void {
    this.targets = [];
    this.currentTarget = null;
    if (this.isTargeting) {
      this.stopTargeting();
    }
    console.log('Cleared all targets');
  }

  /**
   * 设置射击范围
   */
  setShootingRange(range: number): void {
    this.shootingRange = Math.max(0, range);
  }

  /**
   * 设置射击冷却时间
   */
  setShootingCooldown(cooldown: number): void {
    this.shootingCooldown = Math.max(0, cooldown);
  }

  /**
   * 获取射击范围
   */
  getShootingRange(): number {
    return this.shootingRange;
  }

  /**
   * 获取当前目标
   */
  getCurrentTarget(): THREE.Object3D | null {
    return this.currentTarget;
  }

  /**
   * 检查是否在瞄准状态
   */
  isInTargetingMode(): boolean {
    return this.isTargeting;
  }

  /**
   * 获取所有目标
   */
  getTargets(): THREE.Object3D[] {
    return [...this.targets]; // 返回副本
  }

  /**
   * 获取工人状态信息
   */
  getStatus() {
    return {
      health: this.health,
      maxHealth: this.maxHealth,
      isAlive: this.isAlive,
      currentState: this.currentState,
      isLoaded: this.isLoaded,
      isMoving: this.isMoving,
      position: this.model?.position.clone(),
      rotation: this.model?.rotation.clone(),
      moveDirection: this.moveDirection.clone(),
      walkSpeed: this.walkSpeed,
      runSpeed: this.runSpeed,
      weaponVisible: this.isWeaponVisible(),
      // 射击系统状态
      shootingRange: this.shootingRange,
      shootingCooldown: this.shootingCooldown,
      isTargeting: this.isTargeting,
      currentTarget: this.currentTarget?.name || null,
      targetCount: this.targets.length,
      // 手动控制状态
      isManualControl: this.isManualControl,
      manualMoveDirection: this.manualMoveDirection.clone(),
      isRunning: this.isRunning
    };
  }

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 获取模型引用
   */
  getModel(): THREE.Group | null {
    return this.model || null;
  }

  /**
   * 获取动画控制器
   */
  getAnimationController(): AnimationController {
    return this.animationController;
  }

  /**
   * 重新应用纹理（公共方法）
   */
  async reapplyTexture(): Promise<void> {
    if (this.model) {
      await this.loadAndApplyTextures();
    }
  }





  /**
   * 更新纹理配置
   */
  updateTextureConfig(textures: TextureFile[]): void {
    this.config.textures = textures;
  }

  /**
   * 更新武器纹理配置
   */
  updateWeaponTextureConfig(textures: TextureFile[]): void {
    this.config.weaponTextures = textures;
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onAnimationChange?: (newState: Male01State) => void;
    onDeath?: () => void;
    onMoveDirectionChange?: (direction: THREE.Vector3) => void;
    onTextureLoaded?: () => void;
    onTargetDetected?: (target: THREE.Object3D) => void;
    onTargetLost?: () => void;
    onShootTarget?: (target: THREE.Object3D) => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onAnimationChange = callbacks.onAnimationChange;
    this.onDeath = callbacks.onDeath;
    this.onMoveDirectionChange = callbacks.onMoveDirectionChange;
    this.onTextureLoaded = callbacks.onTextureLoaded;
    this.onTargetDetected = callbacks.onTargetDetected;
    this.onTargetLost = callbacks.onTargetLost;
    this.onShootTarget = callbacks.onShootTarget;
  }

  /**
   * 调试：输出模型材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('Model not loaded yet');
      return;
    }

    console.log('=== Male01 Model Material Debug ===');
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        console.log(`Mesh name: ${child.name}`);
        
        if (Array.isArray(child.material)) {
          child.material.forEach((material, index) => {
            console.log(`  Material[${index}]:`, {
              type: material.type,
              name: material.name,
              hasMap: !!material.map,
              mapPath: material.map?.image?.src || 'none',
              color: material.color,
              transparent: material.transparent,
              opacity: material.opacity
            });
          });
        } else {
          console.log(`  Material:`, {
            type: child.material.type,
            name: child.material.name,
            hasMap: !!child.material.map,
            mapPath: child.material.map?.image?.src || 'none',
            color: child.material.color,
            transparent: child.material.transparent,
            opacity: child.material.opacity
          });
        }
      }
    });
    console.log('=== End Material Debug ===');
  }

  /**
   * 调试：输出骨骼结构信息
   */
  debugBoneStructure(): void {
    if (!this.model) {
      console.log('Model not loaded yet');
      return;
    }

    console.log('=== Male01 Model Bone Structure Debug ===');
    this.model.traverse((child) => {
      if (child instanceof THREE.Bone) {
        console.log(`Bone: ${child.name}`, {
          position: child.position,
          rotation: child.rotation,
          scale: child.scale,
          parent: child.parent?.name || 'none',
          children: child.children.map(c => c.name)
        });
      }
    });
    
    if (this.weaponModel) {
      console.log('=== Weapon Model Info ===');
      console.log('Weapon parent:', this.weaponModel.parent?.name || 'none');
      console.log('Weapon position:', this.weaponModel.position);
      console.log('Weapon rotation:', this.weaponModel.rotation);
      console.log('Weapon scale:', this.weaponModel.scale);
    }
    
    console.log('=== End Bone Structure Debug ===');
  }

  /**
   * 调整武器位置（用于调试）
   */
  adjustWeaponPosition(): void {
    if (!this.weaponModel) {
      console.log('Weapon not loaded');
      return;
    }

    console.log('Current weapon transform:', {
      position: this.weaponModel.position,
      rotation: {
        x: THREE.MathUtils.radToDeg(this.weaponModel.rotation.x),
        y: THREE.MathUtils.radToDeg(this.weaponModel.rotation.y),
        z: THREE.MathUtils.radToDeg(this.weaponModel.rotation.z)
      },
      scale: this.weaponModel.scale
    });
  }

  /**
   * 手动设置武器变换（用于实时调试）
   */
  setWeaponTransform(position: THREE.Vector3, rotationDegrees: THREE.Vector3): void {
    if (!this.weaponModel) {
      console.log('Weapon not loaded');
      return;
    }

    this.weaponModel.position.copy(position);
    this.weaponModel.rotation.set(
      THREE.MathUtils.degToRad(rotationDegrees.x),
      THREE.MathUtils.degToRad(rotationDegrees.y),
      THREE.MathUtils.degToRad(rotationDegrees.z)
    );

    console.log('Weapon transform set to:', {
      position: this.weaponModel.position,
      rotation: rotationDegrees,
      scale: this.weaponModel.scale
    });
  }

  /**
   * 获取武器模型引用（用于外部调试）
   */
  getWeaponModel(): THREE.Group | null {
    return this.weaponModel || null;
  }

  /**
   * 根据动作状态更新武器显示/隐藏
   */
  private updateWeaponVisibility(state: Male01State): void {
    if (!this.weaponModel) {
      return;
    }

    // 定义需要显示武器的战斗动作
    const combatStates = [
      Male01State.SHOOTING,
      Male01State.STANDING_SHOOT,
      Male01State.RUN  // 持枪跑步动画也需要显示武器
    ];

    const shouldShowWeapon = combatStates.includes(state);
    
    if (this.weaponModel.visible !== shouldShowWeapon) {
      this.weaponModel.visible = shouldShowWeapon;
      console.log(`武器${shouldShowWeapon ? '显示' : '隐藏'} (状态: ${state})`);
    }
  }

  /**
   * 手动控制武器显示/隐藏
   */
  setWeaponVisible(visible: boolean): void {
    if (this.weaponModel) {
      this.weaponModel.visible = visible;
      console.log(`武器${visible ? '显示' : '隐藏'} (手动控制)`);
    }
  }

  /**
   * 获取武器当前显示状态
   */
  isWeaponVisible(): boolean {
    return this.weaponModel ? this.weaponModel.visible : false;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model && this.scene) {
      this.scene.remove(this.model);
    }
    
    if (this.animationController) {
      this.animationController.dispose();
    }
    
    if (this.loader) {
      this.loader.dispose();
    }

    // 清理纹理资源
    if (this.model) {
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => {
              if (material.map) {
                material.map.dispose();
              }
              material.dispose();
            });
          } else {
            if (child.material.map) {
              child.material.map.dispose();
            }
            child.material.dispose();
          }
        }
      });
    }

    // 清理武器模型资源
    if (this.weaponModel) {
      this.weaponModel.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => {
              if (material.map) {
                material.map.dispose();
              }
              material.dispose();
            });
          } else {
            if (child.material.map) {
              child.material.map.dispose();
            }
            child.material.dispose();
          }
        }
      });
    }

    // 清理子弹系统
    for (const bullet of this.bulletPool) {
      this.scene.remove(bullet);
    }
    this.bulletPool = [];
    this.activeBullets.clear();
    
    if (this.bulletGeometry) {
      this.bulletGeometry.dispose();
    }
    if (this.bulletMaterial) {
      this.bulletMaterial.dispose();
    }

    this.isLoaded = false;
    console.log('Male01 worker model disposed');
  }

  /**
   * 获取武器信息（公共方法）
   */
  getWeaponInfo(): {
    hasWeapon: boolean;
    length?: number;
    size?: THREE.Vector3;
    position?: THREE.Vector3;
    rotation?: THREE.Euler;
    visible?: boolean;
  } {
    if (!this.weaponModel) {
      return { hasWeapon: false };
    }

    try {
      // 计算武器的包围盒
      const box = new THREE.Box3().setFromObject(this.weaponModel);
      const size = new THREE.Vector3();
      box.getSize(size);
      
      const weaponLength = Math.max(size.x, size.y, size.z);
      
      return {
        hasWeapon: true,
        length: weaponLength,
        size: size,
        position: this.weaponModel.position.clone(),
        rotation: this.weaponModel.rotation.clone(),
        visible: this.weaponModel.visible
      };
    } catch (error) {
      console.error('获取武器信息时出错:', error);
      return { 
        hasWeapon: true,
        length: 1.0 // 出错时返回默认长度
      };
    }
  }
} 