import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface RockMeshInfo {
  mesh: THREE.Mesh;
  name: string;
  originalPosition: THREE.Vector3;
  originalRotation: THREE.Euler;
  originalScale: THREE.Vector3;
  visible: boolean;
}

export interface RocksConfig {
  name: string;
  modelPath: string;
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
  materialProperties: {
    mainColor: THREE.Color;
    roughness: number;
    metalness: number;
  };
}

export class RocksModel {
  private config: RocksConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;

  // 多网格管理
  private rockMeshes: RockMeshInfo[] = [];
  private selectedMeshIndex: number = -1; // -1表示选择整体

  // 事件回调
  private onLoadComplete?: () => void;
  private onMaterialApplied?: () => void;
  private onMeshSelectionChanged?: (meshIndex: number) => void;

  constructor(scene: THREE.Scene, config?: Partial<RocksConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置 - 基于meta文件信息
    this.config = {
      name: 'RocksBlock',
      modelPath: '/src/models/Rocks/SS_Rock_01.fbx',
      scale: 1.0,
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      materialProperties: {
        // 基于meta文件: mainColor RGB(148,148,148) = 0x949494
        mainColor: new THREE.Color(0x949494),
        roughness: 0.75,  // 直接来自meta文件
        metalness: 0.1    // 岩石通常金属度较低
      },
      ...config
    };
    
    this.loader = new ModelLoader();
    
    console.log('🪨 RocksModel 初始化:', {
      name: this.config.name,
      modelPath: this.config.modelPath,
      materialColor: `#${this.config.materialProperties.mainColor.getHexString()}`,
      roughness: this.config.materialProperties.roughness,
      metalness: this.config.materialProperties.metalness
    });
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onMaterialApplied?: () => void;
    onMeshSelectionChanged?: (meshIndex: number) => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onMaterialApplied = callbacks.onMaterialApplied;
    this.onMeshSelectionChanged = callbacks.onMeshSelectionChanged;
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 开始加载岩石模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();

      // 预处理模型
      this.loader.preprocessModel(this.model);

      // 解析并收集所有网格
      this.parseRockMeshes();

      // 应用岩石材质属性
      this.applyRockMaterialProperties();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('✅ 岩石模型加载完成');
    } catch (error) {
      console.error('❌ 岩石模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型的基本属性
   */
  private setupModelProperties(): void {
    this.model.name = this.config.name;
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
  }

  /**
   * 解析并收集FBX模型中的所有网格
   */
  private parseRockMeshes(): void {
    this.rockMeshes = [];
    let meshIndex = 0;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 默认只显示第一个网格，其他都隐藏
        const shouldBeVisible = meshIndex === 0;
        child.visible = shouldBeVisible;

        const rockMesh: RockMeshInfo = {
          mesh: child,
          name: child.name || `Rock_${meshIndex + 1}`,
          originalPosition: child.position.clone(),
          originalRotation: child.rotation.clone(),
          originalScale: child.scale.clone(),
          visible: shouldBeVisible
        };

        this.rockMeshes.push(rockMesh);
        meshIndex++;

        console.log(`🪨 发现网格 ${meshIndex}:`, {
          name: rockMesh.name,
          position: rockMesh.originalPosition,
          scale: rockMesh.originalScale,
          visible: rockMesh.visible,
          defaultVisible: shouldBeVisible
        });
      }
    });

    console.log(`✅ 共发现 ${this.rockMeshes.length} 个岩石网格，默认只显示第一个`);
  }

  /**
   * 应用岩石材质属性 - 基于meta文件配置
   */
  private applyRockMaterialProperties(): void {
    if (!this.model) {
      console.warn('⚠️ 模型未加载，无法应用材质');
      return;
    }

    let materialCount = 0;
    
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            // 应用meta文件中的材质属性
            material.color = this.config.materialProperties.mainColor.clone();
            material.roughness = this.config.materialProperties.roughness;
            material.metalness = this.config.materialProperties.metalness;
            
            // 岩石材质特性
            material.transparent = false;  // 岩石不需要透明
            material.side = THREE.FrontSide;  // 岩石通常不需要双面渲染
            material.emissive = new THREE.Color(0x000000);
            material.emissiveIntensity = 0.0;
            
            materialCount++;

            console.log('🎨 应用岩石材质:', {
              color: `#${material.color.getHexString()}`,
              roughness: material.roughness,
              metalness: material.metalness,
              transparent: material.transparent,
              side: material.side === THREE.FrontSide ? 'FrontSide' : 'DoubleSide'
            });
          }
        });
        
        // 设置阴影属性
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    console.log(`✅ 已处理 ${materialCount} 个岩石材质`);
    
    if (this.onMaterialApplied) {
      this.onMaterialApplied();
    }
  }

  /**
   * 设置模型位置
   */
  setPosition(position: THREE.Vector3): void {
    this.config.position = position;
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置模型缩放
   */
  setScale(scale: number): void {
    this.config.scale = scale;
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 设置模型旋转
   */
  setRotation(rotation: THREE.Euler): void {
    this.config.rotation = rotation;
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 更新材质颜色
   */
  updateMaterialColor(color: THREE.Color): void {
    this.config.materialProperties.mainColor = color;
    
    if (this.model) {
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const materials = Array.isArray(child.material) ? child.material : [child.material];
          materials.forEach((material) => {
            if (material instanceof THREE.MeshStandardMaterial) {
              material.color = color.clone();
            }
          });
        }
      });
    }
  }

  /**
   * 更新材质粗糙度
   */
  updateRoughness(roughness: number): void {
    this.config.materialProperties.roughness = roughness;
    
    if (this.model) {
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const materials = Array.isArray(child.material) ? child.material : [child.material];
          materials.forEach((material) => {
            if (material instanceof THREE.MeshStandardMaterial) {
              material.roughness = roughness;
            }
          });
        }
      });
    }
  }

  /**
   * 更新材质金属度
   */
  updateMetalness(metalness: number): void {
    this.config.materialProperties.metalness = metalness;
    
    if (this.model) {
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const materials = Array.isArray(child.material) ? child.material : [child.material];
          materials.forEach((material) => {
            if (material instanceof THREE.MeshStandardMaterial) {
              material.metalness = metalness;
            }
          });
        }
      });
    }
  }

  /**
   * 获取模型对象
   */
  getModel(): THREE.Group | null {
    return this.isLoaded ? this.model : null;
  }

  /**
   * 获取配置信息
   */
  getConfig(): RocksConfig {
    return { ...this.config };
  }

  /**
   * 检查是否已加载
   */
  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('❌ 模型未加载');
      return;
    }

    console.log('=== 🪨 Rocks Material Debug ===');
    
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        console.log(`网格: ${child.name}`);
        
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material, index) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            console.log(`  材质 ${index}:`, {
              color: `#${material.color.getHexString()}`,
              roughness: material.roughness,
              metalness: material.metalness,
              transparent: material.transparent,
              side: material.side === THREE.DoubleSide ? 'DoubleSide' : 'FrontSide'
            });
          }
        });
      }
    });
  }

  // ==================== 多网格控制方法 ====================

  /**
   * 获取所有岩石网格信息
   */
  getRockMeshes(): RockMeshInfo[] {
    return [...this.rockMeshes];
  }

  /**
   * 获取当前选中的网格索引
   */
  getSelectedMeshIndex(): number {
    return this.selectedMeshIndex;
  }

  /**
   * 选择特定的网格进行操作
   * @param meshIndex 网格索引，-1表示选择整体
   */
  selectMesh(meshIndex: number): void {
    if (meshIndex < -1 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    this.selectedMeshIndex = meshIndex;

    if (this.onMeshSelectionChanged) {
      this.onMeshSelectionChanged(meshIndex);
    }

    console.log(`🎯 选中网格: ${meshIndex === -1 ? '整体' : this.rockMeshes[meshIndex].name}`);
  }

  /**
   * 设置指定网格的位置
   */
  setMeshPosition(meshIndex: number, position: THREE.Vector3): void {
    if (meshIndex === -1) {
      // 整体位置
      this.setPosition(position);
      return;
    }

    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    rockMesh.mesh.position.copy(position);

    console.log(`📍 设置网格 ${rockMesh.name} 位置:`, position);
  }

  /**
   * 设置指定网格的旋转
   */
  setMeshRotation(meshIndex: number, rotation: THREE.Euler): void {
    if (meshIndex === -1) {
      // 整体旋转
      this.setRotation(rotation);
      return;
    }

    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    rockMesh.mesh.rotation.copy(rotation);

    console.log(`🔄 设置网格 ${rockMesh.name} 旋转:`, rotation);
  }

  /**
   * 设置指定网格的缩放
   */
  setMeshScale(meshIndex: number, scale: number): void {
    if (meshIndex === -1) {
      // 整体缩放
      this.setScale(scale);
      return;
    }

    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    rockMesh.mesh.scale.setScalar(scale);

    console.log(`📏 设置网格 ${rockMesh.name} 缩放:`, scale);
  }

  /**
   * 设置指定网格的可见性
   */
  setMeshVisibility(meshIndex: number, visible: boolean): void {
    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    rockMesh.mesh.visible = visible;
    rockMesh.visible = visible;

    console.log(`👁️ 设置网格 ${rockMesh.name} 可见性:`, visible);
  }

  /**
   * 设置指定网格的材质属性
   */
  setMeshMaterialProperties(meshIndex: number, properties: {
    color?: THREE.Color;
    roughness?: number;
    metalness?: number;
  }): void {
    if (meshIndex === -1) {
      // 应用到所有网格
      this.rockMeshes.forEach((_, index) => {
        this.setMeshMaterialProperties(index, properties);
      });
      return;
    }

    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    const materials = Array.isArray(rockMesh.mesh.material) ?
      rockMesh.mesh.material : [rockMesh.mesh.material];

    materials.forEach((material) => {
      if (material instanceof THREE.MeshStandardMaterial) {
        if (properties.color) {
          material.color = properties.color.clone();
        }
        if (properties.roughness !== undefined) {
          material.roughness = properties.roughness;
        }
        if (properties.metalness !== undefined) {
          material.metalness = properties.metalness;
        }
      }
    });

    console.log(`🎨 设置网格 ${rockMesh.name} 材质属性:`, properties);
  }

  /**
   * 重置指定网格到原始状态
   */
  resetMesh(meshIndex: number): void {
    if (meshIndex === -1) {
      // 重置整体
      this.setPosition(new THREE.Vector3(0, 0, 0));
      this.setRotation(new THREE.Euler(0, 0, 0));
      this.setScale(1.0);
      return;
    }

    if (meshIndex < 0 || meshIndex >= this.rockMeshes.length) {
      console.warn(`⚠️ 无效的网格索引: ${meshIndex}`);
      return;
    }

    const rockMesh = this.rockMeshes[meshIndex];
    rockMesh.mesh.position.copy(rockMesh.originalPosition);
    rockMesh.mesh.rotation.copy(rockMesh.originalRotation);
    rockMesh.mesh.scale.copy(rockMesh.originalScale);
    rockMesh.mesh.visible = rockMesh.visible;

    console.log(`🔄 重置网格 ${rockMesh.name} 到原始状态`);
  }

  /**
   * 重置所有网格的可见性到默认状态（只显示第一个）
   */
  resetMeshVisibility(): void {
    this.rockMeshes.forEach((rockMesh, index) => {
      const shouldBeVisible = index === 0;
      rockMesh.mesh.visible = shouldBeVisible;
      rockMesh.visible = shouldBeVisible;
    });

    console.log('🔄 已重置网格可见性到默认状态（只显示第一个）');
  }

  /**
   * 销毁资源
   */
  dispose(): void {
    if (this.model) {
      // 清理材质和几何体
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }

          const materials = Array.isArray(child.material) ? child.material : [child.material];
          materials.forEach((material) => {
            material.dispose();
          });
        }
      });

      // 从场景中移除
      this.scene.remove(this.model);
      console.log('🗑️ RocksModel 资源已清理');
    }

    // 清理网格信息
    this.rockMeshes = [];
    this.selectedMeshIndex = -1;
    this.isLoaded = false;
  }
}
