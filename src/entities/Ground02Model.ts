import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: string;
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface Ground02Config {
  name: string;
  modelPath: string;
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
  materialProperties: {
    mainColor: THREE.Color;
    roughness: number;
    metalness: number;
  };
}

export interface Ground02EventCallbacks {
  onLoadComplete?: () => void;
  onMaterialApplied?: () => void;
}

/**
 * Ground02Model - 专门处理Ground_02目录中的地面模型系统
 * 基于Ground01Model架构，专门针对Plane.018网格进行优化处理
 * 
 * 主要特性：
 * - 专门处理Plane.018网格，与Ground01Model保持一致的网格选择逻辑
 * - 基于M_BaseGroundColor_02材质配置
 * - 使用SS_Ground_02_D.png纹理
 * - 完整的PBR材质系统支持
 * - 事件驱动的加载和材质应用
 * - 丰富的控制接口（位置、旋转、缩放、可见性等）
 */
export class Ground02Model {
  private scene: THREE.Scene;
  private config: Ground02Config;
  private loader: ModelLoader;
  private textureLoader: THREE.TextureLoader;
  
  private model: THREE.Group | null = null;
  private targetMesh: THREE.Mesh | null = null;  // 专门针对Plane.018网格
  private isLoaded: boolean = false;
  
  // 事件回调
  private onLoadComplete?: () => void;
  private onMaterialApplied?: () => void;

  // Ground02模型的默认纹理配置 - 基于M_BaseGroundColor_02和SS_Ground_02_D.png
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'diffuse',
      path: '/src/models/Ground_02/SS_Ground_02_D.png',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  constructor(scene: THREE.Scene, config?: Partial<Ground02Config>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置 - 基于M_BaseGroundColor_02材质配置
    this.config = {
      name: 'Ground02',
      modelPath: '/src/models/Ground_02/SS_Ground_01.fbx',
      scale: 0.1,
      position: new THREE.Vector3(0, 0.01, 0),  // 稍微高一点，贴在默认地面上
      rotation: new THREE.Euler(0, 0, 0),
      materialProperties: {
        // Ground02材质：基于M_BaseGroundColor_02的默认配置
        mainColor: new THREE.Color(0xFFFFFF),  // 白色基础色，让纹理颜色主导
        roughness: 1.0,  // 地面通常粗糙度较高
        metalness: 0.0   // 地面通常无金属度
      },
      ...config
    };

    this.loader = new ModelLoader();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: Ground02EventCallbacks): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onMaterialApplied = callbacks.onMaterialApplied;
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 开始加载Ground02模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();

      // 预处理模型
      this.loader.preprocessModel(this.model);

      // 找到目标网格（专门查找Plane.018）
      this.findTargetMesh();

      // 应用Ground02材质属性和贴图
      await this.applyGround02MaterialProperties();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('✅ Ground02模型加载完成');
    } catch (error) {
      console.error('❌ Ground02模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型基础属性
   */
  private setupModelProperties(): void {
    if (!this.model) return;

    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
    this.model.scale.setScalar(this.config.scale);
  }

  /**
   * 找到目标网格 - 专门查找Plane.018网格
   */
  private findTargetMesh(): void {
    let meshFound = false;

    this.model!.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 专门查找名称为"Plane.018"的网格
        if (child.name === 'Plane.018') {
          this.targetMesh = child;
          meshFound = true;

          // 确保目标网格可见
          child.visible = true;
          child.castShadow = true;
          child.receiveShadow = true;

          console.log(`🎯 找到目标网格 Plane.018:`, {
            name: child.name,
            position: child.position,
            scale: child.scale,
            triangles: child.geometry.attributes.position?.count / 3 || 0
          });
        } else {
          // 隐藏其他网格，只显示Plane.018
          child.visible = false;
          console.log(`🙈 隐藏网格: ${child.name || 'Unnamed'}`);
        }
      }
    });

    if (!meshFound) {
      console.warn('⚠️ 未找到Plane.018网格，尝试使用第一个可用网格...');
      // 如果没找到Plane.018，使用第一个网格作为备选
      this.model!.traverse((child) => {
        if (child instanceof THREE.Mesh && !meshFound) {
          this.targetMesh = child;
          meshFound = true;
          child.visible = true;
          child.castShadow = true;
          child.receiveShadow = true;
          console.log(`🔄 使用备选网格: ${child.name || 'Unnamed'}`);
        }
      });
    }

    if (!meshFound) {
      throw new Error('❌ 未找到任何可用的网格');
    }

    console.log('✅ 目标网格设置完成');
  }

  /**
   * 应用Ground02材质属性和贴图
   */
  private async applyGround02MaterialProperties(): Promise<void> {
    if (!this.targetMesh) {
      throw new Error('❌ 目标网格未找到，无法应用材质');
    }

    console.log('🎨 开始应用Ground02材质属性...');

    // 加载纹理
    const textures = await this.loadTextures();

    // 创建PBR材质
    const material = new THREE.MeshStandardMaterial({
      color: this.config.materialProperties.mainColor,
      roughness: this.config.materialProperties.roughness,
      metalness: this.config.materialProperties.metalness,
      map: textures.diffuse || null
    });

    // 应用材质到目标网格
    this.targetMesh.material = material;

    console.log('✅ Ground02材质属性应用完成');
    
    if (this.onMaterialApplied) {
      this.onMaterialApplied();
    }

    // 输出调试信息
    this.logMaterialInfo();
  }

  /**
   * 加载纹理文件
   */
  private async loadTextures(): Promise<Record<string, THREE.Texture>> {
    const textures: Record<string, THREE.Texture> = {};

    for (const textureFile of Ground02Model.DEFAULT_TEXTURES) {
      try {
        console.log(`🔄 加载贴图: ${textureFile.name} from ${textureFile.path}`);
        
        const texture = await new Promise<THREE.Texture>((resolve, reject) => {
          this.textureLoader.load(
            textureFile.path,
            (loadedTexture) => resolve(loadedTexture),
            undefined,
            (error) => reject(error)
          );
        });

        // 配置贴图属性
        if (textureFile.colorSpace) {
          texture.colorSpace = textureFile.colorSpace;
        }
        if (textureFile.flipY !== undefined) {
          texture.flipY = textureFile.flipY;
        }
        if (textureFile.wrapS) {
          texture.wrapS = textureFile.wrapS;
        }
        if (textureFile.wrapT) {
          texture.wrapT = textureFile.wrapT;
        }
        if (textureFile.repeat) {
          texture.repeat.set(textureFile.repeat.x, textureFile.repeat.y);
        }

        texture.needsUpdate = true;
        textures[textureFile.name] = texture;

        console.log(`✅ 贴图加载成功: ${textureFile.name}`);
      } catch (error) {
        console.error(`❌ 贴图加载失败: ${textureFile.name}`, error);
        // 继续加载其他贴图，不中断整个过程
      }
    }

    return textures;
  }

  // ==================== 公共控制方法 ====================

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
      this.config.position.copy(position);
    }
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
      this.config.rotation.copy(rotation);
    }
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
      this.config.scale = scale;
    }
  }

  /**
   * 设置可见性
   */
  setVisibility(visible: boolean): void {
    if (this.model) {
      this.model.visible = visible;
    }
  }

  /**
   * 更新材质颜色
   */
  updateMaterialColor(color: THREE.Color): void {
    if (this.targetMesh && this.targetMesh.material instanceof THREE.MeshStandardMaterial) {
      this.targetMesh.material.color.copy(color);
      this.config.materialProperties.mainColor.copy(color);
    }
  }

  /**
   * 更新材质粗糙度
   */
  updateMaterialRoughness(roughness: number): void {
    if (this.targetMesh && this.targetMesh.material instanceof THREE.MeshStandardMaterial) {
      this.targetMesh.material.roughness = roughness;
      this.config.materialProperties.roughness = roughness;
    }
  }

  /**
   * 更新材质金属度
   */
  updateMaterialMetalness(metalness: number): void {
    if (this.targetMesh && this.targetMesh.material instanceof THREE.MeshStandardMaterial) {
      this.targetMesh.material.metalness = metalness;
      this.config.materialProperties.metalness = metalness;
    }
  }

  /**
   * 重置到默认状态
   */
  reset(): void {
    this.setPosition(new THREE.Vector3(0, 0.01, 0));  // 与默认配置保持一致
    this.setRotation(new THREE.Euler(0, 0, 0));
    this.setScale(1.0);
    this.setVisibility(true);

    // 重置材质属性
    this.updateMaterialColor(new THREE.Color(0xFFFFFF));
    this.updateMaterialRoughness(1.0);
    this.updateMaterialMetalness(0.0);
  }

  // ==================== 信息获取方法 ====================

  /**
   * 获取当前位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : new THREE.Vector3();
  }

  /**
   * 获取当前旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : new THREE.Euler();
  }

  /**
   * 获取当前缩放
   */
  getScale(): number {
    return this.config.scale;
  }

  /**
   * 获取加载状态
   */
  getLoadStatus(): boolean {
    return this.isLoaded;
  }

  /**
   * 输出材质调试信息
   */
  private logMaterialInfo(): void {
    if (!this.targetMesh) return;

    console.log('🎨 Ground02材质信息:');
    console.log('网格名称:', this.targetMesh.name);

    if (this.targetMesh.material instanceof THREE.MeshStandardMaterial) {
      const material = this.targetMesh.material;
      console.log('材质属性:', {
        color: `#${material.color.getHexString()}`,
        roughness: material.roughness,
        metalness: material.metalness,
        hasTexture: !!material.map
      });

      if (material.map) {
        console.log('贴图信息:', {
          wrapS: material.map.wrapS === THREE.RepeatWrapping ? 'Repeat' : 'Clamp',
          wrapT: material.map.wrapT === THREE.RepeatWrapping ? 'Repeat' : 'Clamp',
          repeat: `${material.map.repeat.x}x${material.map.repeat.y}`,
          flipY: material.map.flipY,
          colorSpace: material.map.colorSpace
        });
      }
    }

    console.log('几何体信息:', {
      vertices: this.targetMesh.geometry.attributes.position?.count || 0,
      triangles: (this.targetMesh.geometry.attributes.position?.count || 0) / 3,
      hasUV: !!this.targetMesh.geometry.attributes.uv,
      hasNormals: !!this.targetMesh.geometry.attributes.normal
    });
  }

  /**
   * 获取模型状态信息
   */
  getModelInfo(): any {
    if (!this.isLoaded || !this.targetMesh) {
      return {
        isLoaded: false,
        meshName: 'Not loaded',
        vertices: 0,
        triangles: 0,
        hasTexture: false,
        materialProperties: {
          color: '#FFFFFF',
          roughness: 1.0,
          metalness: 0.0
        }
      };
    }

    const material = this.targetMesh.material as THREE.MeshStandardMaterial;
    const vertices = this.targetMesh.geometry.attributes.position?.count || 0;

    return {
      isLoaded: true,
      meshName: this.targetMesh.name || 'Unnamed',
      vertices,
      triangles: Math.floor(vertices / 3),
      hasTexture: !!material.map,
      materialProperties: {
        color: `#${material.color.getHexString()}`,
        roughness: material.roughness,
        metalness: material.metalness
      }
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
                if (mat.map) mat.map.dispose();
                mat.dispose();
              });
            } else {
              if (child.material.map) child.material.map.dispose();
              child.material.dispose();
            }
          }
        }
      });

      // 从场景中移除
      this.scene.remove(this.model);
      this.model = null;
    }

    this.targetMesh = null;
    this.isLoaded = false;

    console.log('🗑️ Ground02Model 资源已清理');
  }
}
