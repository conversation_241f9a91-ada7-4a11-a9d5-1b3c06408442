import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface OreConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export class OreModel {
  private config: OreConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 基于meta文件分析的矿石纹理配置
  // 暂时禁用纹理，使用纯色效果来匹配原项目的光滑外观
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    // {
    //   name: 'oreDiffuse',
    //   path: '/src/models/Ore/M_SS_Ore_01_D.jpg',
    //   materialProperty: 'map',
    //   colorSpace: THREE.SRGBColorSpace,
    //   flipY: true,  // 基于meta文件分析，纹理不需要翻转
    //   wrapS: THREE.RepeatWrapping,  // meta文件中wrapModeS: "repeat"
    //   wrapT: THREE.RepeatWrapping,  // meta文件中wrapModeT: "repeat"
    //   repeat: { x: 1, y: 1 }  // 先尝试1x1，减少纹理重复效果
    // }
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onTextureLoaded?: () => void;

  constructor(scene: THREE.Scene, config?: Partial<OreConfig>) {
    this.scene = scene;
    this.textureLoader = new THREE.TextureLoader();

    // 安全地合并配置，确保 position 和 rotation 总是有效的 Three.js 对象
    this.config = {
      name: config?.name || 'OreModel',
      modelPath: config?.modelPath || '/src/models/Ore/SS_Ore_01.fbx',
      textures: config?.textures || OreModel.DEFAULT_TEXTURES,
      scale: config?.scale || 0.01,
      position: config?.position || new THREE.Vector3(0, 0, 0),
      rotation: config?.rotation || new THREE.Euler(0, 0, 0)
    };

    this.loader = new ModelLoader();
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onTextureLoaded?: () => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onTextureLoaded = callbacks.onTextureLoaded;
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔶 开始加载矿石模型...');

      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;

      // 应用变换
      this.applyTransforms();

      // 预先应用基础材质属性（确保所有材质都有正确的基础设置）
      this.applyBaseMaterialProperties();

      // 加载纹理
      await this.loadTextures();
      
      // 添加到场景
      this.scene.add(this.model);
      this.isLoaded = true;
      
      console.log('✅ 矿石模型加载完成');
      
      // 触发加载完成回调
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
    } catch (error) {
      console.error('❌ 矿石模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 应用变换
   */
  private applyTransforms(): void {
    if (!this.model) return;

    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
    this.model.scale.setScalar(this.config.scale);
  }

  /**
   * 预先应用基础材质属性到所有材质
   */
  private applyBaseMaterialProperties(): void {
    if (!this.model) return;

    console.log('🔶 预先应用基础材质属性到所有材质');

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material, index) => {
          console.log(`🔶 处理材质 ${index + 1}:`, {
            name: child.name,
            materialType: material.type,
            hasMap: !!(material as any).map
          });

          // 应用基础矿石材质属性
          this.applyOreMaterialProperties(material);
        });
      }
    });
  }

  /**
   * 加载纹理
   */
  private async loadTextures(): Promise<void> {
    if (!this.model) return;

    const texturePromises = this.config.textures.map(async (textureConfig) => {
      try {
        const texture = await this.loadTexture(textureConfig);
        this.applyTextureToModel(texture, textureConfig);
        
        if (this.onTextureLoaded) {
          this.onTextureLoaded();
        }
        
      } catch (error) {
        console.error(`❌ 纹理加载失败: ${textureConfig.path}`, error);
      }
    });

    await Promise.all(texturePromises);
  }

  /**
   * 加载单个纹理
   */
  private loadTexture(config: TextureFile): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        config.path,
        (texture) => {
          // 配置纹理属性
          if (config.colorSpace) texture.colorSpace = config.colorSpace;
          if (config.flipY !== undefined) texture.flipY = config.flipY;
          if (config.wrapS) texture.wrapS = config.wrapS;
          if (config.wrapT) texture.wrapT = config.wrapT;
          if (config.repeat) {
            texture.repeat.set(config.repeat.x, config.repeat.y);
          }
          
          texture.needsUpdate = true;
          console.log(`✅ 纹理加载成功: ${config.name}`);
          resolve(texture);
        },
        (progress) => {
          console.log(`📥 纹理加载中: ${config.name} - ${Math.round((progress.loaded / progress.total) * 100)}%`);
        },
        (error) => {
          console.error(`❌ 纹理加载失败: ${config.name}`, error);
          reject(error);
        }
      );
    });
  }

  /**
   * 将纹理应用到模型
   */
  private applyTextureToModel(texture: THREE.Texture, config: TextureFile): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          this.applyTextureToMaterial(material, texture, config);
        });
      }
    });
  }

  /**
   * 将纹理应用到材质
   */
  private applyTextureToMaterial(material: THREE.Material, texture: THREE.Texture, config: TextureFile): void {
    // 检查材质类型并应用纹理
    if (material instanceof THREE.MeshStandardMaterial ||
        material instanceof THREE.MeshPhongMaterial ||
        material instanceof THREE.MeshLambertMaterial ||
        material instanceof THREE.MeshBasicMaterial) {

      console.log(`🔶 应用纹理到材质: ${config.name}`, {
        materialType: material.type,
        textureSize: `${texture.image?.width}x${texture.image?.height}`,
        textureRepeat: `${texture.repeat.x}x${texture.repeat.y}`,
        colorSpace: texture.colorSpace
      });

      // 根据配置的材质属性应用纹理
      switch (config.materialProperty) {
        case 'map':
          // 先应用基础材质属性
          this.applyOreMaterialProperties(material);
          // 然后设置纹理
          material.map = texture;

          console.log(`🔶 纹理应用完成:`, {
            hasTexture: !!material.map,
            materialColor: material.color.getHexString(),
            roughness: material instanceof THREE.MeshStandardMaterial ? material.roughness : 'N/A',
            metalness: material instanceof THREE.MeshStandardMaterial ? material.metalness : 'N/A'
          });
          break;
        default:
          console.warn(`Unknown material property: ${config.materialProperty}`);
      }

      // 标记材质需要更新
      material.needsUpdate = true;
    } else {
      console.warn(`Material type ${material.type} not supported for texture application`);
    }
  }

  /**
   * 应用矿石材质属性 - 基于Cocos Creator截图的精确配置
   */
  private applyOreMaterialProperties(material: THREE.Material): void {
    console.log(`🔶 开始应用矿石材质属性到: ${material.type}`);

    // 基于Cocos Creator截图的精确材质配置
    // Albedo: FFB800 (亮黄色) - 来自截图的精确颜色值
    // Albedo Scale: 3, 3, 3 - 颜色需要放大3倍
    const baseAlbedoColor = new THREE.Color(0xFFB800);
    const albedoScale = 3.0;  // Cocos Creator中的Albedo Scale
    const albedoColor = baseAlbedoColor.clone().multiplyScalar(albedoScale);

    // 支持多种材质类型
    if (material instanceof THREE.MeshStandardMaterial) {
      // PBR材质 - 完整的PBR属性支持
      material.color.copy(albedoColor);
      material.roughness = 0.5;                    // Cocos Roughness: 0.5
      material.metalness = 0.5;                    // Cocos Metallic: 0.5
      material.emissive.setHex(0x000000);
      material.emissiveIntensity = 0.0;

      console.log('🔶 应用PBR材质属性 (MeshStandardMaterial)');

    } else if (material instanceof THREE.MeshPhongMaterial) {
      // Phong材质 - 应用基础颜色和光照属性，打造光滑金属效果
      material.color.copy(albedoColor);
      material.emissive.setHex(0x000000);
      material.shininess = 100;                    // 高光泽度，让表面更光滑
      material.specular.setHex(0x444444);          // 增强镜面反射，模拟金属光泽

      console.log('🔶 应用Phong材质属性 (MeshPhongMaterial) - 光滑金属效果');

    } else if (material instanceof THREE.MeshLambertMaterial) {
      // Lambert材质 - 应用基础颜色
      material.color.copy(albedoColor);
      material.emissive.setHex(0x000000);

      console.log('🔶 应用Lambert材质属性 (MeshLambertMaterial)');

    } else if (material instanceof THREE.MeshBasicMaterial) {
      // Basic材质 - 应用基础颜色
      material.color.copy(albedoColor);

      console.log('🔶 应用Basic材质属性 (MeshBasicMaterial)');
    }

    // 通用材质设置
    material.transparent = false;
    material.alphaTest = 0.0;
    material.side = THREE.FrontSide;
    material.opacity = 1.0;
    material.needsUpdate = true;

    console.log('🔶 矿石材质属性应用完成:', {
      materialType: material.type,
      baseAlbedoColor: baseAlbedoColor.getHexString(),
      albedoScale: albedoScale,
      finalAlbedoColor: (material as any).color?.getHexString() || 'N/A',
      finalAlbedoRGB: (material as any).color ?
        `rgb(${Math.round((material as any).color.r * 255)}, ${Math.round((material as any).color.g * 255)}, ${Math.round((material as any).color.b * 255)})` : 'N/A',
      roughness: (material as any).roughness || 'N/A',
      metalness: (material as any).metalness || 'N/A',
      shininess: (material as any).shininess || 'N/A',
      emissive: (material as any).emissive?.getHexString() || 'N/A',
      transparent: material.transparent,
      opacity: material.opacity,
      needsUpdate: material.needsUpdate
    });
  }

  // ==================== 公共接口方法 ====================

  /**
   * 获取模型对象
   */
  getModel(): THREE.Group | null {
    return this.isLoaded ? this.model : null;
  }

  /**
   * 检查模型是否已加载
   */
  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
      this.config.position.copy(position);
    }
  }

  /**
   * 获取位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : this.config.position.clone();
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
      this.config.rotation.copy(rotation);
    }
  }

  /**
   * 获取旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : this.config.rotation.clone();
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
      this.config.scale = scale;
    }
  }

  /**
   * 获取缩放
   */
  getScale(): number {
    return this.config.scale;
  }

  /**
   * 设置粗糙度
   */
  setRoughness(roughness: number): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            material.roughness = roughness;
            material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 设置金属度
   */
  setMetalness(metalness: number): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          if (material instanceof THREE.MeshStandardMaterial) {
            material.metalness = metalness;
            material.needsUpdate = true;
          }
        });
      }
    });
  }

  /**
   * 获取模型状态
   */
  getStatus(): {
    name: string;
    loaded: boolean;
    position: THREE.Vector3;
    rotation: THREE.Euler;
    scale: number;
    roughness: number;
    metalness: number;
  } {
    const materialProps = this.getMaterialProperties();
    return {
      name: this.config.name,
      loaded: this.isLoaded,
      position: this.getPosition(),
      rotation: this.getRotation(),
      scale: this.getScale(),
      roughness: materialProps.roughness,
      metalness: materialProps.metalness
    };
  }

  /**
   * 获取材质属性
   */
  private getMaterialProperties(): { roughness: number; metalness: number } {
    if (!this.model) return { roughness: 0.5, metalness: 0.5 };

    // 查找第一个 MeshStandardMaterial
    let roughness = 0.5;
    let metalness = 0.5;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        for (const material of materials) {
          if (material instanceof THREE.MeshStandardMaterial) {
            roughness = material.roughness;
            metalness = material.metalness;
            return; // 找到第一个就退出
          }
        }
      }
    });

    return { roughness, metalness };
  }

  /**
   * 重置材质属性为矿石默认值
   */
  resetMaterialProperties(): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          this.applyOreMaterialProperties(material);
        });
      }
    });
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('🔶 矿石模型未加载');
      return;
    }

    console.log('🔶 矿石模型材质调试信息:');
    let materialCount = 0;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];
        materials.forEach((material) => {
          materialCount++;
          console.log(`  材质 ${materialCount}:`, {
            name: child.name,
            type: material.type,
            color: material instanceof THREE.MeshStandardMaterial ? material.color.getHexString() : 'N/A',
            roughness: material instanceof THREE.MeshStandardMaterial ? material.roughness : 'N/A',
            metalness: material instanceof THREE.MeshStandardMaterial ? material.metalness : 'N/A',
            hasMap: !!(material as any).map,
            transparent: material.transparent,
            opacity: material.opacity
          });
        });
      }
    });

    console.log(`🔶 总计 ${materialCount} 个材质`);
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            const materials = Array.isArray(child.material) ? child.material : [child.material];
            materials.forEach((material) => {
              // 清理纹理
              Object.values(material).forEach((value) => {
                if (value instanceof THREE.Texture) {
                  value.dispose();
                }
              });
              material.dispose();
            });
          }
        }
      });

      // 从场景中移除
      this.scene.remove(this.model);
    }

    this.isLoaded = false;
    console.log('🔶 矿石模型资源已清理');
  }
}
