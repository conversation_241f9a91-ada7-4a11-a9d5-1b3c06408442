import * as THREE from 'three';
import { ModelLoader, AnimationFile } from '../loaders/FBXLoader.js';
import { AnimationController } from '../controllers/AnimationController.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface BearConfig {
  name: string;
  modelPath: string;
  animations: AnimationFile[];
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export enum BearState {
  IDLE = 'idle',
  WALK = 'walk',
  ATTACK = 'attack',
  DEATH = 'death'
}

export class BearModel {
  private config: BearConfig;
  private loader: ModelLoader;
  private animationController!: AnimationController;
  private model!: THREE.Group;
  private mixer!: THREE.AnimationMixer;
  private clock: THREE.Clock;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 默认熊动画配置
  private static readonly DEFAULT_ANIMATIONS: AnimationFile[] = [
    {
      name: BearState.IDLE,
      path: '/src/models/Bear/action/<EMAIL>', // 使用walk动画作为idle
      loop: true,
      priority: 0
    },
    {
      name: BearState.WALK,
      path: '/src/models/Bear/action/<EMAIL>',
      loop: true,
      priority: 1
    },
    {
      name: BearState.ATTACK,
      path: '/src/models/Bear/action/<EMAIL>',
      loop: false,
      priority: 2
    },
    {
      name: BearState.DEATH,
      path: '/src/models/Bear/action/<EMAIL>',
      loop: false,
      priority: 3
    }
  ];

  // 默认熊纹理配置
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'bearDiffuse',
      path: '/src/models/Bear/Tex/M_SS_Bear_01_D.png',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 状态管理
  private health = 100;
  private maxHealth = 100;
  private isAlive = true;
  private currentBearState: BearState = BearState.IDLE;
  private isWalking = false;
  private walkDirection = new THREE.Vector3(1, 0, 0);
  private walkSpeed = 2;
  private walkBounds = { min: -5, max: 5 };
  
  // 攻击系统
  private attackRange = 3.0; // 攻击检测范围
  private attackCooldown = 2000; // 攻击冷却时间(ms)
  private lastAttackTime = 0;
  private currentTarget: THREE.Object3D | null = null;
  private targets: THREE.Object3D[] = []; // 可攻击的目标列表
  private isChasing = false;
  private originalWalkSpeed = 2;
  private chaseSpeed = 3; // 追击速度
  private activeTimers: NodeJS.Timeout[] = []; // 追踪活动的定时器
  
  // 事件回调
  private onLoadComplete?: () => void;
  private onAnimationChange?: (newState: BearState) => void;
  private onDeath?: () => void;
  private onDeathAnimationComplete?: () => void;
  private onWalkDirectionChange?: (direction: THREE.Vector3) => void;
  private onTextureLoaded?: () => void;
  private onTargetDetected?: (target: THREE.Object3D) => void;
  private onTargetLost?: () => void;
  private onAttackTarget?: (target: THREE.Object3D) => void;

  constructor(scene: THREE.Scene, config?: Partial<BearConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'Bear',
      modelPath: '/src/models/Bear/action/<EMAIL>',
      animations: BearModel.DEFAULT_ANIMATIONS,
      textures: BearModel.DEFAULT_TEXTURES,
      scale: 0.015,  // 调整为更小的尺寸，相对于人更协调
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.clock = new THREE.Clock();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 创建标准熊实例的快捷方法
   */
  static create(scene: THREE.Scene, name = 'Bear'): BearModel {
    return new BearModel(scene, { name });
  }

  /**
   * 创建自定义位置的熊实例
   */
  static createAt(scene: THREE.Scene, position: THREE.Vector3, name = 'Bear'): BearModel {
    return new BearModel(scene, { name, position });
  }

  /**
   * 创建自定义缩放的熊实例
   */
  static createWithScale(scene: THREE.Scene, scale: number, name = 'Bear'): BearModel {
    return new BearModel(scene, { name, scale });
  }

  /**
   * 创建自定义纹理的熊实例
   */
  static createWithTextures(scene: THREE.Scene, textures: TextureFile[], name = 'Bear'): BearModel {
    return new BearModel(scene, { name, textures });
  }

  /**
   * 创建简单纹理配置的工厂方法
   */
  static createTextureConfig(name: string, path: string, materialProperty: TextureFile['materialProperty'] = 'map'): TextureFile {
    return {
      name,
      path,
      materialProperty,
      colorSpace: materialProperty === 'map' ? THREE.SRGBColorSpace : THREE.NoColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    };
  }

  /**
   * 创建完整纹理配置的工厂方法
   */
  static createFullTextureConfig(config: Partial<TextureFile> & { name: string; path: string }): TextureFile {
    return {
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 },
      ...config
    };
  }

  /**
   * 初始化并加载熊模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('Loading bear model...');
      
      // 加载主模型 (使用rig文件作为主模型)
      const modelResult = await this.loader.loadMainModel(this.config.modelPath);
      this.model = modelResult.model;
      this.mixer = modelResult.mixer!;
      
      // 预处理模型
      this.loader.preprocessModel(this.model);
      this.setupModel();

      // 创建动画控制器
      this.animationController = new AnimationController(this.mixer);

      // 加载所有动画文件
      const animations = await this.loader.loadAnimations(this.config.animations);
      this.setupAnimations(animations);

      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      console.log('Bear model loaded successfully');
      
      // 开始默认动画
      this.setState(BearState.IDLE);
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
    } catch (error) {
      console.error('Failed to load bear model:', error);
      throw error;
    }
  }

  /**
   * 设置模型属性
   */
  private setupModel(): void {
    // 设置缩放
    this.model.scale.setScalar(this.config.scale);
    
    // 设置位置
    this.model.position.copy(this.config.position);
    
    // 设置旋转
    this.model.rotation.copy(this.config.rotation);

    // 应用自定义纹理
    this.applyBearTexture();

    // 设置初始朝向匹配移动方向
    this.updateBearRotation();
  }

  /**
   * 应用熊的纹理贴图
   */
  private applyBearTexture(): void {
    if (!this.config.textures || this.config.textures.length === 0) {
      console.log('No textures configured for bear model');
      return;
    }

    // 加载所有配置的纹理
    this.config.textures.forEach((textureConfig, index) => {
      this.loadAndApplyTexture(textureConfig, index, this.config.textures.length);
    });
  }

  /**
   * 加载并应用单个纹理
   */
  private loadAndApplyTexture(textureConfig: TextureFile, index: number, total: number): void {
    const texture = this.textureLoader.load(
      textureConfig.path,
      () => {
        console.log(`Texture "${textureConfig.name}" loaded successfully`);
        // 当所有纹理都加载完成时触发回调
        if (index === total - 1 && this.onTextureLoaded) {
          this.onTextureLoaded();
        }
      },
      undefined,
      (error) => {
        console.error(`Failed to load texture "${textureConfig.name}":`, error);
      }
    );

    // 应用纹理配置
    this.configureTexture(texture, textureConfig);

    // 将纹理应用到模型
    this.applyTextureToModel(texture, textureConfig);
  }

  /**
   * 配置纹理属性
   */
  private configureTexture(texture: THREE.Texture, config: TextureFile): void {
    // 设置颜色空间
    if (config.colorSpace !== undefined) {
      texture.colorSpace = config.colorSpace;
    }

    // 设置翻转
    if (config.flipY !== undefined) {
      texture.flipY = config.flipY;
    }

    // 设置包装模式
    if (config.wrapS !== undefined) {
      texture.wrapS = config.wrapS;
    }
    if (config.wrapT !== undefined) {
      texture.wrapT = config.wrapT;
    }

    // 设置重复
    if (config.repeat) {
      texture.repeat.set(config.repeat.x, config.repeat.y);
    }

    texture.needsUpdate = true;
  }

  /**
   * 将纹理应用到模型
   */
  private applyTextureToModel(texture: THREE.Texture, config: TextureFile): void {
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            this.applyTextureToMaterial(material, texture, config);
          });
        } else {
          this.applyTextureToMaterial(child.material, texture, config);
        }
      }
    });
  }

  /**
   * 将纹理应用到材质
   */
  private applyTextureToMaterial(material: THREE.Material, texture: THREE.Texture, config: TextureFile): void {
    // 检查材质类型并应用纹理
    if (material instanceof THREE.MeshStandardMaterial || 
        material instanceof THREE.MeshPhongMaterial ||
        material instanceof THREE.MeshLambertMaterial ||
        material instanceof THREE.MeshBasicMaterial) {
      
      // 根据配置的材质属性应用纹理
      switch (config.materialProperty) {
        case 'map':
          material.map = texture;
          // 设置材质颜色为白色以显示纹理的真实颜色
          material.color = new THREE.Color(0xffffff);
          break;
        case 'normalMap':
          if ('normalMap' in material) {
            (material as any).normalMap = texture;
          }
          break;
        case 'roughnessMap':
          if ('roughnessMap' in material) {
            (material as any).roughnessMap = texture;
          }
          break;
        case 'metalnessMap':
          if ('metalnessMap' in material) {
            (material as any).metalnessMap = texture;
          }
          break;
        case 'aoMap':
          if ('aoMap' in material) {
            (material as any).aoMap = texture;
          }
          break;
        case 'emissiveMap':
          if ('emissiveMap' in material) {
            (material as any).emissiveMap = texture;
          }
          break;
      }
      
      // 标记材质需要更新
      material.needsUpdate = true;
      
      console.log(`Applied ${config.materialProperty} texture "${config.name}" to ${material.type} material`);
    }
  }



  /**
   * 设置动画状态
   */
  private setupAnimations(animationsMap: Map<string, THREE.AnimationClip[]>): void {
    // 为每个动画文件添加状态
    this.config.animations.forEach((animConfig) => {
      const clips = animationsMap.get(animConfig.name);
      if (clips && clips.length > 0) {
        // 使用第一个动画片段
        const clip = clips[0];
        
        console.log(`🎬 Setting up animation: ${animConfig.name}, duration: ${clip.duration}s, tracks: ${clip.tracks.length}`);
        
        this.animationController.addState(animConfig.name, clip, {
          loop: animConfig.loop,
          priority: animConfig.priority,
          fadeInTime: 0.5,
          fadeOutTime: 0.5
        });
        
        console.log(`✅ Animation state added: ${animConfig.name}`);
      } else {
        console.error(`❌ No clips found for animation: ${animConfig.name}`);
      }
    });

    // 设置状态转换规则
    this.setupAnimationTransitions();
    
    // 验证死亡动画是否正确加载
    const deathState = this.animationController.getStateInfo(BearState.DEATH);
    if (deathState) {
      console.log(`🐻 Death animation verified: duration=${deathState.clip.duration}s, loop=${deathState.loop}`);
    } else {
      console.error(`🐻 Death animation NOT found in controller!`);
    }
    
    // 验证IDLE动画配置（使用walk动画）
    const idleState = this.animationController.getStateInfo(BearState.IDLE);
    if (idleState) {
      console.log(`🐻 IDLE animation verified (using walk animation): duration=${idleState.clip.duration}s, loop=${idleState.loop}`);
      console.log(`🐻 Note: IDLE state uses walk animation but with isWalking=false to prevent movement`);
    } else {
      console.error(`🐻 IDLE animation NOT found in controller!`);
    }
  }

  /**
   * 设置动画状态转换规则
   */
  private setupAnimationTransitions(): void {
    // IDLE -> 其他状态 transitions
    this.animationController.addTransition(BearState.IDLE, BearState.WALK, 0.3);
    this.animationController.addTransition(BearState.IDLE, BearState.ATTACK, 0.2);
    this.animationController.addTransition(BearState.IDLE, BearState.DEATH, 0.5);
    
    // WALK -> 其他状态 transitions
    this.animationController.addTransition(BearState.WALK, BearState.IDLE, 0.3);
    this.animationController.addTransition(BearState.WALK, BearState.ATTACK, 0.2);
    this.animationController.addTransition(BearState.WALK, BearState.DEATH, 0.5);
    
    // ATTACK -> 其他状态 transitions
    this.animationController.addTransition(BearState.ATTACK, BearState.IDLE, 0.3);
    this.animationController.addTransition(BearState.ATTACK, BearState.WALK, 0.3);
    this.animationController.addTransition(BearState.ATTACK, BearState.DEATH, 0.5);
  }

  /**
   * 设置熊状态
   */
  async setState(state: BearState): Promise<void> {
    console.log(`🐻 setState called with state: ${state}, isLoaded: ${this.isLoaded}, isAlive: ${this.isAlive}, currentState: ${this.currentBearState}`);
    
    // 如果模型未加载，直接返回
    if (!this.isLoaded) {
      console.log(`🐻 setState rejected - model not loaded`);
      return;
    }

    // 特殊处理死亡状态：允许死亡状态即使在 isAlive = false 时也能播放
    if (state === BearState.DEATH) {
      console.log(`🐻 Force playing death animation`);
      this.currentBearState = state;
      
      try {
        // 强制播放死亡动画，不管当前状态
        await this.animationController.playState(BearState.DEATH, true);
        console.log(`🐻 Death animation state set successfully`);
      } catch (error) {
        console.error(`🐻 Error setting death animation state:`, error);
      }
      
      // if (this.onAnimationChange) {
      //   this.onAnimationChange(state);
      // }
      return;
    }

    // 对于非死亡状态，检查是否存活
    if (!this.isAlive) {
      console.log(`🐻 setState rejected - bear is dead, cannot change to ${state}`);
      return;
    }

    // 如果当前已经是死亡状态，不允许切换到其他状态
    if (this.currentBearState === BearState.DEATH) {
      console.log(`🐻 setState rejected - already in death state, cannot change to ${state}`);
      return;
    }

    console.log(`🐻 Setting bear state to: ${state}`);
    this.currentBearState = state;
    
    try {
      await this.animationController.playState(state);
      console.log(`🐻 Animation state ${state} set successfully`);
    } catch (error) {
      console.error(`🐻 Error setting animation state ${state}:`, error);
    }
    
    // 特殊状态处理
    if (state === BearState.WALK) {
      this.isWalking = true;
    } else if (state === BearState.IDLE) {
      // IDLE状态：播放walk动画但不实际移动
      this.isWalking = false;
    } else {
      this.isWalking = false;
    }
    
    if (this.onAnimationChange) {
      this.onAnimationChange(state);
    }
  }

  /**
   * 开始巡逻行走
   */
  async startPatrol(): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    await this.setState(BearState.WALK);
    this.isWalking = true;
    
    // 设置初始朝向以匹配移动方向
    this.updateBearRotation();
  }

  /**
   * 停止巡逻
   */
  async stopPatrol(): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    this.isWalking = false;
    await this.setState(BearState.IDLE);
  }

  /**
   * 攻击动作
   */
  async attack(): Promise<void> {
    if (!this.isLoaded || !this.isAlive) return;

    const wasWalking = this.isWalking;
    this.isWalking = false;
    
    await this.setState(BearState.ATTACK);
    
    // 攻击完成后返回之前的状态
    const timer = setTimeout(() => {
      if (this.isAlive && this.isLoaded) {
        if (wasWalking) {
          this.setState(BearState.WALK);
          this.isWalking = true;
        } else {
          this.setState(BearState.IDLE);
        }
      }
    }, 2000);
    this.activeTimers.push(timer);
  }

  /**
   * 受到伤害
   */
  takeDamage(damage: number): void {
    if (!this.isAlive) return;

    this.health = Math.max(0, this.health - damage);
    console.log(`🐻 Bear took ${damage} damage. Health: ${this.health}/${this.maxHealth}`);

    // debugger;

    if (this.health <= 0) {
      console.log(`🐻 Bear health reached 0, calling die()...`);
      // 异步调用死亡方法，不阻塞主线程
      this.die().catch(error => {
        console.error('Error during bear death animation:', error);
      });
    }
  }

  /**
   * 获取当前血量
   */
  getHealth(): number {
    return this.health;
  }

  /**
   * 获取最大血量
   */
  getMaxHealth(): number {
    return this.maxHealth;
  }

  /**
   * 设置血量
   */
  setHealth(health: number): void {
    if (!this.isAlive) return;
    
    const oldHealth = this.health;
    this.health = Math.max(0, Math.min(health, this.maxHealth));
    
    console.log(`🐻 Bear health set to: ${this.health}/${this.maxHealth}`);
    
    // 检查血量是否降为0或以下，触发死亡
    if (this.health <= 0 && oldHealth > 0) {
      console.log(`🐻 Bear health reached 0 via setHealth, calling die()...`);
      // 异步调用死亡方法，不阻塞主线程
      this.die().catch(error => {
        console.error('Error during bear death animation:', error);
      });
    }
  }

  /**
   * 设置最大血量
   */
  setMaxHealth(maxHealth: number): void {
    if (!this.isAlive) return;
    
    const oldHealth = this.health;
    this.maxHealth = Math.max(1, maxHealth);
    this.health = Math.min(this.health, this.maxHealth);
    
    console.log(`🐻 Bear max health set to: ${this.maxHealth}, current health: ${this.health}`);
    
    // 检查调整最大血量后当前血量是否变为0
    if (this.health <= 0 && oldHealth > 0) {
      console.log(`🐻 Bear health reached 0 due to max health adjustment, calling die()...`);
      // 异步调用死亡方法，不阻塞主线程
      this.die().catch(error => {
        console.error('Error during bear death animation:', error);
      });
    }
  }

  /**
   * 检查血量状态并触发死亡（如果需要）
   */
  checkHealthStatus(): void {
    if (!this.isAlive) return;
    
    if (this.health <= 0) {
      console.log(`🐻 Bear health check: ${this.health}/${this.maxHealth} - triggering death`);
      // 异步调用死亡方法，不阻塞主线程
      this.die().catch(error => {
        console.error('Error during bear death animation:', error);
      });
    }
  }

  /**
   * 获取存活状态
   */
  getIsAlive(): boolean {
    return this.isAlive;
  }

  /**
   * 设置存活状态（仅供内部使用，外部应通过takeDamage等方法改变状态）
   * @param alive 存活状态
   * @internal 此方法主要用于内部状态管理，不建议外部直接调用
   */
  setIsAlive(alive: boolean): void {
    this.isAlive = alive;
  }

  /**
   * 清除所有活动的定时器
   */
  private clearAllTimers(): void {
    console.log(`🐻 Clearing ${this.activeTimers.length} active timers`);
    for (const timer of this.activeTimers) {
      clearTimeout(timer);
    }
    this.activeTimers = [];
  }

  /**
   * 熊死亡
   */
  private async die(): Promise<void> {
    if (!this.isAlive) {
      console.log('🐻 Bear is already dead, skipping die()');
      return;
    }

    console.log('🐻 Bear is dying, starting death sequence...');
    
    // 清除所有定时器（在标记死亡之前）
    this.clearAllTimers();
    
    // 停止所有活动
    this.isWalking = false;
    this.isChasing = false;
    this.currentTarget = null;
    
    // 标记为死亡状态 - 这必须在播放死亡动画之前设置
    this.isAlive = false;
    
    console.log('🐻 Bear marked as dead, now playing death animation...');
    
    try {
      // 播放死亡动画（现在 setState 会正确处理死亡状态）
      await this.setState(BearState.DEATH);
      console.log('🐻 Death animation state set, waiting for animation to complete...');
      
      // 等待死亡动画完成
      await this.waitForDeathAnimationComplete();
      
      console.log('🐻 Bear death animation sequence completed');
      
      // 触发死亡动画完成回调
      if (this.onDeathAnimationComplete) {
        this.onDeathAnimationComplete();
      }
      
    } catch (error) {
      console.error('🐻 Error during death animation:', error);
      
      // 即使动画失败，也要确保触发完成回调
      if (this.onDeathAnimationComplete) {
        this.onDeathAnimationComplete();
      }
    }
  }

  /**
   * 等待死亡动画完成
   */
  private waitForDeathAnimationComplete(): Promise<void> {
    return new Promise((resolve) => {
      console.log('🐻 Waiting for death animation to complete...');
      
      // 获取死亡动画的状态信息
      const deathState = this.animationController.getCurrentState();
      console.log(`🐻 Current animation state: ${deathState}`);
      
      if (deathState === BearState.DEATH) {
        const deathAnimation = this.animationController.getStateInfo(BearState.DEATH);
        console.log(`🐻 Death animation info:`, deathAnimation);
        
        if (deathAnimation?.clip && deathAnimation?.action) {
          const clip = deathAnimation.clip;
          const action = deathAnimation.action;
          const duration = clip.duration * 1000; // 转换为毫秒
          
          console.log(`🐻 Death animation duration: ${duration}ms`);
          console.log(`🐻 Death animation action weight: ${action.getEffectiveWeight()}`);
          console.log(`🐻 Death animation action enabled: ${action.enabled}`);
          console.log(`🐻 Death animation action paused: ${action.paused}`);
          console.log(`🐻 Death animation action running: ${action.isRunning()}`);
          
          // 确保动画正在运行
          if (!action.isRunning() || !action.enabled) {
            console.warn('🐻 Death animation is not running, forcing restart...');
            action.reset();
            action.enabled = true;
            action.setEffectiveWeight(1.0);
            action.play();
          }
          
          // 监控动画播放进度
          let progressCheckInterval: NodeJS.Timeout;
          let lastTime = 0;
          let stuckCount = 0;
          
          progressCheckInterval = setInterval(() => {
            const currentTime = action.time;
            const progress = (currentTime / clip.duration) * 100;
            
            console.log(`🐻 Death animation progress: ${progress.toFixed(1)}% (${currentTime.toFixed(2)}s/${clip.duration.toFixed(2)}s)`);
            console.log(`🐻 Animation state: weight=${action.getEffectiveWeight()}, running=${action.isRunning()}, enabled=${action.enabled}`);
            
            // 检查动画是否卡住
            if (Math.abs(currentTime - lastTime) < 0.01) {
              stuckCount++;
              if (stuckCount > 5) {
                console.warn('🐻 Death animation appears stuck, forcing completion');
                clearInterval(progressCheckInterval);
                resolve();
                return;
              }
            } else {
              stuckCount = 0;
            }
            lastTime = currentTime;
            
            // 检查是否完成（允许一些误差）
            if (currentTime >= clip.duration * 0.95 || !action.isRunning()) {
              console.log('🐻 Death animation completed');
              clearInterval(progressCheckInterval);
              resolve();
            }
          }, 200); // 每200ms检查一次
          
          // 设置最大等待时间（动画时长 + 1秒缓冲）
          const maxWaitTime = duration + 1000;
          const timeoutId = setTimeout(() => {
            console.log('🐻 Death animation timeout reached, forcing completion');
            clearInterval(progressCheckInterval);
            resolve();
          }, maxWaitTime);
          
          // 确保在resolve时清理定时器
          const originalResolve = resolve;
          resolve = (...args) => {
            clearTimeout(timeoutId);
            clearInterval(progressCheckInterval);
            originalResolve(...args);
          };
          
        } else {
          // 如果没有找到动画clip或action，立即完成
          console.log('🐻 Death animation clip or action not found, completing immediately');
          resolve();
        }
      } else {
        console.log(`🐻 Not in death state (current: ${deathState}), completing immediately`);
        resolve();
      }
    });
  }

  /**
   * 复活熊
   */
  revive(): void {
    this.health = this.maxHealth;
    this.isAlive = true;
    this.setState(BearState.IDLE);
    console.log('Bear revived');
  }

  /**
   * 更新熊模型（每帧调用）
   */
  update(): void {
    if (!this.isLoaded) return;

    const deltaTime = this.clock.getDelta();
    this.animationController.update(deltaTime);

    // 攻击系统更新
    if (this.isAlive) {
      this.updateAttackSystem();
    }

    // 处理行走逻辑
    if (this.isWalking && this.isAlive) {
      this.updateWalkMovement(deltaTime);
    }
  }

  /**
   * 更新行走移动
   */
  private updateWalkMovement(deltaTime: number): void {
    if (!this.model) return;

    if (this.isChasing && this.currentTarget) {
      // 追击模式：朝目标移动
      this.updateChaseMovement(deltaTime);
    } else {
      // 巡逻模式：正常巡逻
      this.updatePatrolMovement(deltaTime);
    }
  }

  /**
   * 更新追击移动
   */
  private updateChaseMovement(deltaTime: number): void {
    if (!this.model || !this.currentTarget) return;

    // 计算朝向目标的方向
    const direction = new THREE.Vector3();
    direction.subVectors(this.currentTarget.position, this.model.position);
    direction.y = 0; // 只考虑水平移动
    direction.normalize();

    // 朝目标移动
    const movement = direction.multiplyScalar(this.walkSpeed * deltaTime);
    this.model.position.add(movement);

    // 更新移动方向（用于后续参考）
    this.walkDirection.copy(direction);
  }

  /**
   * 更新巡逻移动
   */
  private updatePatrolMovement(deltaTime: number): void {
    if (!this.model) return;

    // 沿着当前方向移动
    const movement = this.walkDirection.clone().multiplyScalar(this.walkSpeed * deltaTime);
    this.model.position.add(movement);

    // 检查边界并转向
    let directionChanged = false;
    
    if (this.model.position.x > this.walkBounds.max || this.model.position.x < this.walkBounds.min) {
      this.walkDirection.x *= -1;
      directionChanged = true;
    }

    if (this.model.position.z > this.walkBounds.max || this.model.position.z < this.walkBounds.min) {
      this.walkDirection.z *= -1;
      directionChanged = true;
    }

    // 如果方向改变了，更新熊的朝向
    if (directionChanged) {
      this.updateBearRotation();
      
      if (this.onWalkDirectionChange) {
        this.onWalkDirectionChange(this.walkDirection);
      }
    }
  }

  /**
   * 更新熊的朝向以匹配移动方向
   */
  private updateBearRotation(): void {
    if (!this.model) return;

    // 计算目标旋转角度
    const targetAngle = Math.atan2(this.walkDirection.x, this.walkDirection.z);
    
    // 设置熊的Y轴旋转，使其面向移动方向
    this.model.rotation.y = targetAngle;
  }

  /**
   * 更新攻击系统
   */
  private updateAttackSystem(): void {
    if (!this.model || this.targets.length === 0) return;

    const currentTime = Date.now();

    // 如果没有当前目标，扫描范围内的目标
    if (!this.currentTarget || !this.isTargetInRange(this.currentTarget)) {
      this.findNearestTarget();
    }

    // 如果有目标
    if (this.currentTarget) {
      const distanceToTarget = this.getDistanceToTarget(this.currentTarget);
      
      // 检查是否在攻击范围内
      if (distanceToTarget <= this.attackRange) {
        // 在攻击范围内
        if (!this.isChasing) {
          this.startChasing();
        }
        
        // 面向目标
        this.faceTarget(this.currentTarget);
        
        // 检查攻击冷却
        if (currentTime - this.lastAttackTime >= this.attackCooldown) {
          this.attackTarget();
        }
      } else {
        // 超出攻击范围，停止追击
        if (this.isChasing) {
          this.stopChasing();
        }
      }
    } else {
      // 没有目标，停止追击
      if (this.isChasing) {
        this.stopChasing();
      }
    }
  }

  /**
   * 查找最近的目标
   */
  private findNearestTarget(): void {
    if (!this.model) return;

    let nearestTarget: THREE.Object3D | null = null;
    let nearestDistance = this.attackRange;

    for (const target of this.targets) {
      const distance = this.getDistanceToTarget(target);
      if (distance <= this.attackRange && distance < nearestDistance) {
        nearestTarget = target;
        nearestDistance = distance;
      }
    }

    if (nearestTarget !== this.currentTarget) {
      this.currentTarget = nearestTarget;
      
      if (nearestTarget && this.onTargetDetected) {
        this.onTargetDetected(nearestTarget);
      } else if (!nearestTarget && this.onTargetLost) {
        this.onTargetLost();
      }
    }
  }

  /**
   * 检查目标是否在范围内
   */
  private isTargetInRange(target: THREE.Object3D): boolean {
    return this.getDistanceToTarget(target) <= this.attackRange;
  }

  /**
   * 获取到目标的距离
   */
  private getDistanceToTarget(target: THREE.Object3D): number {
    if (!this.model) return Infinity;
    return this.model.position.distanceTo(target.position);
  }

  /**
   * 面向目标
   */
  private faceTarget(target: THREE.Object3D): void {
    if (!this.model) return;

    const direction = new THREE.Vector3();
    direction.subVectors(target.position, this.model.position);
    direction.y = 0; // 只考虑水平方向
    direction.normalize();

    if (direction.length() > 0) {
      const targetAngle = Math.atan2(direction.x, direction.z);
      this.model.rotation.y = targetAngle;
    }
  }

  /**
   * 开始追击目标
   */
  private async startChasing(): Promise<void> {
    if (this.isChasing) return;
    
    this.isChasing = true;
    this.originalWalkSpeed = this.walkSpeed;
    this.walkSpeed = this.chaseSpeed;
    
    // 如果当前不在移动，开始移动
    if (!this.isWalking) {
      await this.setState(BearState.WALK);
      this.isWalking = true;
    }
    
    console.log('Bear started chasing target');
  }

  /**
   * 停止追击
   */
  private async stopChasing(): Promise<void> {
    if (!this.isChasing) return;
    
    this.isChasing = false;
    this.walkSpeed = this.originalWalkSpeed;
    this.currentTarget = null;
    
    // 返回巡逻状态
    if (this.isWalking) {
      // 恢复原来的巡逻方向
      this.walkDirection.set(1, 0, 0);
      this.updateBearRotation();
    }
    
    console.log('Bear stopped chasing');
  }

  /**
   * 攻击目标
   */
  private async attackTarget(): Promise<void> {
    if (!this.currentTarget) return;
    
    // 保存当前目标，防止在异步操作期间被清空
    const targetToAttack = this.currentTarget;
    
    this.lastAttackTime = Date.now();
    
    // 暂停移动进行攻击
    const wasWalking = this.isWalking;
    this.isWalking = false;
    
    // 执行攻击动画
    await this.setState(BearState.ATTACK);
    
    console.log('Bear attacking target!');
    
    // 检查目标是否仍然有效
    if (this.onAttackTarget && targetToAttack) {
      this.onAttackTarget(targetToAttack);
    }
    
    // 攻击完成后恢复状态
    const timer = setTimeout(() => {
      if (this.isAlive && this.isLoaded && this.currentTarget) {
        if (wasWalking || this.isChasing) {
          this.setState(BearState.WALK);
          this.isWalking = true;
        } else {
          this.setState(BearState.IDLE);
        }
      }
    }, 1500); // 攻击动画持续时间
    this.activeTimers.push(timer);
  }

  /**
   * 设置行走边界
   */
  setWalkBounds(min: number, max: number): void {
    this.walkBounds = { min, max };
  }

  /**
   * 设置行走速度
   */
  setWalkSpeed(speed: number): void {
    this.walkSpeed = speed;
  }

  /**
   * 设置行走方向
   */
  setWalkDirection(direction: THREE.Vector3): void {
    this.walkDirection.copy(direction).normalize();
    // 更新朝向以匹配新的移动方向
    this.updateBearRotation();
  }

  /**
   * 添加攻击目标
   */
  addTarget(target: THREE.Object3D): void {
    if (!this.targets.includes(target)) {
      this.targets.push(target);
      console.log('Added target to bear:', target.name || 'Unnamed');
    }
  }

  /**
   * 移除攻击目标
   */
  removeTarget(target: THREE.Object3D): void {
    const index = this.targets.indexOf(target);
    if (index !== -1) {
      this.targets.splice(index, 1);
      
      // 如果移除的是当前目标，清除当前目标
      if (this.currentTarget === target) {
        this.currentTarget = null;
        if (this.isChasing) {
          this.stopChasing();
        }
      }
      
      console.log('Removed target from bear:', target.name || 'Unnamed');
    }
  }

  /**
   * 清除所有目标
   */
  clearTargets(): void {
    this.targets = [];
    this.currentTarget = null;
    if (this.isChasing) {
      this.stopChasing();
    }
    console.log('Cleared all targets');
  }

  /**
   * 设置攻击范围
   */
  setAttackRange(range: number): void {
    this.attackRange = Math.max(0, range);
  }

  /**
   * 设置攻击冷却时间
   */
  setAttackCooldown(cooldown: number): void {
    this.attackCooldown = Math.max(0, cooldown);
  }

  /**
   * 设置追击速度
   */
  setChaseSpeed(speed: number): void {
    this.chaseSpeed = Math.max(0, speed);
  }

  /**
   * 获取攻击范围
   */
  getAttackRange(): number {
    return this.attackRange;
  }

  /**
   * 获取当前目标
   */
  getCurrentTarget(): THREE.Object3D | null {
    return this.currentTarget;
  }

  /**
   * 检查是否在追击状态
   */
  isInChaseMode(): boolean {
    return this.isChasing;
  }

  /**
   * 获取所有目标
   */
  getTargets(): THREE.Object3D[] {
    return [...this.targets]; // 返回副本
  }

  /**
   * 获取熊状态信息
   */
  getStatus() {
    return {
      health: this.health,
      maxHealth: this.maxHealth,
      isAlive: this.isAlive,
      currentState: this.currentBearState,
      isLoaded: this.isLoaded,
      isWalking: this.isWalking,
      position: this.model?.position.clone(),
      rotation: this.model?.rotation.clone(),
      walkDirection: this.walkDirection.clone(),
      walkSpeed: this.walkSpeed,
      // 攻击系统状态
      attackRange: this.attackRange,
      attackCooldown: this.attackCooldown,
      isChasing: this.isChasing,
      currentTarget: this.currentTarget?.name || null,
      targetCount: this.targets.length,
      chaseSpeed: this.chaseSpeed
    };
  }

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 获取模型引用
   */
  getModel(): THREE.Group | null {
    return this.model || null;
  }

  /**
   * 获取动画控制器
   */
  getAnimationController(): AnimationController {
    return this.animationController;
  }

  /**
   * 重新应用纹理（公共方法）
   */
  reapplyTexture(): void {
    if (this.model) {
      this.applyBearTexture();
    }
  }

  /**
   * 更新纹理配置
   */
  updateTextureConfig(textures: TextureFile[]): void {
    this.config.textures = textures;
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onAnimationChange?: (newState: BearState) => void;
    onDeath?: () => void;
    onDeathAnimationComplete?: () => void;
    onWalkDirectionChange?: (direction: THREE.Vector3) => void;
    onTextureLoaded?: () => void;
    onTargetDetected?: (target: THREE.Object3D) => void;
    onTargetLost?: () => void;
    onAttackTarget?: (target: THREE.Object3D) => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onAnimationChange = callbacks.onAnimationChange;
    this.onDeath = callbacks.onDeath;
    this.onDeathAnimationComplete = callbacks.onDeathAnimationComplete;
    this.onWalkDirectionChange = callbacks.onWalkDirectionChange;
    this.onTextureLoaded = callbacks.onTextureLoaded;
    this.onTargetDetected = callbacks.onTargetDetected;
    this.onTargetLost = callbacks.onTargetLost;
    this.onAttackTarget = callbacks.onAttackTarget;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model && this.scene) {
      this.scene.remove(this.model);
    }
    
    if (this.animationController) {
      this.animationController.dispose();
    }
    
    if (this.loader) {
      this.loader.dispose();
    }

    // 清理纹理资源
    if (this.model) {
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => {
              if (material.map) {
                material.map.dispose();
              }
              material.dispose();
            });
          } else {
            if (child.material.map) {
              child.material.map.dispose();
            }
            child.material.dispose();
          }
        }
      });
    }

    this.isLoaded = false;
    console.log('Bear model disposed');
  }
} 