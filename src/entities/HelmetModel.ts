import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface HelmetConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export class HelmetModel {
  private config: HelmetConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 默认头盔纹理配置（基于meta文件分析）
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'helmetDiffuse',
      path: '/src/models/Helmet/SS_Helmet_01_D.jpg',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    },
    {
      name: 'helmetNormal',
      path: '/src/models/Helmet/SS_Helmet_01_N.jpg',
      materialProperty: 'normalMap',
      colorSpace: THREE.LinearSRGBColorSpace,
      flipY: false,
      wrapS: THREE.RepeatWrapping,
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onTextureLoaded?: () => void;

  constructor(scene: THREE.Scene, config?: Partial<HelmetConfig>) {
    this.scene = scene;
    this.textureLoader = new THREE.TextureLoader();
    
    // 默认配置
    const defaultConfig: HelmetConfig = {
      name: 'Helmet',
      modelPath: '/src/models/Helmet/SS_Helmet_01.fbx',
      textures: HelmetModel.DEFAULT_TEXTURES,
      scale: 0.01,
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0)
    };

    this.config = { ...defaultConfig, ...config };
    this.loader = new ModelLoader();
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onTextureLoaded?: () => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onTextureLoaded = callbacks.onTextureLoaded;
  }

  /**
   * 初始化并加载头盔模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🪖 开始加载头盔模型...');

      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;

      // 设置模型属性
      this.setupModelProperties();

      // 预处理模型
      this.loader.preprocessModel(this.model);

      // 配置材质和纹理
      await this.configureMaterials();

      // 添加到场景
      this.scene.add(this.model);
      this.isLoaded = true;

      console.log('✅ 头盔模型加载完成');
      this.onLoadComplete?.();

    } catch (error) {
      console.error('❌ 头盔模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型基本属性
   */
  private setupModelProperties(): void {
    if (!this.model) return;

    // 应用初始变换
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);

    console.log('🎯 头盔模型属性设置完成:', {
      scale: this.config.scale,
      position: this.config.position,
      rotation: this.config.rotation
    });
  }

  /**
   * 向后兼容的load方法
   */
  async load(): Promise<void> {
    return this.initialize();
  }

  /**
   * 配置材质和纹理（基于meta文件配置）
   */
  private async configureMaterials(): Promise<void> {
    const texturePromises: Promise<THREE.Texture>[] = [];
    const textureMap = new Map<string, THREE.Texture>();

    // 加载所有纹理
    for (const textureConfig of this.config.textures) {
      const promise = this.loadTexture(textureConfig).then(texture => {
        textureMap.set(textureConfig.materialProperty, texture);
        return texture;
      });
      texturePromises.push(promise);
    }

    try {
      await Promise.all(texturePromises);
      console.log('✅ 所有纹理加载完成');
    } catch (error) {
      console.warn('⚠️ 部分纹理加载失败，使用默认材质:', error);
    }

    // 遍历模型并应用材质
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const material = new THREE.MeshStandardMaterial({
          // 基于meta文件中的mainColor配置 (r: 202, g: 202, b: 202)
          color: new THREE.Color(202/255, 202/255, 202/255),
          roughness: 0.8,
          metalness: 0.1,
          side: THREE.DoubleSide
        });

        // 应用纹理
        const diffuseTexture = textureMap.get('map');
        if (diffuseTexture) {
          material.map = diffuseTexture;
        }

        const normalTexture = textureMap.get('normalMap');
        if (normalTexture) {
          material.normalMap = normalTexture;
        }

        child.material = material;
        child.castShadow = true;
        child.receiveShadow = true;

        console.log(`🎨 已配置网格材质: ${child.name || 'Unnamed'}`);
      }
    });

    this.onTextureLoaded?.();
  }

  /**
   * 加载单个纹理
   */
  private async loadTexture(config: TextureFile): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        config.path,
        (texture) => {
          // 配置纹理属性
          if (config.colorSpace) texture.colorSpace = config.colorSpace;
          if (config.flipY !== undefined) texture.flipY = config.flipY;
          if (config.wrapS) texture.wrapS = config.wrapS;
          if (config.wrapT) texture.wrapT = config.wrapT;
          if (config.repeat) texture.repeat.set(config.repeat.x, config.repeat.y);

          texture.needsUpdate = true;
          console.log(`✅ 纹理加载成功: ${config.name}`);
          resolve(texture);
        },
        (progress) => {
          console.log(`📥 纹理加载中: ${config.name} - ${Math.round(progress.loaded / progress.total * 100)}%`);
        },
        (error) => {
          console.error(`❌ 纹理加载失败: ${config.name}`, error);
          reject(error);
        }
      );
    });
  }

  /**
   * 获取模型对象
   */
  getModel(): THREE.Group | null {
    return this.isLoaded ? this.model : null;
  }

  /**
   * 设置位置
   */
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
      this.config.position.copy(position);
    }
  }

  /**
   * 获取位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : this.config.position.clone();
  }

  /**
   * 设置旋转
   */
  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
      this.config.rotation.copy(rotation);
    }
  }

  /**
   * 获取旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : this.config.rotation.clone();
  }

  /**
   * 设置缩放
   */
  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
      this.config.scale = scale;
    }
  }

  /**
   * 获取缩放
   */
  getScale(): number {
    return this.config.scale;
  }

  /**
   * 设置材质属性
   */
  setMaterialProperties(properties: {
    color?: THREE.Color;
    roughness?: number;
    metalness?: number;
    emissive?: THREE.Color;
  }): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
        if (properties.color) child.material.color.copy(properties.color);
        if (properties.roughness !== undefined) child.material.roughness = properties.roughness;
        if (properties.metalness !== undefined) child.material.metalness = properties.metalness;
        if (properties.emissive) child.material.emissive.copy(properties.emissive);
        child.material.needsUpdate = true;
      }
    });
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('❌ 模型未加载');
      return;
    }

    console.log('🔍 头盔模型材质调试信息:');
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        console.log(`网格: ${child.name || 'Unnamed'}`);
        console.log('  材质:', child.material);
        if (child.material instanceof THREE.MeshStandardMaterial) {
          console.log(`  颜色: rgb(${Math.round(child.material.color.r * 255)}, ${Math.round(child.material.color.g * 255)}, ${Math.round(child.material.color.b * 255)})`);
          console.log(`  粗糙度: ${child.material.roughness}`);
          console.log(`  金属度: ${child.material.metalness}`);
          console.log(`  纹理: ${child.material.map ? '已加载' : '无'}`);
          console.log(`  法线贴图: ${child.material.normalMap ? '已加载' : '无'}`);
        }
      }
    });
  }

  /**
   * 检查是否已加载
   */
  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 销毁资源
   */
  dispose(): void {
    if (this.model) {
      this.scene.remove(this.model);
      
      // 清理材质和纹理
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.material instanceof THREE.Material) {
            child.material.dispose();
          }
          if (child.geometry) {
            child.geometry.dispose();
          }
        }
      });
    }

    this.isLoaded = false;
    console.log('🗑️ 头盔模型资源已清理');
  }
}
