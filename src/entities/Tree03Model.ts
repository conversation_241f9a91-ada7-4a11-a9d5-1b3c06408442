import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface Tree03Config {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export enum Tree03State {
  STATIC = 'static',
  SWAYING = 'swaying',
  WINDY = 'windy'
}

export class Tree03Model {
  private config: Tree03Config;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;
  private clock: THREE.Clock;

  // 树的状态
  private currentTreeState: Tree03State = Tree03State.STATIC;
  private swayIntensity = 0.5;
  private windStrength = 1.0;
  private swaySpeed = 1.0;

  // 色温调整
  private colorWarmth = 0; // 0-100的色温值
  private baseColor: THREE.Color | null = null; // 存储原始Cocos Creator颜色

  // 默认树纹理配置 (Tree_03只有FBX文件，没有标准纹理文件)
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    // 如果后续添加纹理，可以在这里配置
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onStateChange?: (state: Tree03State) => void;

  constructor(scene: THREE.Scene, config?: Partial<Tree03Config>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'Tree03',
      modelPath: '/src/models/Tree_03/SS_Tree_03.fbx',
      textures: Tree03Model.DEFAULT_TEXTURES,
      scale: 0.02,  // 适合的树木尺寸
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.clock = new THREE.Clock();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 创建标准树实例的快捷方法
   */
  static create(scene: THREE.Scene, name = 'Tree03'): Tree03Model {
    return new Tree03Model(scene, { name });
  }

  /**
   * 初始化并加载树模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('开始加载Tree03模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();
      
      // 预处理模型
      this.loader.preprocessModel(this.model);

      // 应用基于Cocos Creator材质的优化（异步）
      await this.applyCocosMaterialProperties();
      
      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('Tree03模型加载完成');
    } catch (error) {
      console.error('Tree03模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置模型的基本属性
   */
  private setupModelProperties(): void {
    this.model.name = this.config.name;
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);
  }

  /**
   * 加载并解析Cocos Creator材质文件
   */
  private async loadCocosCreatorMaterial(): Promise<THREE.Color> {
    try {
      const response = await fetch('/src/models/Tree_03/M_SS_Tree_02.mtl');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const materialData = await response.json();

      // 验证Cocos Creator材质文件结构
      if (!materialData.__type__ || materialData.__type__ !== 'cc.Material') {
        throw new Error('Invalid Cocos Creator material file format');
      }

      // 解析Cocos Creator材质属性
      if (materialData._props && materialData._props[0] && materialData._props[0].mainColor) {
        const mainColor = materialData._props[0].mainColor;

        // 验证颜色数据
        if (typeof mainColor.r === 'number' && typeof mainColor.g === 'number' && typeof mainColor.b === 'number') {
          // 使用十六进制方式创建颜色，确保正确的颜色空间处理
          const hexValue = (mainColor.r << 16) | (mainColor.g << 8) | mainColor.b;
          const color = new THREE.Color(hexValue);

          console.log('🎨 Tree03 Cocos Creator 材质颜色信息:');
          console.log('  原始RGB:', `rgb(${mainColor.r}, ${mainColor.g}, ${mainColor.b})`);
          console.log('  十六进制值:', `0x${hexValue.toString(16)}`);
          console.log('  Three.js颜色:', `#${color.getHexString()}`);
          console.log('  ✅ 颜色空间问题已修复，使用十六进制构造方式');

          return color;
        } else {
          throw new Error('Invalid color values in material file');
        }
      } else {
        throw new Error('No mainColor found in material properties');
      }
    } catch (error) {
      console.warn('⚠️ Failed to load Cocos Creator material, using default color:', error);
      // 使用默认的暖棕色 (基于文件中的RGB值)
      return new THREE.Color(0.847, 0.580, 0.286); // RGB(216, 148, 73) / 255
    }
  }

  /**
   * 应用基于Cocos Creator材质文件的属性
   */
  private async applyCocosMaterialProperties(): Promise<void> {
    // 动态加载Cocos Creator材质颜色
    const cocosColor = await this.loadCocosCreatorMaterial();

    // 存储基础颜色用于色温调整
    this.baseColor = cocosColor.clone();

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];

        materials.forEach((material) => {
          this.optimizeTree03Material(material, cocosColor);
        });
      }
    });
  }

  /**
   * 优化Tree03的材质属性（基于Cocos Creator材质文件中的mainColor）
   */
  private optimizeTree03Material(material: THREE.Material, cocosColor: THREE.Color): void {
    console.log(`🎨 正在应用颜色到Tree03材质 ${material.type}:`, {
      expectedColor: `#${cocosColor.getHexString()}`,
      originalColor: material instanceof THREE.MeshStandardMaterial ||
                     material instanceof THREE.MeshPhongMaterial ||
                     material instanceof THREE.MeshLambertMaterial ||
                     material instanceof THREE.MeshBasicMaterial ?
                     `#${material.color.getHexString()}` : 'N/A'
    });

    if (material instanceof THREE.MeshStandardMaterial) {
      material.color.copy(cocosColor);
      material.roughness = 0.6;                    // 适中的粗糙度，增强反射
      material.metalness = 0.0;                    // 非金属
      material.transparent = false;
      material.opacity = 1.0;
      // 增强环境光反射，模拟Cocos Creator的效果
      material.envMapIntensity = 1.2;

      // 启用阴影
      material.shadowSide = THREE.DoubleSide;

      console.log('✅ Applied Cocos-based tree material properties to Tree03 MeshStandardMaterial');
    } else if (material instanceof THREE.MeshPhongMaterial) {
      material.color.copy(cocosColor);
      material.shininess = 50;                     // 增加光泽度，更好的光照反射
      material.transparent = false;
      material.opacity = 1.0;
      // 增强镜面反射，模拟温暖的光照效果
      material.specular = new THREE.Color(0xffeb99);

      console.log('✅ Applied Cocos-based tree material properties to Tree03 MeshPhongMaterial');
    } else if (material instanceof THREE.MeshLambertMaterial) {
      material.color.copy(cocosColor);
      material.transparent = false;
      material.opacity = 1.0;

      console.log('✅ Applied Cocos-based tree material properties to Tree03 MeshLambertMaterial');
    } else if (material instanceof THREE.MeshBasicMaterial) {
      material.color.copy(cocosColor);
      material.transparent = false;
      material.opacity = 1.0;

      console.log('✅ Applied Cocos-based tree material properties to Tree03 MeshBasicMaterial');
    }

    // 验证颜色是否正确应用
    if (material instanceof THREE.MeshStandardMaterial ||
        material instanceof THREE.MeshPhongMaterial ||
        material instanceof THREE.MeshLambertMaterial ||
        material instanceof THREE.MeshBasicMaterial) {
      console.log(`🔍 Tree03材质颜色应用后验证: #${material.color.getHexString()}`);
    }

    // 标记材质需要更新
    material.needsUpdate = true;
  }

  /**
   * 设置树的状态
   */
  setState(state: Tree03State): void {
    if (this.currentTreeState === state) return;

    this.currentTreeState = state;
    console.log(`Tree03 state changed to: ${state}`);

    if (this.onStateChange) {
      this.onStateChange(state);
    }
  }

  /**
   * 更新树模型（每帧调用）
   */
  update(): void {
    if (!this.isLoaded || !this.model) return;

    const deltaTime = this.clock.getDelta();
    const time = this.clock.getElapsedTime();

    // 根据状态更新树的行为
    switch (this.currentTreeState) {
      case Tree03State.SWAYING:
        this.updateSwaying(time, deltaTime);
        break;
      case Tree03State.WINDY:
        this.updateWindyMotion(time, deltaTime);
        break;
      case Tree03State.STATIC:
      default:
        // 静态状态，重置旋转
        this.model.rotation.x = this.config.rotation.x;
        this.model.rotation.z = this.config.rotation.z;
        break;
    }
  }

  /**
   * 更新轻微摆动
   */
  private updateSwaying(time: number, _deltaTime: number): void {
    if (!this.model) return;

    // 轻微的摆动效果
    const swayX = Math.sin(time * this.swaySpeed) * this.swayIntensity * 0.01;
    const swayZ = Math.cos(time * this.swaySpeed * 0.7) * this.swayIntensity * 0.008;

    this.model.rotation.x = this.config.rotation.x + swayX;
    this.model.rotation.z = this.config.rotation.z + swayZ;
  }

  /**
   * 更新强风摆动
   */
  private updateWindyMotion(time: number, _deltaTime: number): void {
    if (!this.model) return;

    // 强风摆动效果
    const windX = Math.sin(time * this.swaySpeed * 1.5) * this.windStrength * 0.03;
    const windZ = Math.cos(time * this.swaySpeed * 1.2) * this.windStrength * 0.025;

    // 添加一些随机性
    const randomFactor = Math.sin(time * 3.7) * 0.5 + 0.5;

    this.model.rotation.x = this.config.rotation.x + windX * randomFactor;
    this.model.rotation.z = this.config.rotation.z + windZ * randomFactor;
  }

  /**
   * 设置摆动参数
   */
  setSwayParameters(intensity: number, speed: number, windStrength: number): void {
    this.swayIntensity = Math.max(0, Math.min(2, intensity));
    this.swaySpeed = Math.max(0.1, Math.min(5, speed));
    this.windStrength = Math.max(0, Math.min(3, windStrength));
  }

  /**
   * 设置摆动强度
   */
  setSwayIntensity(intensity: number): void {
    this.swayIntensity = Math.max(0, Math.min(2, intensity));
  }

  /**
   * 设置摆动速度
   */
  setSwaySpeed(speed: number): void {
    this.swaySpeed = Math.max(0.1, Math.min(5, speed));
  }

  /**
   * 设置风力强度
   */
  setWindStrength(strength: number): void {
    this.windStrength = Math.max(0, Math.min(3, strength));
  }

  /**
   * 调整颜色温度，模拟Cocos Creator的效果
   * @param warmth - 温度值 (0-100)
   */
  setColorWarmth(warmth: number): void {
    this.colorWarmth = Math.max(0, Math.min(100, warmth));
    this.applyColorWarmth();
  }

  /**
   * 获取当前色温值
   */
  getColorWarmth(): number {
    return this.colorWarmth;
  }

  /**
   * 应用色温调整到所有材质
   */
  private applyColorWarmth(): void {
    if (!this.model || !this.baseColor) return;

    // 根据warmth值调整颜色，增加金黄色调
    const warmthFactor = this.colorWarmth / 100;

    // 向金黄色调整
    const warmColor = new THREE.Color(0xffd700); // 金色
    const adjustedColor = this.baseColor.clone().lerp(warmColor, warmthFactor * 0.3);

    // 应用到所有材质
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material];

        materials.forEach((material) => {
          if ('color' in material && material.color instanceof THREE.Color) {
            material.color.copy(adjustedColor);
            material.needsUpdate = true;
          }
        });
      }
    });

    console.log(`🎨 Tree03色温调整: ${this.colorWarmth}%, 颜色: #${adjustedColor.getHexString()}`);
  }

  /**
   * 重置颜色到原始状态
   */
  resetColor(): void {
    this.colorWarmth = 0;
    this.applyColorWarmth();
  }

  /**
   * 设置模型位置
   */
  setPosition(position: THREE.Vector3): void {
    this.config.position.copy(position);
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  /**
   * 设置模型旋转
   */
  setRotation(rotation: THREE.Euler): void {
    this.config.rotation.copy(rotation);
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  /**
   * 设置模型缩放
   */
  setScale(scale: number): void {
    this.config.scale = scale;
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  /**
   * 获取模型位置
   */
  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : this.config.position.clone();
  }

  /**
   * 获取模型旋转
   */
  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : this.config.rotation.clone();
  }

  /**
   * 获取模型缩放
   */
  getScale(): number {
    return this.model ? this.model.scale.x : this.config.scale;
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): Tree03State {
    return this.currentTreeState;
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onStateChange?: (state: Tree03State) => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onStateChange = callbacks.onStateChange;
  }

  /**
   * 获取模型状态
   */
  getStatus(): {
    name: string;
    loaded: boolean;
    position: THREE.Vector3;
    rotation: THREE.Euler;
    scale: number;
    state: Tree03State;
    swayIntensity: number;
    swaySpeed: number;
    windStrength: number;
    colorWarmth: number;
  } {
    return {
      name: this.config.name,
      loaded: this.isLoaded,
      position: this.getPosition(),
      rotation: this.getRotation(),
      scale: this.getScale(),
      state: this.currentTreeState,
      swayIntensity: this.swayIntensity,
      swaySpeed: this.swaySpeed,
      windStrength: this.windStrength,
      colorWarmth: this.colorWarmth
    };
  }

  /**
   * 获取底层模型对象
   */
  getModel(): THREE.Group | null {
    return this.model || null;
  }

  /**
   * 调试材质信息
   */
  async debugMaterials(): Promise<void> {
    if (!this.model) {
      console.log('❌ Tree03 模型未加载');
      return;
    }

    console.log('🔍 === Tree03 材质调试信息 ===');
    console.log('📄 Cocos Creator 材质文件: /src/models/Tree_03/M_SS_Tree_02.mtl');

    // 重新加载材质文件以获取实际颜色
    const actualColor = await this.loadCocosCreatorMaterial();
    console.log('🎨 实际加载的颜色: #' + actualColor.getHexString());
    console.log('🎨 正确的颜色处理: RGB(216, 148, 73) -> #' + new THREE.Color(0xd89449).getHexString());
    console.log('✅ 颜色空间问题已修复，现在使用正确的十六进制构造方式');
    console.log('');

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        console.log(`🔸 网格: "${child.name}"`);
        const materials = Array.isArray(child.material) ? child.material : [child.material];

        materials.forEach((material, index) => {
          console.log(`  材质 ${index}:`);
          console.log(`    类型: ${material.type}`);
          console.log(`    名称: ${material.name || 'Unnamed'}`);

          if ('color' in material) {
            console.log(`    颜色: #${material.color.getHexString()}`);
          }
          if ('map' in material) {
            console.log(`    纹理: ${material.map ? '有纹理' : '无纹理'}`);
          }
          if (material instanceof THREE.MeshStandardMaterial) {
            console.log(`    粗糙度: ${material.roughness}`);
            console.log(`    金属度: ${material.metalness}`);
          }
          console.log(`    透明: ${material.transparent}`);
          console.log(`    不透明度: ${material.opacity}`);
        });
        console.log('');
      }
    });

    console.log('💡 说明:');
    console.log('  - Three.js 不能直接使用 Cocos Creator 的 MTL 文件');
    console.log('  - 该文件是 JSON 格式，需要手动解析并应用材质属性');
    console.log('  - 当前实现会自动解析 mainColor 并应用到模型材质');
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      this.scene.remove(this.model);

      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    }

    console.log('Tree03 model disposed');
  }
}
