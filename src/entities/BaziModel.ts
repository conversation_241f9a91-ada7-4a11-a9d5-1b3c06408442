import * as THREE from 'three';
import { ModelLoader } from '../loaders/FBXLoader.js';

export interface TextureFile {
  name: string;
  path: string;
  materialProperty: 'map' | 'normalMap' | 'roughnessMap' | 'metalnessMap' | 'aoMap' | 'emissiveMap';
  colorSpace?: THREE.ColorSpace;
  flipY?: boolean;
  wrapS?: THREE.Wrapping;
  wrapT?: THREE.Wrapping;
  repeat?: { x: number; y: number };
}

export interface BaziConfig {
  name: string;
  modelPath: string;
  textures: TextureFile[];
  scale: number;
  position: THREE.Vector3;
  rotation: THREE.Euler;
}

export class BaziModel {
  private config: BaziConfig;
  private loader: ModelLoader;
  private model!: THREE.Group;
  private isLoaded = false;
  private scene: THREE.Scene;
  private textureLoader: THREE.TextureLoader;

  // 基于meta文件分析的默认纹理配置
  private static readonly DEFAULT_TEXTURES: TextureFile[] = [
    {
      name: 'baziDiffuse',
      path: '/src/models/Bazi/SSD_Bazi_01_D.jpg',
      materialProperty: 'map',
      colorSpace: THREE.SRGBColorSpace,
      flipY: true,  // 根据meta文件，FBX纹理不需要翻转
      wrapS: THREE.RepeatWrapping,  // 根据meta文件的repeat设置
      wrapT: THREE.RepeatWrapping,
      repeat: { x: 1, y: 1 }
    }
  ];

  // 事件回调
  private onLoadComplete?: () => void;
  private onTextureLoaded?: () => void;

  constructor(scene: THREE.Scene, config?: Partial<BaziConfig>) {
    this.scene = scene;
    
    // 使用默认配置并合并用户配置
    this.config = {
      name: 'BaziModel',
      modelPath: '/src/models/Bazi/SSD_Bazi_01.fbx',
      textures: BaziModel.DEFAULT_TEXTURES,
      scale: 0.7,  // 适合的默认大小
      position: new THREE.Vector3(0, 0, 0),
      rotation: new THREE.Euler(0, 0, 0),
      ...config
    };
    
    this.loader = new ModelLoader();
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 初始化并加载模型
   */
  async initialize(): Promise<void> {
    try {
      console.log('🍽️ 开始加载八字模型...');
      
      // 加载主模型
      const result = await this.loader.loadMainModel(this.config.modelPath);
      this.model = result.model;
      
      // 设置模型属性
      this.setupModelProperties();
      
      // 预处理模型
      this.loader.preprocessModel(this.model);
      
      // 配置材质
      await this.configureMaterials();

      // 加载并应用纹理
      await this.loadAndApplyTextures();

      // 添加到场景
      this.scene.add(this.model);
      
      this.isLoaded = true;
      
      if (this.onLoadComplete) {
        this.onLoadComplete();
      }
      
      console.log('✅ 八字模型加载完成');
    } catch (error) {
      console.error('❌ 八字模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 向后兼容的load方法
   */
  async load(): Promise<void> {
    return this.initialize();
  }

  /**
   * 设置模型基本属性
   */
  private setupModelProperties(): void {
    if (!this.model) return;

    // 应用初始变换
    this.model.scale.setScalar(this.config.scale);
    this.model.position.copy(this.config.position);
    this.model.rotation.copy(this.config.rotation);

    console.log('🎯 八字模型属性设置完成:', {
      scale: this.config.scale,
      position: this.config.position,
      rotation: this.config.rotation
    });
  }

  /**
   * 异步加载并应用纹理
   */
  private async loadAndApplyTextures(): Promise<void> {
    if (!this.config.textures || this.config.textures.length === 0) {
      console.log('No textures configured for Bazi model');
      return;
    }

    // 创建所有纹理加载的Promise
    const texturePromises = this.config.textures.map((textureConfig) => {
      return this.loadTextureAsync(textureConfig);
    });

    try {
      // 等待所有纹理加载完成
      const loadedTextures = await Promise.all(texturePromises);
      
      // 应用所有纹理到模型
      loadedTextures.forEach((textureResult) => {
        if (textureResult.texture) {
          this.applyTextureToModel(textureResult.texture, textureResult.config);
        }
      });

      console.log('✅ 八字模型纹理加载完成');
      if (this.onTextureLoaded) {
        this.onTextureLoaded();
      }
    } catch (error) {
      console.error('❌ 八字模型纹理加载失败:', error);
    }
  }

  /**
   * 异步加载单个纹理
   */
  private loadTextureAsync(textureConfig: TextureFile): Promise<{texture: THREE.Texture | null, config: TextureFile}> {
    return new Promise((resolve) => {
      this.textureLoader.load(
        textureConfig.path,
        (loadedTexture) => {
          // 配置纹理属性
          this.configureTexture(loadedTexture, textureConfig);
          console.log(`Texture "${textureConfig.name}" loaded successfully`);
          resolve({ texture: loadedTexture, config: textureConfig });
        },
        undefined,
        (error) => {
          console.error(`Failed to load texture "${textureConfig.name}":`, error);
          resolve({ texture: null, config: textureConfig });
        }
      );
    });
  }

  /**
   * 配置纹理属性
   */
  private configureTexture(texture: THREE.Texture, config: TextureFile): void {
    // 设置颜色空间
    if (config.colorSpace) {
      texture.colorSpace = config.colorSpace;
    }

    // 设置翻转
    if (config.flipY !== undefined) {
      texture.flipY = config.flipY;
    }

    // 设置包装模式
    if (config.wrapS) texture.wrapS = config.wrapS;
    if (config.wrapT) texture.wrapT = config.wrapT;

    // 设置重复
    if (config.repeat) {
      texture.repeat.set(config.repeat.x, config.repeat.y);
    }

    texture.needsUpdate = true;
  }

  /**
   * 将纹理应用到模型
   */
  private applyTextureToModel(texture: THREE.Texture, config: TextureFile): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const material = child.material;
        
        if (material instanceof THREE.MeshStandardMaterial) {
          // 应用纹理到指定的材质属性
          (material as any)[config.materialProperty] = texture;
          material.needsUpdate = true;
          
          console.log(`Applied texture "${config.name}" to material property "${config.materialProperty}"`);
        }
      }
    });
  }

  /**
   * 配置材质和纹理
   */
  private async configureMaterials(): Promise<void> {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 创建基于meta文件配置的PBR材质
        const material = new THREE.MeshStandardMaterial({
          // 基于Cocos Creator材质的默认配置
          color: new THREE.Color(0xffffff), // 白色，让纹理颜色显示
          roughness: 0.8,
          metalness: 0.1,
          side: THREE.DoubleSide
        });

        child.material = material;
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    console.log('🎨 八字模型材质配置完成');
  }

  // Getter方法
  getModel(): THREE.Group | null {
    return this.isLoaded ? this.model : null;
  }

  getPosition(): THREE.Vector3 {
    return this.model ? this.model.position.clone() : new THREE.Vector3();
  }

  getRotation(): THREE.Euler {
    return this.model ? this.model.rotation.clone() : new THREE.Euler();
  }

  getScale(): number {
    return this.model ? this.model.scale.x : 1.0;
  }

  isModelLoaded(): boolean {
    return this.isLoaded;
  }

  // Setter方法
  setPosition(position: THREE.Vector3): void {
    if (this.model) {
      this.model.position.copy(position);
    }
  }

  setRotation(rotation: THREE.Euler): void {
    if (this.model) {
      this.model.rotation.copy(rotation);
    }
  }

  setScale(scale: number): void {
    if (this.model) {
      this.model.scale.setScalar(scale);
    }
  }

  setMaterialProperties(properties: { 
    roughness?: number; 
    metalness?: number; 
    color?: THREE.Color 
  }): void {
    if (!this.model) return;

    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
        if (properties.roughness !== undefined) {
          child.material.roughness = properties.roughness;
        }
        if (properties.metalness !== undefined) {
          child.material.metalness = properties.metalness;
        }
        if (properties.color) {
          child.material.color.copy(properties.color);
        }
        child.material.needsUpdate = true;
      }
    });
  }

  // 事件回调设置
  setEventCallbacks(callbacks: {
    onLoadComplete?: () => void;
    onTextureLoaded?: () => void;
  }): void {
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onTextureLoaded = callbacks.onTextureLoaded;
  }

  /**
   * 调试材质信息
   */
  debugMaterials(): void {
    if (!this.model) {
      console.log('❌ 八字模型未加载');
      return;
    }

    console.log('🔍 八字模型材质调试信息:');
    let meshIndex = 0;
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        const material = child.material;
        console.log(`  网格 ${meshIndex}:`, {
          name: child.name,
          materialType: material.constructor.name,
          material: material instanceof THREE.MeshStandardMaterial ? {
            color: material.color.getHexString(),
            roughness: material.roughness,
            metalness: material.metalness,
            map: material.map ? '已加载' : '未加载'
          } : 'Unknown material type'
        });
        meshIndex++;
      }
    });
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.model) {
      this.scene.remove(this.model);
      
      // 清理几何体和材质
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => mat.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    }

    if (this.loader) {
      this.loader.dispose();
    }

    console.log('BaziModel disposed');
  }
}
