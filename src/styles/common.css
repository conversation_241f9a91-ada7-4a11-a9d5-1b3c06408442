/* 公共样式 - 供所有模块页面使用 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow: hidden;
  background-color: #000;
  font-family: 'Arial', 'Helvetica Neue', sans-serif;
  color: white;
}

/* 3D画布样式 */
#app {
  width: 100vw;
  height: 100vh;
  display: block;
}

/* 加载状态样式 */
#loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 18px;
  z-index: 100;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(78, 205, 196, 0.3);
  border-radius: 50%;
  border-top-color: #4ecdc4;
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 信息面板样式 */
#info {
  position: absolute;
  top: 10px;
  left: 10px;
  color: white;
  font-size: 14px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  max-width: 300px;
  backdrop-filter: blur(5px);
}

#info h3 {
  color: #4ecdc4;
  margin-bottom: 10px;
  font-size: 16px;
}

#info div {
  margin-bottom: 5px;
}

.info-section {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  opacity: 0.8;
}

/* 返回按钮样式 */
#back-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  padding: 10px 20px;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-weight: bold;
  transition: all 0.3s ease;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

#back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(78, 205, 196, 0.3);
}

/* 错误消息样式 */
#error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff6b6b;
  font-size: 16px;
  z-index: 100;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
  backdrop-filter: blur(5px);
  display: none;
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  padding: 10px 15px;
  border-radius: 20px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  backdrop-filter: blur(5px);
  font-size: 12px;
}

.status-indicator.loading {
  border-color: rgba(255, 152, 0, 0.3);
  color: #ff9800;
}

.status-indicator.ready {
  border-color: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.status-indicator.error {
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

/* 通用按钮样式 */
.btn {
  padding: 8px 16px;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
}

.btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
}

/* 模块特定的调试面板位置调整 */
.dg.ac {
  z-index: 1000 !important;
}

.dg.main {
  position: fixed !important;
  top: 10px !important;
  right: 320px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #info {
    max-width: 250px;
    font-size: 12px;
    padding: 10px;
  }
  
  #back-button {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .dg.main {
    right: 10px !important;
    width: 250px !important;
  }
}

@media (max-width: 480px) {
  #info {
    position: relative;
    top: auto;
    left: auto;
    margin: 10px;
    max-width: none;
  }
  
  .status-indicator {
    bottom: 10px;
    left: 10px;
    font-size: 10px;
    padding: 8px 12px;
  }
} 