{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505", "files": [], "subMetas": {"73684": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@73684", "displayName": "", "id": "73684", "name": "Cube.012.mesh", "userData": {"gltfIndex": 5, "triangleCount": 215}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "c6e33": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@c6e33", "displayName": "", "id": "c6e33", "name": "Cube.002.mesh", "userData": {"gltfIndex": 0, "triangleCount": 207}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "53dde": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@53dde", "displayName": "", "id": "53dde", "name": "Cube.006.mesh", "userData": {"gltfIndex": 1, "triangleCount": 68}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "c2d4f": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@c2d4f", "displayName": "", "id": "c2d4f", "name": "Cube.009.mesh", "userData": {"gltfIndex": 2, "triangleCount": 110}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "88b23": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@88b23", "displayName": "", "id": "88b23", "name": "Cube.010.mesh", "userData": {"gltfIndex": 3, "triangleCount": 80}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "cf1a7": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@cf1a7", "displayName": "", "id": "cf1a7", "name": "Cube.011.mesh", "userData": {"gltfIndex": 4, "triangleCount": 216}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "1ed8d": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@1ed8d", "displayName": "", "id": "1ed8d", "name": "Cube.015.mesh", "userData": {"gltfIndex": 6, "triangleCount": 88}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "f051b": {"importer": "gltf-mesh", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@f051b", "displayName": "", "id": "f051b", "name": "Cube.016.mesh", "userData": {"gltfIndex": 7, "triangleCount": 88}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "ad76b": {"importer": "gltf-material", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@ad76b", "displayName": "", "id": "ad76b", "name": "M_SS_Rock_01.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "0df82": {"importer": "gltf-scene", "uuid": "26feaaba-4f57-452a-9770-bd3e52b82505@0df82", "displayName": "", "id": "0df82", "name": "SS_Rock_01.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["26feaaba-4f57-452a-9770-bd3e52b82505@c6e33", "26feaaba-4f57-452a-9770-bd3e52b82505@53dde", "26feaaba-4f57-452a-9770-bd3e52b82505@c2d4f", "26feaaba-4f57-452a-9770-bd3e52b82505@88b23", "26feaaba-4f57-452a-9770-bd3e52b82505@cf1a7", "26feaaba-4f57-452a-9770-bd3e52b82505@73684", "26feaaba-4f57-452a-9770-bd3e52b82505@1ed8d", "26feaaba-4f57-452a-9770-bd3e52b82505@f051b"], "skeletons": [], "textures": [], "materials": ["26feaaba-4f57-452a-9770-bd3e52b82505@ad76b"], "scenes": ["26feaaba-4f57-452a-9770-bd3e52b82505@0df82"]}, "materials": {"26feaaba-4f57-452a-9770-bd3e52b82505@ad76b": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "c8f66d17-351a-48da-a12c-0212d28575c4", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{"USE_INSTANCING": true}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"mainColor": {"__type__": "cc.Color", "r": 148, "g": 148, "b": 148, "a": 255}, "roughness": 0.75}, {}, {}, {}, {}, {}]}}, "tangents": 1, "meshCompress": {"enable": true, "compress": true, "quantize": true}, "meshSimplify": {"enable": true, "errorRate": 0.1, "lockBoundary": true, "autoErrorRate": true, "targetRatio": 0.3}}}