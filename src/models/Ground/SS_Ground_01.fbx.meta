{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "14d8049d-0727-4894-ac08-110b2a481403", "files": [], "subMetas": {"29428": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@29428", "displayName": "", "id": "29428", "name": "Plane.012.mesh", "userData": {"gltfIndex": 2, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "94519": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@94519", "displayName": "", "id": "94519", "name": "Plane.018.mesh", "userData": {"gltfIndex": 8, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "57ecb": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@57ecb", "displayName": "", "id": "57ecb", "name": "Plane.010.mesh", "userData": {"gltfIndex": 0, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "38a9f": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@38a9f", "displayName": "", "id": "38a9f", "name": "Plane.011.mesh", "userData": {"gltfIndex": 1, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "9d07f": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@9d07f", "displayName": "", "id": "9d07f", "name": "Plane.013.mesh", "userData": {"gltfIndex": 3, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "32aeb": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@32aeb", "displayName": "", "id": "32aeb", "name": "Plane.014.mesh", "userData": {"gltfIndex": 4, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "f702a": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@f702a", "displayName": "", "id": "f702a", "name": "Plane.015.mesh", "userData": {"gltfIndex": 5, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "591ce": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@591ce", "displayName": "", "id": "591ce", "name": "Plane.016.mesh", "userData": {"gltfIndex": 6, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "723e8": {"importer": "gltf-mesh", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@723e8", "displayName": "", "id": "723e8", "name": "Plane.017.mesh", "userData": {"gltfIndex": 7, "triangleCount": 10}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "d6ca6": {"importer": "gltf-scene", "uuid": "14d8049d-0727-4894-ac08-110b2a481403@d6ca6", "displayName": "", "id": "d6ca6", "name": "SS_Ground_01.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["14d8049d-0727-4894-ac08-110b2a481403@57ecb", "14d8049d-0727-4894-ac08-110b2a481403@38a9f", "14d8049d-0727-4894-ac08-110b2a481403@29428", "14d8049d-0727-4894-ac08-110b2a481403@9d07f", "14d8049d-0727-4894-ac08-110b2a481403@32aeb", "14d8049d-0727-4894-ac08-110b2a481403@f702a", "14d8049d-0727-4894-ac08-110b2a481403@591ce", "14d8049d-0727-4894-ac08-110b2a481403@723e8", "14d8049d-0727-4894-ac08-110b2a481403@94519"], "skeletons": [], "textures": [], "materials": [], "scenes": ["14d8049d-0727-4894-ac08-110b2a481403@d6ca6"]}, "tangents": 1, "meshCompress": {"enable": true, "compress": true, "quantize": false}, "meshSimplify": {"lockBoundary": true, "autoErrorRate": true, "errorRate": 0.1, "targetRatio": 0.1, "enable": true}, "legacyFbxImporter": false, "allowMeshDataAccess": true, "addVertexColor": false, "generateLightmapUVNode": false, "meshOptimizer": {"enable": false, "algorithm": "simplify", "simplifyOptions": {"targetRatio": 1, "enableSmartLink": true, "agressiveness": 7, "maxIterationCount": 100}}}}