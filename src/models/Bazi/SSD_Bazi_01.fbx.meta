{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "d743e5d8-e60e-4061-b99d-e7ec82ff638c", "files": [], "subMetas": {"e2349": {"importer": "gltf-mesh", "uuid": "d743e5d8-e60e-4061-b99d-e7ec82ff638c@e2349", "displayName": "", "id": "e2349", "name": "Mesh.mesh", "userData": {"gltfIndex": 0, "triangleCount": 836}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "a95a9": {"importer": "gltf-material", "uuid": "d743e5d8-e60e-4061-b99d-e7ec82ff638c@a95a9", "displayName": "", "id": "a95a9", "name": "M_SSD_Bazi_01.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "cc623": {"importer": "gltf-scene", "uuid": "d743e5d8-e60e-4061-b99d-e7ec82ff638c@cc623", "displayName": "", "id": "cc623", "name": "SSD_Bazi_01.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["d743e5d8-e60e-4061-b99d-e7ec82ff638c@e2349"], "skeletons": [], "textures": [], "materials": ["d743e5d8-e60e-4061-b99d-e7ec82ff638c@a95a9"], "scenes": ["d743e5d8-e60e-4061-b99d-e7ec82ff638c@cc623"]}, "materials": {"d743e5d8-e60e-4061-b99d-e7ec82ff638c@a95a9": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "e643cf22-8ba2-4e11-ad93-dff7503664f3", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{}, {"USE_ALBEDO_MAP": true}, {}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{}, {"mainTexture": {"__uuid__": "4f8a48e6-b3b9-45fb-b40a-edbae8dbc091@6c48a", "__expectedType__": "cc.Texture2D"}}, {}, {}, {}, {}, {}, {}]}}, "tangents": 1, "meshCompress": {"enable": true, "compress": true, "quantize": true}, "meshSimplify": {"enable": true, "errorRate": 0.1, "lockBoundary": true, "targetRatio": 0.8, "autoErrorRate": true}}}