/**
 * 页面管理器 - 处理多页面应用的通信和状态管理
 */

export interface PageMessage {
  type: string;
  data?: any;
  timestamp?: number;
}

export class PageManager {
  private static instance: PageManager;
  private currentPage: string;
  private messageHandlers: Map<string, Function[]> = new Map();

  private constructor() {
    this.currentPage = this.getCurrentPageName();
    this.setupMessageListener();
  }

  public static getInstance(): PageManager {
    if (!PageManager.instance) {
      PageManager.instance = new PageManager();
    }
    return PageManager.instance;
  }

  /**
   * 获取当前页面名称
   */
  private getCurrentPageName(): string {
    const path = window.location.pathname;
    if (path === '/' || path === '/index.html') {
      return 'main';
    }
    
    const match = path.match(/\/pages\/(.+)\.html$/);
    return match ? match[1] : 'unknown';
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    window.addEventListener('message', (event) => {
      this.handleMessage(event.data);
    });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: PageMessage): void {
    if (!message.type) return;

    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.data);
        } catch (error) {
          console.error(`Error handling message ${message.type}:`, error);
        }
      });
    }
  }

  /**
   * 发送消息到父窗口或其他窗口
   */
  public sendMessage(type: string, data?: any): void {
    const message: PageMessage = {
      type,
      data,
      timestamp: Date.now()
    };

    // 发送到父窗口
    if (window.parent && window.parent !== window) {
      window.parent.postMessage(message, '*');
    }

    // 发送到当前窗口（用于同页面组件通信）
    window.postMessage(message, '*');
  }

  /**
   * 订阅消息
   */
  public on(messageType: string, handler: Function): void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }
    this.messageHandlers.get(messageType)!.push(handler);
  }

  /**
   * 取消订阅消息
   */
  public off(messageType: string, handler: Function): void {
    const handlers = this.messageHandlers.get(messageType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 清除所有消息处理器
   */
  public clearHandlers(): void {
    this.messageHandlers.clear();
  }

  /**
   * 获取当前页面信息
   */
  public getCurrentPage(): string {
    return this.currentPage;
  }

  /**
   * 页面加载完成通知
   */
  public notifyPageLoaded(): void {
    this.sendMessage('page-loaded', {
      page: this.currentPage,
      timestamp: Date.now()
    });
  }

  /**
   * 页面错误通知
   */
  public notifyPageError(error: string): void {
    this.sendMessage('page-error', {
      page: this.currentPage,
      error,
      timestamp: Date.now()
    });
  }

  /**
   * 模块加载状态通知
   */
  public notifyModuleStatus(moduleName: string, status: 'loading' | 'loaded' | 'error', message?: string): void {
    this.sendMessage('module-status', {
      module: moduleName,
      status,
      message,
      page: this.currentPage,
      timestamp: Date.now()
    });

    // 更新页面状态指示器
    this.updateStatusIndicator(status, message);
  }

  /**
   * 更新状态指示器
   */
  private updateStatusIndicator(status: string, message?: string): void {
    let indicator = document.getElementById('status-indicator');
    
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'status-indicator';
      indicator.className = 'status-indicator';
      document.body.appendChild(indicator);
    }

    indicator.className = `status-indicator ${status}`;
    indicator.textContent = message || this.getStatusText(status);
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    switch (status) {
      case 'loading':
        return '加载中...';
      case 'loaded':
        return '就绪';
      case 'error':
        return '错误';
      default:
        return '未知状态';
    }
  }

  /**
   * 显示加载状态
   */
  public showLoading(message = '加载中...'): void {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.display = 'block';
      const textElement = loading.querySelector('div:last-child');
      if (textElement) {
        textElement.textContent = message;
      }
    }
  }

  /**
   * 隐藏加载状态
   */
  public hideLoading(): void {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.display = 'none';
    }
  }

  /**
   * 显示错误消息
   */
  public showError(message: string): void {
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
      const messageElement = errorDiv.querySelector('p');
      if (messageElement) {
        messageElement.textContent = message;
      }
      errorDiv.style.display = 'block';
    }
    this.hideLoading();
  }

  /**
   * 隐藏错误消息
   */
  public hideError(): void {
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
      errorDiv.style.display = 'none';
    }
  }
}

// 创建全局实例
export const pageManager = PageManager.getInstance(); 